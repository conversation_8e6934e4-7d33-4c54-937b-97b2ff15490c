# Android 13 权限更新指南

## 概述

Android 13 (API 33) 引入了新的权限模型，特别是对存储权限进行了重大更改。本文档说明了如何更新应用以兼容 Android 13 的新权限系统。

## 主要变化

### 1. 存储权限的变化

**Android 12 及以下:**
- `READ_EXTERNAL_STORAGE` - 读取外部存储
- `WRITE_EXTERNAL_STORAGE` - 写入外部存储

**Android 13+:**
- `READ_MEDIA_IMAGES` - 读取图片文件
- `READ_MEDIA_VIDEO` - 读取视频文件  
- `READ_MEDIA_AUDIO` - 读取音频文件

### 2. 权限策略

我们的应用现在根据 Android 版本使用不同的权限策略：

- **Android 13+ (API 33+)**: 使用新的媒体权限
- **Android 11-12 (API 30-32)**: 使用 scoped storage 和传统权限的混合
- **Android 10 及以下**: 使用传统的存储权限
- **iOS**: 使用照片库权限

## 实现详情

### 1. AndroidManifest.xml 更新

```xml
<!-- 传统存储权限 (Android 10及以下) -->
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" 
    android:maxSdkVersion="32" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" 
    android:maxSdkVersion="29" />

<!-- Android 13+ 媒体权限 -->
<uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
<uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
<uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
```

### 2. pubspec.yaml 更新

```yaml
dependencies:
  permission_handler: ^12.0.1  # 支持 Android 13 权限
```

### 3. 代码实现

#### 主要权限检查方法 (login.dart)

```dart
void checkPermission() async {
  if (Platform.isAndroid) {
    final deviceInfo = await DeviceInfoPlugin().androidInfo;
    final androidVersion = deviceInfo.version.sdkInt ?? 0;
    
    if (androidVersion >= 33) {
      // Android 13+ - 使用新的媒体权限
      await _checkAndroid13Permissions();
    } else if (androidVersion >= 30) {
      // Android 11-12 - 混合权限模式
      await _checkAndroid11Permissions();
    } else {
      // Android 10及以下 - 传统权限
      await _checkLegacyStoragePermissions();
    }
  } else if (Platform.isIOS) {
    await _checkIOSPermissions();
  }
}
```

#### Android 13+ 权限处理

```dart
Future<void> _checkAndroid13Permissions() async {
  List<Permission> permissionsToRequest = [];
  
  final photoStatus = await Permission.photos.status;
  final videoStatus = await Permission.videos.status;
  
  if (!photoStatus.isGranted) {
    permissionsToRequest.add(Permission.photos);
  }
  if (!videoStatus.isGranted) {
    permissionsToRequest.add(Permission.videos);
  }
  
  if (permissionsToRequest.isNotEmpty) {
    await _requestPermissions(permissionsToRequest);
  }
}
```

### 4. 权限工具类

创建了 `PermissionHelper` 类来简化权限管理：

```dart
// 检查并请求存储权限
bool hasPermission = await PermissionHelper.checkAndRequestStoragePermissions();

// 显示权限解释对话框
PermissionHelper.showPermissionExplanationDialog(context, Permission.photos, () {
  // 重试逻辑
});
```

## 使用指南

### 1. 基本权限检查

```dart
import 'package:your_app/utils/permission_helper.dart';

// 在需要访问媒体文件之前调用
bool hasPermission = await PermissionHelper.checkAndRequestStoragePermissions();
if (hasPermission) {
  // 可以访问媒体文件
} else {
  // 权限被拒绝，显示说明或引导用户到设置
}
```

### 2. 处理权限拒绝

```dart
if (!hasPermission) {
  final status = await Permission.photos.status;
  if (status.isPermanentlyDenied) {
    // 显示设置引导对话框
    PermissionHelper.showPermissionSettingsDialog(context, Permission.photos);
  } else {
    // 显示权限解释对话框
    PermissionHelper.showPermissionExplanationDialog(context, Permission.photos, () {
      // 重新请求权限
      PermissionHelper.checkAndRequestStoragePermissions();
    });
  }
}
```

## 测试建议

### 1. 测试不同 Android 版本

- Android 10 及以下：测试传统存储权限
- Android 11-12：测试 scoped storage 行为
- Android 13+：测试新的媒体权限

### 2. 测试权限场景

- 首次安装应用
- 权限被拒绝
- 权限被永久拒绝
- 从设置中手动授予权限

### 3. 功能测试

- 图片选择和上传
- 文件下载和保存
- 媒体文件访问

## 注意事项

1. **MANAGE_EXTERNAL_STORAGE 权限**: 这是一个敏感权限，只有文件管理器等特殊应用才需要。大多数应用应该避免使用。

2. **向后兼容性**: 代码保持了对旧版本 Android 的兼容性。

3. **权限说明**: 始终向用户解释为什么需要特定权限。

4. **渐进式权限请求**: 只在需要时请求权限，而不是在应用启动时请求所有权限。

## 故障排除

### 常见问题

1. **权限总是返回拒绝**: 检查 AndroidManifest.xml 中是否正确声明了权限。

2. **在 Android 13 上无法访问文件**: 确保使用了正确的媒体权限而不是传统的存储权限。

3. **权限对话框不显示**: 检查 permission_handler 版本是否支持 Android 13。

### 调试技巧

1. 使用 `adb shell dumpsys package <package_name>` 查看应用权限状态
2. 在不同 Android 版本的设备/模拟器上测试
3. 检查系统日志中的权限相关错误

## 更新日志

- **v2.5.0**: 添加 Android 13 权限支持
- 更新 permission_handler 到 12.0.1
- 添加 PermissionHelper 工具类
- 更新 AndroidManifest.xml 权限声明

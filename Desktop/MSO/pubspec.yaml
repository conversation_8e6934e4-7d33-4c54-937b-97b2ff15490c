name: amkor
description: MSO

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 2.5.0+1

environment:
  sdk: ">=3.2.0 <4.0.0"
  flutter: ">=3.24.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  # 基础UI组件
  cupertino_icons: ^1.0.8
  fluttertoast: ^8.2.8
  awesome_dialog: ^3.2.1  # 替代 flutter_animated_dialog，兼容 Flutter 3.24
  # 使用 Flutter 内置组件替代不兼容的第三方包
  flutter_easyloading: ^3.0.5
  # outline_search_bar: ^2.3.0  # 使用 Flutter 内置 SearchBar 替代
  pull_to_refresh: ^2.0.0
  flutter_staggered_animations: ^1.1.1
  photo_view: ^0.15.0
  badges: ^3.1.2

  # 网络和存储
  dio: ^5.4.3+1
  http: ^1.2.1
  get_storage: ^2.1.1
  sqflite: ^2.3.3+1
  web_socket_channel: ^3.0.0
  mqtt_client: ^10.2.0

  # 加密和安全
  crypto: ^3.0.3
  encrypt: ^5.0.3
  # secure_application: ^3.8.0  # 暂时禁用，存在兼容性问题
  # flutter_windowmanager: ^0.2.0  # 暂时禁用，存在编译问题
  screen_protector: ^1.4.2  # 新增：更现代的防截屏插件

  # 设备信息和权限
  device_info_plus: ^10.1.0
  permission_handler: ^11.3.1
  flutter_secure_storage: ^9.2.2  # 替代 flutter_keychain，更好的 Flutter 3.24 兼容性
  screen_brightness: ^1.0.1
  flutter_udid: ^4.0.0

  # 文件和媒体处理
  image_picker: ^1.0.8  # 降级到兼容版本
  flutter_image_compress: ^2.3.0
  path_provider: ^2.1.3
  open_file: ^3.3.2

  # WebView和HTML
  webview_flutter: ^4.8.0
  universal_html: ^2.2.4

  # 二维码功能
  qr_flutter: ^4.1.0
  mobile_scanner: ^5.1.1

  # 导航和URL
  url_launcher: ^6.3.0
  get: ^4.6.6
  external_app_launcher: ^4.0.3

  # 输入组件 - 使用Flutter内置日期选择器替代date_time_picker
  pinput: ^5.0.0
  wheel_picker: ^0.2.2  # 替代 flutter_picker，兼容 Flutter 3.24

  # 推送和通知
  # jpush_flutter: ^2.3.4
  flutter_local_notifications: ^17.2.1+2

  # 工具包
  package_info_plus: ^8.0.0
  flutter_launcher_icons: ^0.13.1


#  screenshot_callback: ^2.0.0
#  screenshot: ^1.3.0
dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.0

# 强制依赖版本解析
dependency_overrides:
  flutter_plugin_android_lifecycle: ^2.0.20
  image_picker_android: ^0.8.12  # 使用兼容 Flutter 3.24 的版本

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  assets:
    - assets/images/
    - assets/icons/
    - assets/icons/nav/
    - assets/icons/news/
  fonts:
    - family: MyIcons
      fonts:
        - asset: assets/fonts/icomoon.ttf
          weight: 600
  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

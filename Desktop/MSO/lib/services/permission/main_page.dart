
List<dynamic> homePage = [
  ["扫一扫","sys","/uploadPosition"],
  ["付款码","jc","/KongPage"],
  ["钱包","qb","报表||","true","HR"],
  ["员工码","gp","/showPassportCode"]
];

List<dynamic> supplier=[
  // ["劳勤","lq.png","URL||alipays://platformapi/startapp?appId=2021001132656455","true","第三方应用"],
  // ["Teams","Teams.png","URL||msteams://teams.microsoft.com/||com.microsoft.teams","true","第三方应用"],
  // "Outlook", "E-mobile7", "公司菜单"
];

String roles = "0";

Future<void> complexDataRemoveAt(List<dynamic> data,dynamic removeData) async {
  for(int i=0;i<data.length;i++){
    if(data[i][0]==removeData && removeData != '') {
      data.removeAt(i);
    }
  }
}
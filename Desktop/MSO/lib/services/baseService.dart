import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:amkor/login.dart';
import 'package:amkor/utils/gKey.dart';
import 'package:flutter/material.dart';
import 'package:amkor/model/apiResponse.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get_storage/get_storage.dart';

class GlobalHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    // TODO: implement createHttpClient
    return super.createHttpClient(context)
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}

class BaseService {
  late final String url;
  String controller = "";

  ///判断测试站还是正式站
  String judge = "";
  BaseService(this.controller) {
    if (kDebugMode) {
      //模拟器
      // 测试站
      judge = "Test API";
      url = "https://mobileappt01.amkor.com.cn:4433/api"; // 测试
      // url = "http://************:8080/api"; // Jim 设备绑定
      // url = "http://************:5283/api"; // Justin 设备绑定
      // url = "https://mobileappt01.amkor.com.cn:4433/api_web";  //测试设备绑定

      // url = "https://mobileappp01.amkor.com.cn/api";

      //DMZ 测试
      //url = "https://msodmztest.amkor.com.cn:4433/api";
      // url = "https://msodmztest.amkor.com.cn/api";

      // url = "http://***********/api"; //迁移SQL test
      // url = "https://atcmsoapi.amkor.com/api";  // 短信
      // url = "http://************:8088/api";  // ATC员工和供应商区分权限
      // url = "http://***********:8088/api";
    } else {
      //真机测试
      judge = "Test API";
      url = "https://mobileappt01.amkor.com.cn:4433/api"; // 测试

      //正式站
      // judge="";
      // url = "https://mobileappp01.amkor.com.cn/api"; //正式站
      // url = "https://mobileappt01.amkor.com.cn:4433/api_web";  //测试设备绑定
    }
  }

  Future<ApiResponse?> get(String action, {dynamic queryParams}) async {
    try {
      var token = GetStorage().read("token");
      print("token is bearer $token");
      var dio = Dio();
      dio.options.contentType = "application/json";
      dio.options.method = "GET";
      // dio.interceptors.add(LogInterceptor(responseBody: true)); //开启请求日志
      dio.options.headers["Authorization"] = "bearer $token";
      var u = "$url/$controller/$action";
      print("get: $u \n data is $queryParams");
      final Response<Map<String, dynamic>> response = await dio.get(
        u,
        queryParameters: queryParams,
        options: Options(responseType: ResponseType.plain),
      );
      if (response.statusCode == HttpStatus.ok ||
          response.statusCode == HttpStatus.created) {
        if (response.data == null) return null;
        print("$action response is ${response.data}");
        var r = ApiResponse.fromJson(response.data!);

        return r;
      }
    } on DioException catch (e) {
      print("$action response failed. $e");
      if (e.response?.statusCode == HttpStatus.unauthorized) {
        Fluttertoast.showToast(msg: "登录已过期，请重新登录");
        GlobalKeys.navigatorKey.currentState!.pushAndRemoveUntil(
            MaterialPageRoute(
                builder: (BuildContext context) => const LoginPage()),
            (route) => false);
      }
      return null;
    } catch (e) {
      print("$action response failed. ${e.runtimeType} $e");
      return null;
    }
    return null;
  }

  Future<ApiResponse?> post(String action, {dynamic body}) async {
    try {
      var token = GetStorage().read("token");
      print("token is bearer $token");
      var dio = Dio();
      dio.options.headers["Authorization"] = "bearer $token";
      dio.options.headers["content-type"] = "application/json";
      // dio.options.responseType = ResponseType.json;
      dio.options.method = "post";
      dio.options.sendTimeout = const Duration(seconds: 30);
      // dio.interceptors.add(LogInterceptor(responseBody: false)); //开启请求日志
      var u = "$url/$controller/$action";
      var j = jsonEncode(body);
      print("post: $u \n data is $j ");

      final Response<Map<String, dynamic>> response = await dio.post(
        u,
        data: body,
        options: Options(responseType: ResponseType.plain),
      );

      if (response.statusCode == HttpStatus.ok ||
          response.statusCode == HttpStatus.created) {
        if (response.data == null) return null;
        var r = ApiResponse.fromJson(response.data!);
        print("$action response is ${response.data}");
        return r;
      }
      print("$action response failed");

      return null;
    } on DioError catch (e) {
      print("$action response failed. $e");
      if (e.response?.statusCode == HttpStatus.unauthorized) {
        Fluttertoast.showToast(msg: "登录已过期，请重新登录");
        // Navigator.pushAndRemoveUntil(, newRoute, (route) => false)
        GlobalKeys.navigatorKey.currentState!.pushAndRemoveUntil(
            MaterialPageRoute(
                builder: (BuildContext context) => const LoginPage()),
            (route) => false);
      }
      return null;
    } catch (e) {
      print("$action response failed. ${e.runtimeType} $e");
      return null;
    }
  }

  ///公告
  Future<ApiResponsegg?> postgg(String action, {dynamic body}) async {
    try {
      var token = GetStorage().read("token");
      print("token is bearer $token");
      var dio = Dio();
      dio.options.headers["Authorization"] = "bearer $token";
      dio.options.headers["content-type"] = "application/json";
      // dio.options.responseType = ResponseType.json;
      dio.options.method = "post";
      dio.options.sendTimeout = const Duration(seconds: 30);
      // dio.interceptors.add(LogInterceptor(responseBody: false)); //开启请求日志
      var u = "$url/$controller/$action";
      var j = jsonEncode(body);
      print("post: $u \n data is $j ");

      final Response<Map<String, dynamic>> response = await dio.post(
        u,
        data: body,
        options: Options(responseType: ResponseType.plain),
      );

      if (response.statusCode == HttpStatus.ok ||
          response.statusCode == HttpStatus.created) {
        if (response.data == null) return null;
        var r = ApiResponsegg.fromJson(response.data!);
        print("$action response is ${response.data}");
        return r;
      }
      print("$action response failed");

      return null;
    } on DioException catch (e) {
      print("$action response failed. $e");
      if (e.response?.statusCode == HttpStatus.unauthorized) {
        Fluttertoast.showToast(msg: "登录已过期，请重新登录");
        // Navigator.pushAndRemoveUntil(, newRoute, (route) => false)
        GlobalKeys.navigatorKey.currentState!.pushAndRemoveUntil(
            MaterialPageRoute(
                builder: (BuildContext context) => const LoginPage()),
            (route) => false);
      }
      return null;
    } catch (e) {
      print("$action response failed. ${e.runtimeType} $e");
      return null;
    }
  }

  //公司菜单 主页头图片
  Future<Response<Uint8List>> post_cd(String action, {dynamic body}) async {
    var dio = Dio();
    dio.options.headers["content-type"] = "image/jpeg";
    dio.options.responseType = ResponseType.json;
    dio.options.method = "post";
    dio.options.sendTimeout = const Duration(seconds: 30);
    var u = "$url/$controller/$action";
    final Response<Uint8List> response = await dio.post(
      u,
      data: body,
      options: Options(responseType: ResponseType.bytes),
    );
    return response;
  }

  Future<dynamic> postWithT<T>(String action, {dynamic body}) async {
    var r = await this.post(action, body: body);
    if (r == null) {
      Fluttertoast.showToast(msg: "网络错误，请稍后再试");
      return null;
    }
    if (!r.success) {
      Fluttertoast.showToast(msg: r.message);
      return null;
    }
    return r.data;
  }

  Future<ApiResponse?> uploadFile(
      String action, Map<String, dynamic> body) async {
    try {
      var token = GetStorage().read("token");
      print("token is bearer $token");
      var dio = Dio();
      dio.options.headers["Authorization"] = "bearer $token";
      var formData = FormData.fromMap(body);
      var u = "$url/$controller/$action";
      var json = "{";
      body.forEach((key, value) {
        json += '"$key":"$value",';
      });
      json += "}";
      print("post: $u \n  data is $json");
      //   {
      //   'file': await MultipartFile.fromFile('./text.txt', filename: 'upload.txt')
      // }
      final Response<Map<String, dynamic>> response = await dio.post(
        u,
        data: formData,
      );
      if (response.statusCode == HttpStatus.ok ||
          response.statusCode == HttpStatus.created) {
        if (response.data == null) return null;
        var r = ApiResponse.fromJson(response.data!);
        print("$action response is ${response.data}");
        return r;
      }
      print("$action response failed");

      return null;
    } on DioException catch (e) {
      print("$action response failed. $e");
      if (e.response?.statusCode == HttpStatus.unauthorized) {
        Fluttertoast.showToast(msg: "登录已过期，请重新登录");
        // Navigator.pushAndRemoveUntil(, newRoute, (route) => false)
        GlobalKeys.navigatorKey.currentState!.pushAndRemoveUntil(
            MaterialPageRoute(
                builder: (BuildContext context) => const LoginPage()),
            (route) => false);
      }
      return null;
    } catch (e) {
      print("$action response failed. ${e.runtimeType} $e");
      return null;
    }
  }
}

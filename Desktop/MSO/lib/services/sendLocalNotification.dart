import 'dart:async';
import 'package:amkor/Notice_xq.dart';
import 'package:amkor/utils/gKey.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/src/date_time.dart';

class LocalNotification {
  late FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin;
  // 使用单例模式进行初始化
  static final LocalNotification _instance = LocalNotification._internal();
  factory LocalNotification() => _instance;
  LocalNotification._internal();

  // 初始化函数
  Future<void> initialize() async {
    flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    var initializationSettingsIOS =
        // DarwinInitializationSettings();
        DarwinInitializationSettings(
            onDidReceiveLocalNotification: onDidReceiveLocalNotification);
    var initializationSettings = InitializationSettings(
        android: initializationSettingsAndroid, iOS: initializationSettingsIOS);
    flutterLocalNotificationsPlugin.initialize(initializationSettings,
        onDidReceiveNotificationResponse: onDidReceiveNotificationResponse);

    ///在Android 13或更高版本上请求权限
    flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.requestNotificationsPermission();
  }

  void onDidReceiveLocalNotification(
      int id, String? title, String? body, String? payload) async {
    // display a dialog with the notification details, tap ok to go to another page
    showDialog(
      context: GlobalKeys.currentContext!,
      builder: (BuildContext context) => CupertinoAlertDialog(
        title: Text(title ?? ''),
        content: Text(body ?? ''),
        actions: [
          CupertinoDialogAction(
            isDefaultAction: true,
            child: Text('Ok'),
            onPressed: () async {
              Navigator.of(context, rootNavigator: true).pop();
              await Navigator.push(
                  context,
                  MaterialPageRoute<void>(
                    builder: (context) => NoticeXqPage(id: payload),
                  ));
            },
          )
        ],
      ),
    );
  }

  void onDidReceiveNotificationResponse(
      NotificationResponse notificationResponse) async {
    final String? payload = notificationResponse.payload;
    if (notificationResponse.payload != null) {
      debugPrint('notification payload: $payload');
    }
    await Navigator.push(
        GlobalKeys.currentContext!,
        MaterialPageRoute<void>(
          builder: (context) => NoticeXqPage(id: payload),
        ));
  }

  //通道1有前台弹窗有声音
  Future<void> showNotification(
      {required int id,
      required String title,
      required String body,
      String? payload,
      TZDateTime? scheduledDate,
      String channelId = 'default',
      String channelName = 'Default Channel',
      String channelDescription = '有前台弹窗有声音',
      bool playSound = true,
      bool vibrate = true,
      bool useChronometer = false,
      bool onlyAlertOnce = false,
      bool autoCancel = true,
      bool ongoing = false}) async {
    var androidPlatformChannelSpecifics = AndroidNotificationDetails(
      channelId,
      channelName,
      importance: Importance.max,
      priority: Priority.high,
      //声音
      playSound: playSound,
      //震动
      enableVibration: vibrate,
      //控制前台弹窗
      onlyAlertOnce: onlyAlertOnce,
      autoCancel: autoCancel,
      ongoing: ongoing,
      // 如果useChronometer为true，则将“subText”设置为计时器格式：hh:mm:ss
      // 如果useChronometer为false，则将“subText”设置为“timeAgo”格式（例如“5分钟前”）
      usesChronometer: useChronometer,
    );
    var platformChannelSpecifics =
        NotificationDetails(android: androidPlatformChannelSpecifics);

    if (scheduledDate == null) {
      // 立即发送通知
      await flutterLocalNotificationsPlugin
          .show(id, title, body, platformChannelSpecifics, payload: payload);
    } else {
      // 在指定时间发送通知
      await flutterLocalNotificationsPlugin.zonedSchedule(
          id, title, body, scheduledDate, platformChannelSpecifics,
          androidAllowWhileIdle: true,
          uiLocalNotificationDateInterpretation:
              UILocalNotificationDateInterpretation.absoluteTime,
          payload: payload);
    }
  }

  //通道2没有前台弹窗没有声音
  Future<void> showNotification2(
      {required int id,
      required String title,
      required String body,
      String? payload,
      TZDateTime? scheduledDate,
      String channelId = 'default2',
      String channelName = 'Default Channel2',
      String channelDescription = '没有前台弹窗没有声音',
      bool useChronometer = false,
      bool onlyAlertOnce = false,
      bool autoCancel = true,
      bool ongoing = false}) async {
    var androidPlatformChannelSpecifics = AndroidNotificationDetails(
      channelId,
      channelName,
      // channelDescription,
      importance: Importance.min,
      priority: Priority.high,
      //声音
      playSound: false,
      //震动
      enableVibration: false,
      //控制前台弹窗
      onlyAlertOnce: onlyAlertOnce,
      autoCancel: autoCancel,
      ongoing: ongoing,
      // 如果useChronometer为true，则将“subText”设置为计时器格式：hh:mm:ss
      // 如果useChronometer为false，则将“subText”设置为“timeAgo”格式（例如“5分钟前”）
      usesChronometer: useChronometer,
    );
    var platformChannelSpecifics =
        NotificationDetails(android: androidPlatformChannelSpecifics);

    if (scheduledDate == null) {
      // 立即发送通知
      await flutterLocalNotificationsPlugin
          .show(id, title, body, platformChannelSpecifics, payload: payload);
    } else {
      // 在指定时间发送通知
      await flutterLocalNotificationsPlugin.zonedSchedule(
          id, title, body, scheduledDate, platformChannelSpecifics,
          androidAllowWhileIdle: true,
          uiLocalNotificationDateInterpretation:
              UILocalNotificationDateInterpretation.absoluteTime,
          payload: payload);
    }
  }

  //通道3没有前台弹窗有声音
  Future<void> showNotification3(
      {required int id,
      required String title,
      required String body,
      String? payload,
      TZDateTime? scheduledDate,
      String channelId = 'default3',
      String channelName = 'Default Channel3',
      String channelDescription = '没有前台弹窗有声音',
      bool useChronometer = false,
      bool onlyAlertOnce = false,
      bool autoCancel = true,
      bool ongoing = false}) async {
    var androidPlatformChannelSpecifics = AndroidNotificationDetails(
      channelId,
      channelName,
      importance: Importance.defaultImportance,
      priority: Priority.high,
      //声音
      playSound: true,
      //震动
      enableVibration: true,
      //控制前台弹窗
      onlyAlertOnce: onlyAlertOnce,
      autoCancel: autoCancel,
      ongoing: ongoing,
      // 如果useChronometer为true，则将“subText”设置为计时器格式：hh:mm:ss
      // 如果useChronometer为false，则将“subText”设置为“timeAgo”格式（例如“5分钟前”）
      usesChronometer: useChronometer,
    );
    var platformChannelSpecifics =
        NotificationDetails(android: androidPlatformChannelSpecifics);

    if (scheduledDate == null) {
      // 立即发送通知
      await flutterLocalNotificationsPlugin
          .show(id, title, body, platformChannelSpecifics, payload: payload);
    } else {
      // 在指定时间发送通知
      await flutterLocalNotificationsPlugin.zonedSchedule(
          id, title, body, scheduledDate, platformChannelSpecifics,
          androidAllowWhileIdle: true,
          uiLocalNotificationDateInterpretation:
              UILocalNotificationDateInterpretation.absoluteTime,
          payload: payload);
    }
  }

  Future<void> cancelNotification(int id) async {
    await flutterLocalNotificationsPlugin.cancel(id);
  }

  Future<void> cancelAllNotifications() async {
    await flutterLocalNotificationsPlugin.cancelAll();
  }
}

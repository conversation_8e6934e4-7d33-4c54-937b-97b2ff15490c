import 'dart:convert';

import 'package:amkor/services/baseService.dart';
import 'package:amkor/services/dto/updatePwdDto.dart';

import '../model/apiResponse.dart';
import 'dto/loginDto.dart';

class AuthService extends BaseService {
  AuthService() : super("Auth");
  ///登录
  Future<ApiResponse?> login(LoginInputDto d) async {
    var v = await super.post("Login", body: d);
    return v;
  }
  ///web登陆待完成
  Future<ApiResponse?> Weblogin(LoginInputDto d) async {
    var v = await super.post("WapLogin", body: d);
    return v;
  }
///纯web，app未使用
  Future<ApiResponse?> GetVerificationCode() async {
    var v = await super.get("GetVerificationCode");
    return v;
  }

  ///忘记密码并且获取验证码
  Future<ApiResponse?> ResetPassword(String employeeId, String mobile) async {
    var v = await super.post("ResetPassword", body: ChangePassword(employeeId, mobile));
    return v;
  }

  /// 过期短信验证
  Future<ApiResponse?> GetLoginSmsCode(String employeeId, String mobile) async {
    var v = await super.post("GetLoginSmsCode", body: ChangePassword(employeeId, mobile));
    return v;
  }

  /// 短信登陆 SmsLogin
  Future<ApiResponse?> SmsLogin(String employeeId, String mobile, String smsCode, String source, String version, String lang) async {
    var v = await super.post("SmsLogin", body: {
      "employeeId": employeeId,
      "mobile": mobile,
      "smsCode": smsCode,
      "source": source,
      "version": version,
      "lang": lang
    });
    return v;
  }

  /// 设备绑定登陆 DeviceLogin
  Future<ApiResponse?> DeviceLogin(String employeeId, String mobile, String smsCode, String source, String version, String lang, String uuid) async {
    var v = await super.post("DeviceLogin", body: {
      "employeeId": employeeId,
      "mobile": mobile,
      "smsCode": smsCode,
      "source": source,
      "version": version,
      "lang": lang,
      "uuid": uuid
    });
    return v;
  }
}

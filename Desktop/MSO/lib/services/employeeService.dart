import 'dart:typed_data';

import 'package:amkor/model/apiResponse.dart';
import 'package:amkor/services/baseService.dart';
import 'package:amkor/services/dto/uploadAntigenDto.dart';
import 'package:amkor/services/dto/uploadCovidDto.dart';
import 'package:amkor/services/dto/questionnaire.dart';
import 'package:dio/dio.dart';
import 'dto/UpdateEmployeeAddressDto.dart';
import 'dto/updatePwdDto.dart';
import 'dto/locationCodeDto.dart';

class EmployeeService extends BaseService {
  EmployeeService() : super("Employee");
  ///进厂码
  Future<ApiResponse?> GetAdmittanceCode() async {
    return await super.get("GetAdmittanceCode");
  }
  ///离厂码
  Future<ApiResponse?> GetDepartureCode() async {
    return await super.get("GetDepartureCode");
  }
  ///位置码
  Future<ApiResponse?> GetLocationCode() async {
    return await super.get("GetLocationCode");
  }
  ///员工码
  Future<ApiResponse?> GetEmployeeCode() async {
    return await super.get("GetEmployeeCode");
  }
  ///宿舍码
  Future<ApiResponse?> GetDormitoryCode() async {
    return await super.get("GetDormitoryCode");
  }
  Future<ApiResponse?> UpdatePwd(String oldPwd, String newPwd,String validationCode) async {
    return await super
        .post("UpdatePasswordV2", body: UpdatePwdInputDto(oldPwd, newPwd,validationCode));
  }

  Future<ApiResponse?> GetPosition(String locationCode) async {
    return await super
        .post("GetPosition", body: LocationCodeInputDto(locationCode));
  }

  Future<ApiResponse?> PositionReport(String locationCode) async {
    return await super
        .post("PositionReport", body: LocationCodeInputDto(locationCode));
  }

  Future<ApiResponse?> GetLiveTypes() async {
    return await super.get("GetLiveTypes");
  }

  Future<ApiResponse?> GetCurrentAddress() async {
    return await super.get("GetCurrentAddress");
  }

  Future<ApiResponse?> UpdateEmployeeAddress(
      UpdateEmployeeAddressInputDto dto) async {
    return await super.post("UpdateEmployeeAddress", body: dto);
  }
///核酸数据上传
  Future<ApiResponse?> UploadCovid(UploadCovidInputDto dto) async {
    var json = await dto.toJson();
    return await super.uploadFile("UploadCovid", json);
  }
  Future<ApiResponse?> UploadCovidBase64(UploadCovidInputDtoweb dto) async {
    var json = await dto.toJson();
    return await super.uploadFile("UploadCovidBase64", json);
  }
///抗原数据上传
  Future<ApiResponse?> UploadAntigen(UploadAntigenInputDto dto) async {
    var json = await dto.toJson();
    return await super.uploadFile("UploadAntigen", json);
  }
  Future<ApiResponse?> UploadAntigenBase64(UploadAntigenInputDtoweb dto) async {
    var json = await dto.toJson();
    return await super.uploadFile("UploadAntigenBase64", json);
  }
  ///问卷
  Future<ApiResponse?> Getquestionnaire(int id) async {
    return await super.post("GetQAList",body: {"type":id});
  }

  ///短信
  Future<ApiResponse?> GetSmsCode(String password) async {
    return await super.post("GetSmsCode",body: GetSmsCodedata(password));
  }
}
class PgyService extends BaseService {
  PgyService() : super("Pgyer");

  ///蒲公英api
  Future<ApiResponse?> GetPgyer() async {
    return await super.get("AppCheck");
  }

}
class AnnouncementApp extends BaseService {
  AnnouncementApp() : super("AnnouncementApp");
 ///获取公告列表分页
  Future<ApiResponsegg?> PostAnnouncementList( int pageIndex, int pageSize ) async {
    return await super.postgg("GetAppAnnouncementList",body:PostAnnouncement(pageIndex, pageSize) );
  }
  ///查看公告详情
  Future<ApiResponse?> GetAnnouncementDetail( String annId ) async {
    return await super.get("GetAppAnnouncementDetail",queryParams:{
      "id": annId,
    });
  }
  ///最近一条公告消息
  Future<ApiResponse?> GetAppNewAnnouncement() async {
    return await super.get("GetAppNewAnnouncement");
  }
  ///查看最新公告详情
  Future<ApiResponse?> GetAppNewAnnouncementDetail( String annId ) async {
    return await super.get("GetAppNewAnnouncementDetail",queryParams:{
      "id": annId,
    });
  }
  ///

 ///查看重要公告消息列表（未读）
  Future<ApiResponsegg?> GetAppImportantAnnouncementList( int pageIndex, int pageSize ) async {
    return await super.postgg("GetAppImportantAnnouncementList",body:PostAnnouncement(pageIndex, pageSize) );
  }
  ///查看重要公告详情（未读）
  Future<ApiResponse?> GetAppImportantAnnouncementDetail( String annId ) async {
    return await super.get("GetAppImportantAnnouncementDetail",queryParams:{
      "id": annId,
    });
  }

}
class EpidemicPreventionInfoApp extends BaseService {
  EpidemicPreventionInfoApp() : super("EpidemicPreventionInfoApp");

  ///上海疫情风险区域
  Future<ApiResponse?> GetEpidemicPreventionShInfoPage() async {
    return await super.post("GetEpidemicPreventionShInfoPage");
  }
  ///非上海区域省市数据
  Future<ApiResponse?> GetCityDictionary() async {
    return await super.get("GetCityDictionary");
  }
  ///非上海区域省市疫情管理数据
  Future<ApiResponse?> GetEpidemicPreventionFshInfo(String id) async {
    return await super.post("GetEpidemicPreventionFshInfo",body:{"code":id});
  }
}
class QuestionApp extends BaseService {
  QuestionApp() : super("QuestionApp");

  ///常见问题获取列表
  Future<ApiResponse?> GetAppQuestionList() async {
    return await super.post("GetAppQuestionList");
  }
  ///常见问题获取详情
  Future<ApiResponse?> GetAppQuestionDetail(String id) async {
    return await super.get("GetAppQuestionDetail",queryParams:{"id": id,
    });
  }
  ///提问
  Future<ApiResponse?> AskQuestion(String title,int questionId) async {
    return await super.post("AskQuestion",body:{"title": title,
      "questionId": questionId});
  }
}
class BusApp extends BaseService {
  BusApp() : super("BusApp");

  ///获取路线列表
  Future<ApiResponse?> GetBusLines( String search,String busType) async {
    return await super.post("GetBusLines",body:{"search": search,"busType":busType} );
  }
  ///路线详情
  Future<ApiResponse?> GetBusLineDetail(int lineId) async {
    return await super.post("GetBusLineDetail",body:{"lineId": lineId} );
  }

}

// 公司菜单 By leo 2023-04-19
class Dish extends BaseService{
  Dish() : super("Dish");

  Future<ApiResponse?> GetDishIntro( String dishDate,  int dishType) async {
    // http://10.86.72.192:5284/api/Dish/GetDishIntro
    // Future<ApiResponse?> GetAnnouncementDetail( String annId ) async {
    return await super.post("GetDishIntro",body:{
      "dishDate": dishDate,
      "dishType": dishType
    });
  }

  //餐厅菜单主页图片
  Future<Response<Uint8List>> ReadDishHeaderImage() async{
    return await super.post_cd("ReadDishHeaderImage");
  }



  Future<ApiResponse?> GetDishAnnouncementContent() async {
    // http://10.86.72.192:5284/api/Dish/GetDishIntro
    // Future<ApiResponse?> GetAnnouncementDetail( String annId ) async {
    return await super.post("GetDishAnnouncementContent");
  }

}

class HistoryApp extends BaseService {
  HistoryApp() : super("HistoryApp");

  ///ios手机端截屏日志
  Future<ApiResponse?> UploadHistoryCutScreenBase64( String fileBase64,String title) async {
    return await super.uploadFile("UploadHistoryCutScreenBase64",
    {
      "Source": "search",
      "Title":title,
      "File":"File",
      "FileBase64":fileBase64,
      "FileName":"FileName.jpg",
    });
  }
}

class Announcement extends BaseService {
  Announcement() : super("Announcement");
  ///新增编辑公告
  Future<ApiResponse?> addAnnouncement() async {
    return await super.post("AddAnnouncement",body:{
      "id": 166,
      "type": 1,
      "employeeId": "1111",
      "title": "测试的ren3",
      "author": "1111",
      "content": "测试的ren11",
      "cover": "20230726154533337.png",
      "summary": "1111",
      "isTop": true
    });
  }
}
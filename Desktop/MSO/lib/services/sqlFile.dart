import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:collection/collection.dart';
class DatabaseHelper {
  static late Database _database;
  // 获取数据库路径
  static Future<String> _getDatabasePath() async {
    String databasesPath = await getDatabasesPath();
    return join(databasesPath, 'demo.db');
  }
  // 删除数据库文件
  static Future<void> deleteDatabaseFile() async {
    String path = await _getDatabasePath();
    await deleteDatabase(path);
  }
  // 打开数据库文件
  static Future<void> openDatabaseFile() async {
    String path = await _getDatabasePath();
    _database = await openDatabase(path, version: 1,
        onCreate: (Database db, int version) async {
          await db.execute(
              'CREATE TABLE Test (id INTEGER PRIMARY KEY, name TEXT, value INTEGER, num REAL)');
        });
  }
  // 插入记录
  static Future<void> insertRecord() async {
    await _database.transaction((txn) async {
      int id1 = await txn.rawInsert(
          'INSERT INTO Test(name, value, num) VALUES("some name", 1234, 456.789)');
      print('inserted1: $id1');
      int id2 = await txn.rawInsert(
          'INSERT INTO Test(name, value, num) VALUES(?, ?, ?)',
          ['another name', 12345678, 3.1416]);
      print('inserted2: $id2');
    });
  }
  // 更新记录
  static Future<void> updateRecord() async {
    int count = await _database.rawUpdate(
        'UPDATE Test SET name = ?, value = ? WHERE name = ?',
        ['updated name', 9876, 'some name']);
    print('updated: $count');
  }
  // 查询记录
  static Future<List<Map<String, dynamic>>> queryRecords() async {
    List<Map<String, dynamic>> list = await _database.rawQuery('SELECT * FROM Test');
    return list;
  }
  // 预期结果
  static List<Map<String, dynamic>> expectedList = [
    {'name': 'updated name', 'id': 1, 'value': 9876, 'num': 456.789},
    {'name': 'another name', 'id': 2, 'value': 12345678, 'num': 3.1416}
  ];
  // 比较查询结果与预期结果
  static Future<void> compareRecords() async {
    List<Map<String, dynamic>> list = await queryRecords();
    print(list);
    print(expectedList);
    assert(const DeepCollectionEquality().equals(list, expectedList));
  }
  // 计算记录数
  static Future<int> countRecords() async {
    int? count = Sqflite.firstIntValue(await _database.rawQuery('SELECT COUNT(*) FROM Test'));
    return count!;
  }
  // 删除记录
  static Future<void> deleteRecord() async {
    int count = await _database.rawDelete('DELETE FROM Test WHERE name = ?', ['another name']);
    assert(count == 1);
  }
  // 关闭数据库连接
  static Future<void> closeDatabase() async {
    await _database.close();
  }
}
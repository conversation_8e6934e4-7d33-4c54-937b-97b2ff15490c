import 'package:dio/dio.dart';

class UploadAntigenInputDto {
  String? antigenDate;
  String? antigenTime;
  String? antigenResult;
  String? filePath;
  UploadAntigenInputDto(
      {this.antigenTime, this.antigenDate, this.antigenResult, this.filePath});

  Future<Map<String, dynamic>> toJson() async {
    return {
      "antigenDate": antigenDate,
      "antigenTime": antigenTime,
      "antigenResult": antigenResult,
      "file": await MultipartFile.fromFile(filePath!),
    };
  }

  UploadAntigenInputDto clone() {
    var r = UploadAntigenInputDto(
        antigenTime: antigenTime,
        antigenDate: antigenDate,
        antigenResult: antigenResult,
        filePath: filePath

    );
    return r;
  }
}


class UploadAntigenInputDtoweb {
  String? antigenDate;
  String? antigenTime;
  String? antigenResult;
  String? fileBase64;
  String? fileName;
  UploadAntigenInputDtoweb(
      {this.antigenTime, this.antigenDate, this.antigenResult, this.fileBase64,this.fileName});

  Future<Map<String, dynamic>> toJson() async {
    return {
      "antigenDate": antigenDate,
      "antigenTime": antigenTime,
      "antigenResult": antigenResult,
      "fileBase64": fileBase64,
      "fileName": fileName,
    };
  }

  UploadAntigenInputDtoweb clone() {
    var r = UploadAntigenInputDtoweb(
        antigenTime: antigenTime,
        antigenDate: antigenDate,
        antigenResult: antigenResult,
        fileBase64:fileBase64,
        fileName: fileName);
    return r;
  }
}

import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:dio/dio.dart';
// import 'package:http/http.dart'as http;
class UploadCovidInputDto {
  String? testTime;
  String? testResult;
  String? filePath;


  UploadCovidInputDto({this.testTime, this.testResult,this.filePath});

  Future<Map<String, dynamic>> toJson() async {
    return {
      "testTime": testTime,
      "testResult": testResult,
      "file": await MultipartFile.fromFile(filePath!),
      // "file": await http.MultipartFile.fromPath("image",filePath!),
    };
  }

  UploadCovidInputDto clone() {
    var r = UploadCovidInputDto(
        testTime: testTime, testResult: testResult, filePath: filePath);
    return r;
  }


}
class UploadCovidInputDtoweb {
  String? testTime;
  String? testResult;
  String? fileBase64;
  String? fileName;

  UploadCovidInputDtoweb({this.testTime, this.testResult,this.fileBase64,this.fileName});

  Future<Map<String, dynamic>> toJson() async {
    return {
      "testTime": testTime,
      "testResult": testResult,
      "fileBase64": fileBase64,
      "fileName":fileName,
      // "file": await http.MultipartFile.fromPath("image",filePath!),
    };
  }
  UploadCovidInputDtoweb clone() {
    var r = UploadCovidInputDtoweb(
        testTime: testTime, testResult: testResult, fileName:fileName,fileBase64:fileBase64);
    return r;
  }


}

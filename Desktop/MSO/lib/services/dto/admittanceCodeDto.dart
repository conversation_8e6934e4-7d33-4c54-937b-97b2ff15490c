class AdmittanceCodeOutputDto {
  String? employeeId;
  String? employeeName;
  List<int>? barcodeColor;
  String? codeContent;
  String? redCodeMessage;
  String? expireTime;

  Map<String, dynamic> toJson() {
    return {
      "employeeId": this.employeeId,
      "employeeName": this.employeeName,
      "barcodeColor": this.barcodeColor,
      "redCodeMessage": this.redCodeMessage,
      "codeContent": this.codeContent,
      "expireTime": this.expireTime
    };
  }

  AdmittanceCodeOutputDto.fromJson(Map<String, dynamic> json)
      : employeeId = json["employeeId"],
        employeeName = json["employeeName"],
        redCodeMessage = json["redCodeMessage"],
        barcodeColor = List<int>.from(json["barcodeColor"]),
        codeContent = json["codeContent"],
        expireTime = json["expireTime"];
}

class GetLiveTypesOutputDto {
  String unitCode = "";
  String unitName = "";
  String cscText = "";
  String detailAddress = "";
  String liveTypeName = "";
  String liveTypeKey = "";


  String addressAlias="";
  String buildingNo="";
  String roomNo="";


  GetLiveTypesOutputDto.from(Map<String, dynamic> json)
      : unitName = json['unitName'] ?? "",
        unitCode = json['unitCode'] ?? "",
        detailAddress = json['detailAddress'] ?? "",
        liveTypeName = json['liveTypeName'] ?? "",
        liveTypeKey = json['liveTypeKey'] ?? "",
        cscText = json['cscText'] ?? ""
  ,
        addressAlias = json['addressAlias'] ?? "",
        buildingNo = json['buildingNo'] ?? "",
        roomNo = json['roomNo'] ?? ""
  ;

  static List<GetLiveTypesOutputDto> fromArray(dynamic list) {
    List<GetLiveTypesOutputDto> r = [];
    var input = List<dynamic>.from(list);
    input.forEach((element) =>
        {r.add(GetLiveTypesOutputDto.from(element as Map<String, dynamic>))});
    return r;
  }
}

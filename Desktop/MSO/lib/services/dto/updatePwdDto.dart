class UpdatePwdInputDto {
  String oldPassword;
  String newPassword;
  String validationCode;
  UpdatePwdInputDto(this.oldPassword, this.newPassword,this.validationCode);
  Map<String, dynamic> toJson() {
    return {
      "oldPassword": oldPassword,
      "newPassword": newPassword,
      "validationCode": validationCode,
    };
  }
}
///修改密码
class ChangePassword {
  String? employeeId;
  String? mobile;
  ChangePassword(this.employeeId, this.mobile);
  Map<String, dynamic> toJson() {
    return {
      "employeeId": employeeId,
      "mobile": mobile,
    };
  }
}

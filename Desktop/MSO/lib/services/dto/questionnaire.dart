// class UpdateQuestionnaire{
//
//   String pageIndex;
//   String pageSize;
//   UpdateQuestionnaire(this.pageIndex,this.pageSize);
//
//   Map<String, dynamic> toJson() {
//     return {
//       "pageIndex": pageIndex,
//       "pageSize": pageSize,
//     };
//   }
//
// }
class GetSmsCodedata{

  String password;

  GetSmsCodedata(this.password);

  Map<String, dynamic> toJson() {
    return {
      "password": password,
    };
  }

}


///获取公告列表
class PostAnnouncement{

  int pageIndex;
  int pageSize;
  PostAnnouncement(this.pageIndex,this.pageSize);

  Map<String, dynamic> toJson() {
    return {
      "pageIndex": pageIndex,
      "pageSize": pageSize,
    };
  }

}

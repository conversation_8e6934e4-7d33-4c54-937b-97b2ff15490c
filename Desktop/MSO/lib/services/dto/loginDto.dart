import '../../model/employee.dart';

class LoginInputDto {
  String employeeId;
  String password;
  String? source ;
  String? version;
  String? lang;
  String? uuid;
  LoginInputDto(this.employeeId, this.password,this.source,this.version,this.lang, this.uuid);

  Map<String, dynamic> toJson() {
    return {
      "employeeId": this.employeeId,
      "password": this.password,
      "source": this.source,
      "version": this.version,
      "lang": this.lang,
      "uuid": this.uuid
    };
  }

  LoginInputDto.fromJson(Map<String, dynamic> json)
      : employeeId = json['employeeId'],
        password = json['password'],
        source = json['source'],
        version = json['version'],
        lang=json['lang'],
        uuid=json['uuid'];
}

class LoginOutputDto {
  Employee? employee;
  String? token;

  LoginOutputDto.fromJson(Map<String, dynamic> json)
      : employee = Employee.fromJson(json['employee']),
        token = json['token'];
}

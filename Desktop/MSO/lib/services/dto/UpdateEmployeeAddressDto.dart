class UpdateEmployeeAddressInputDto {
  String? unitCode;
  String? detailAddr;
  String liveType;
  String? addressAlias;
  String? buildingNo;
  String? roomNo;
  UpdateEmployeeAddressInputDto(this.liveType,
      {this.unitCode, this.detailAddr,
      this.roomNo,this.buildingNo,this.addressAlias
      });

  Map<String, dynamic> toJson() {
    return {
      "liveType": liveType,
      "unitCode": unitCode,
      "detailAddr": detailAddr,
      "addressAlias":addressAlias,
      "buildingNo":buildingNo,
      "roomNo":roomNo
    };
  }
}

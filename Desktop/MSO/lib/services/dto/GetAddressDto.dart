// import 'dart:ffi';

class GetAddressDto {
  List<AddressInfo> city = [];
  List<AddressInfo> county = [];
  List<AddressInfo> street = [];
  List<AddressInfo> unit = [];

  GetAddressDto.from(Map<String, dynamic> json) {
    var _city = List<dynamic>.from(json["city"]);
    _city.forEach((element) =>
        {city.add(AddressInfo.from(element as Map<String, dynamic>))});

    var _county = List<dynamic>.from(json["county"]);
    _county.forEach((element) =>
        {county.add(AddressInfo.from(element as Map<String, dynamic>))});

    var _street = List<dynamic>.from(json["street"]);
    _street.forEach((element) =>
        {street.add(AddressInfo.from(element as Map<String, dynamic>))});

    var _unit = List<dynamic>.from(json["unit"]);
    _unit.forEach((element) =>
        {unit.add(AddressInfo.from(element as Map<String, dynamic>))});
  }

  getV() {
    Map<String, dynamic> res = Map<String, dynamic>();
    for (var countyItem in county) {
      Map<String, dynamic> countryDic = Map<String, dynamic>();
      res.addAll(countryDic);
    }
  }
}

class AddressInfo {
  String code = "";
  String name = "";
  AddressInfo({this.code = "", this.name = ""});

  AddressInfo.from(Map<String, dynamic> json)
      : code = json["code"],
        name = json["name"];

  @override
  String toString() {
    return "{'code':'$code','name':'$name'}";
  }
}

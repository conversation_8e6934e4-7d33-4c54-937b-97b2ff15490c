class GetLinesOutputDto {
  String? lineName;
  List<int>? lineColor;

  GetLinesOutputDto.fromJson(Map<String, dynamic> json)
      : lineName = json["lineName"],
        lineColor = List<int>.from(json["lineColor"]);

  static List<GetLinesOutputDto> getLines(List<dynamic> jarr) {
    List<GetLinesOutputDto> l = [];
    jarr.forEach((json) {
      l.add(GetLinesOutputDto.fromJson(json));
    });
    return l;
  }
}

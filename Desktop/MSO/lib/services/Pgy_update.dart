import 'dart:async';
import 'dart:io';
import 'package:amkor/login.dart';
import 'package:amkor/services/employeeService.dart';
import 'package:amkor/services/sendLocalNotification.dart';
import 'package:amkor/utils/apk_installer.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:open_file/open_file.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';

class UpdaterPage extends StatefulWidget {
  final Widget child;
  const UpdaterPage(this.child, {super.key});

  @override
  UpdatePagerState createState() => UpdatePagerState();
}

class UpdatePagerState extends State<UpdaterPage> with WidgetsBindingObserver {
  String? buildload_url, buildVersion, _version;

  double _dPercent = 0.0;
  String _dPercent0 = "0.00";

  /// 用来判断文件是否完成下载并且在本地可以打开
  bool a = false;

  /// 文件打开路径
  File _apkFile = File("Error");

  @override
  void initState() {
    super.initState();
    if (!kIsWeb) {
      PackageInfo.fromPlatform().then((info) {
        setState(() {
          _version = "v${info.version}";
        });
      });
      getData(); // IOS 和 安卓都可以用
    }
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    switch (state) {
      // 处于这种状态的应用程序应该假设它们可能在任何时候暂停。
      case AppLifecycleState.inactive:
        // _judgePlatformInfo();
        break;
      // 应用程序可见，前台
      case AppLifecycleState.resumed:
        //判断平台信息
        apkDownload();
        break;
      // 应用程序不可见，后台
      case AppLifecycleState.paused:
        break;
      // 申请将暂时暂停
      case AppLifecycleState.detached:
        break;
      // 应用程序隐藏状态
      case AppLifecycleState.hidden:
        break;
    }
  }

  ///apk下载
  Future<void> apkDownload() async {
    if (a && _apkFile != File("Error")) {
      a = !a;
      OpenFile.open(_apkFile.path).then((value) {});
    }
  }

  Future<void> getData() async {
    var response = await PgyService().GetPgyer();
    print('Response body: ${response?.data['buildVersion']}');
    buildVersion = response?.data['buildVersion'];
    buildload_url = response?.data['downloadURL'];
    //检查版本更新的版本号
    Future.delayed(const Duration(seconds: 1), () {
      if (buildVersion != null && _version != null) {
        if (Comparable.compare(
                _version.toString().replaceAll("v", ""), buildVersion!) ==
            -1) {
          _showNewVersionAppDialog(); //弹出对话框
        }
      }
    });
  }

  Future<void> _privacyPolicyDialog() {
    return showDialog<void>(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return CupertinoAlertDialog(
            title: Row(
              children: const <Widget>[
                Image(
                  width: 34,
                  height: 34,
                  image: AssetImage("assets/icons/qr_embedded.png"),
                ),
                Padding(
                    padding: EdgeInsets.fromLTRB(10.0, 0.0, 10.0, 0.0),
                    child: Text("Amkor MSO"))
              ],
            ),
            content: Text.rich(
              textAlign: TextAlign.left,
              TextSpan(
                text:
                    '        为了更好地维护您的权益，我们对《隐私政策》进行了更新，特向您推送本更新提示，请仔细阅读并充分理解相关条款。您点击“同意”，即表示您已阅读并同意更新后的',
                style: const TextStyle(fontSize: 12, color: Colors.black),
                children: <InlineSpan>[
                  WidgetSpan(
                    child: GestureDetector(
                      onTap: () {},
                      child: const Text(
                        '《隐私政策》',
                        style: TextStyle(fontSize: 12, color: Colors.blue),
                      ),
                    ),
                  ),
                  const TextSpan(
                    text: '条款。',
                    style: TextStyle(fontSize: 12, color: Colors.black),
                  ),
                ],
              ),
            ),
            actions: <Widget>[
              CupertinoDialogAction(
                child: const Text("取消"),
                onPressed: () {
                  Navigator.of(context).pushAndRemoveUntil(
                      MaterialPageRoute(
                          builder: (BuildContext context) => const LoginPage()),
                      (route) => false);
                },
              ),
              CupertinoDialogAction(
                child: const Text("确定"),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              )
            ],
          );
        });
  }

  Future<void> _LogOutAppDialog(context1) {
    return showDialog<void>(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return StatefulBuilder(
            builder: (BuildContext context, StateSetter dialogSetState) {
              // 使用Timer定期更新对话框UI
              Timer.periodic(const Duration(milliseconds: 100), (timer) {
                if (_dPercent >= 1.0) {
                  timer.cancel();
                  // 下载完成，延迟关闭对话框
                  Future.delayed(const Duration(milliseconds: 500), () {
                    if (Navigator.canPop(context)) {
                      Navigator.of(context).pop();
                    }
                  });
                } else {
                  // 更新对话框UI
                  if (context.mounted) {
                    dialogSetState(() {
                      // 触发对话框重建，显示最新进度
                    });
                  }
                }
              });

              return CupertinoAlertDialog(
                title: const Row(
                  children: <Widget>[
                    Image(
                      width: 34,
                      height: 34,
                      image: AssetImage("assets/icons/qr_embedded.png"),
                    ),
                    Padding(
                        padding: EdgeInsets.fromLTRB(10.0, 0.0, 10.0, 0.0),
                        child: Text("Amkor MSO"))
                  ],
                ),
                content: Column(
                  children: [
                    const Row(
                      children: [
                        Text(
                          "正在更新请稍等。。。。 ",
                          style: TextStyle(fontSize: 14),
                        ),
                        CupertinoActivityIndicator(
                          radius: 10,
                          animating: true,
                        ),
                      ],
                    ),
                    Row(
                      children: [
                        Expanded(
                          child: LinearProgressIndicator(
                            backgroundColor: Colors.grey[600],
                            value: _dPercent,
                          ),
                        ),
                        const SizedBox(
                          width: 5,
                        ),
                        Text(
                          _dPercent0,
                          style: const TextStyle(
                              color: Colors.black, fontSize: 12.0),
                        ),
                      ],
                    ),
                  ],
                ),
              );
            },
          );
        });
  }

  //弹出"版本更新"对话框
  Future<void> _showNewVersionAppDialog() {
    if (Platform.isAndroid) {
      return showDialog<void>(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return StatefulBuilder(
              builder: (BuildContext context,
                  void Function(void Function()) setState) {
                return AlertDialog(
                  title: const Row(
                    children: <Widget>[
                      Image(
                        height: 30,
                        image: AssetImage(
                          "assets/images/show_code_logo.png",
                        ),
                      ),
                    ],
                  ),
                  content: Text('现有最新版v${buildVersion}, 是否立刻更新？'),
                  actions: <Widget>[
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextButton(
                          child: const Text(
                            '以后',
                          ),
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                        ),
                        TextButton(
                          child: const Text(
                            '更新',
                          ),
                          onPressed: () async {
                            Navigator.of(context).pop();
                            // 使用优化的下载安装方法
                            await _downloadAndInstallWithProgress(
                                buildload_url!);
                          },
                        ),
                      ],
                    ),
                  ],
                );
              },
            );
          });
    } else {
      //iOS
      return showDialog<void>(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return CupertinoAlertDialog(
              title: const Row(
                children: <Widget>[
                  Image(
                    width: 35,
                    height: 35,
                    // image: AssetImage("assets/icons/qr_embedded.png"),
                    image: AssetImage("assets/images/show_code_logo.png"),
                  ),
                  Padding(
                      padding: EdgeInsets.fromLTRB(30.0, 0.0, 10.0, 0.0),
                      child: Text("MSO升级"))
                ],
              ),
              content: Text('现有最新版v${buildVersion}, 是否立刻更新？'),
              actions: <Widget>[
                CupertinoDialogAction(
                  child: const Text("取消"),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
                CupertinoDialogAction(
                  child: const Text("更新"),
                  onPressed: () {
                    launchUrl(Uri.parse(
                        "itms-apps://duidui?action=discover&amp;referrer=app-store&amp;itscg=10000&amp;itsct=app-appstore-nav-200917"));
                    Navigator.of(context).pop();
                  },
                )
              ],
            );
          });
    }
  }

  /// 使用优化的APK安装工具下载并安装
  Future<void> _downloadAndInstallWithProgress(String url) async {
    // 重置进度
    if (mounted) {
      setState(() {
        _dPercent = 0.0;
        _dPercent0 = "0.00%";
      });

      // 显示下载进度对话框
      _LogOutAppDialog(context);
    }

    try {
      final success = await ApkInstaller.downloadAndInstallApk(
        url: url,
        context: context,
        onProgress: (received, total) {
          if (mounted) {
            // 确保Widget仍然存在
            setState(() {
              _dPercent = received / total;
              _dPercent0 = '${(_dPercent * 100).toStringAsFixed(2)}%';
            });
            debugPrint('下载进度: ${(_dPercent * 100).toStringAsFixed(2)}%');
          }
        },
        fileName: 'mso_update_${DateTime.now().millisecondsSinceEpoch}.apk',
      );

      if (mounted) {
        if (success) {
          // 下载完成，显示通知
          LocalNotification().showNotification(
            id: 000,
            title: "下载通知",
            body: "下载已完成，正在安装...",
            payload: "000",
          );

          setState(() {
            a = true;
          });

          // 关闭进度对话框
          if (Navigator.canPop(context)) {
            Navigator.of(context).pop();
          }
        } else {
          // 下载失败，关闭进度对话框并显示错误信息
          if (Navigator.canPop(context)) {
            Navigator.of(context).pop();
          }
          _showErrorDialog('下载失败', '无法下载更新包，请检查网络连接或稍后重试。');
        }
      }
    } catch (e) {
      debugPrint('下载APK失败: $e');
      if (mounted) {
        // 关闭进度对话框并显示错误信息
        if (Navigator.canPop(context)) {
          Navigator.of(context).pop();
        }
        _showErrorDialog('下载失败', '下载过程中发生错误：$e');
      }
    }
  }

  /// 显示错误对话框
  void _showErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }

  ///用分割URL来获取下载的APK文件名称
  String getFlieName(String url) {
    try {
      List<String> lstStr = url.split('/');
      return lstStr.last;
    } catch (e) {
      debugPrint("获取下载文件名错误：$e");
    }
    return "";
  }

  @override
  Widget build(BuildContext context) => widget.child;
}

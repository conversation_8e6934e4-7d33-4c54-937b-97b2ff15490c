import 'dart:io';
import 'package:amkor/jgts.dart';
import 'package:amkor/services/baseService.dart';
import 'package:amkor/services/sendLocalNotification.dart';
import 'package:amkor/utils/adapt.dart';
import 'package:amkor/components/security_manager.dart';
import 'package:amkor/LanguageSwitching/DemoLocalizations.dart';
import 'package:amkor/components/routes.dart';
import 'package:amkor/model/MyBinding.dart';
import 'package:amkor/startPage.dart';
import 'package:amkor/utils/gKey.dart';
import 'package:amkor/watermark.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_udid/flutter_udid.dart';
import 'package:get_storage/get_storage.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get/get.dart';
// import 'package:secure_application/secure_application.dart'; // 暂时禁用

void main() async {
  await GetStorage.init();

  ///初始化极光推送
  // PushUtil.initPush();  //Leo remark 20231017
  Adapt.setAndroid();
  HttpOverrides.global = GlobalHttpOverrides();
  LocalNotification().initialize();

  // 初始化安全管理器
  await SecurityManager.instance.initialize();

  SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp])
      .then((_) {
    runApp(const MyApp());
  });
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  // Future<void> dataAcquisition() async {
  //   ///获取极光推送注册id
  //   String registrationId = await PushUtil.jpush!.getRegistrationID();
  //   debugPrint('JPush Registration ID: $registrationId');
  //   ///获取设备UDID
  //   String deviceUdid = await FlutterUdid.udid;

  //   for (var interface in await NetworkInterface.list()) {
  //     for (var address in interface.addresses) {
  //       if (address.type == InternetAddressType.IPv4) {
  //       }
  //     }
  //   }

  // }

  @override
  Widget build(BuildContext context) {
    ///获取udid
    // dataAcquisition();

    Widget app = GetMaterialApp(
      // showPerformanceOverlay: true,
      initialBinding: MyBinding(),
      locale: Get.deviceLocale,
      translations: Messages(),
      fallbackLocale: const Locale('zh', 'CN'),
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        // scaffoldBackgroundColor:Colors.white,
        scaffoldBackgroundColor: const Color.fromRGBO(241, 242, 246, 1),
        primarySwatch: Colors.blue,
      ),
      home: StartPage(),
      // TreeNodePage(),
      // const ShowMapPageBody(),
      builder: EasyLoading.init(),
      routes: Routes.getRoutes(),
      // navigatorObservers: [MyNavigatorObserver()],
      navigatorKey: GlobalKeys.navigatorKey,
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('zh', 'CN'),
        Locale('en', 'EN'),
        // Locale('en', 'EN'),
      ],
    );

    // Android: 启用防截屏，iOS: 不启用（通过监听处理）
    // 暂时禁用 SecureApplication 功能
    // if (Platform.isAndroid) {
    //   return SecureApplication(
    //     child: app,
    //   );
    // } else {
    return app;
    // }
    //   ColorFiltered(
    //   colorFilter:ColorFilter.mode(Colors.grey, BlendMode.color),
    //   child:
    // );
  }
}

class MessagesController extends GetxController {
  void changeLanguage(String languageCode, String countryCode) {
    var locale = Locale(languageCode, countryCode);
    Get.updateLocale(locale);
  }
}

class MyNavigatorObserver extends NavigatorObserver {
  @override
  void didPush(Route<dynamic>? route, Route<dynamic>? previousRoute) {
    super.didPush(route!, previousRoute);
    debugPrint('页面切换：${route.settings.name}');
    if (GlobalKeys.currentContext != null) {
      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        addWaterMarkter(GlobalKeys.currentContext, 'Gh', 3, 12);
      });
    }
  }

  @override
  void didPop(Route<dynamic>? route, Route<dynamic>? previousRoute) {
    super.didPop(route!, previousRoute);
    print('页面返回：${route.settings.name}');
  }
}

///默认水印
void addWaterMarkter(
    BuildContext? context, String string, int row, int column) async {
  OverlayEntry? overlayEntry;
  OverlayState? overlayState = Overlay.of(context!);
  overlayEntry = OverlayEntry(
      builder: (context) => WatermarkWidget(
            rowCount: row,
            columnCount: column,
            text: string,
            textStyle: const TextStyle(
                color: Color.fromRGBO(201, 205, 212, 0.06),
                fontSize: 14,
                decoration: TextDecoration.none),
          ));
  overlayState?.insert(overlayEntry!);
}

import 'package:amkor/indexPages/news/news_notice.dart';
import 'package:amkor/model/MyBinding.dart';
import 'package:flutter/material.dart';
// import 'package:flutter_app_badger/flutter_app_badger.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:badges/badges.dart' as badges;
class NewsPage extends StatefulWidget {
   const NewsPage({Key? key}) : super(key: key);
  @override
  State<NewsPage> createState() => _NewsPageState();
}

class _NewsPageState extends State<NewsPage> {
  @override
  void initState() {
    // TODO: implement activate
    super.initState();
  }
  final UIMessage c=Get.find();
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Text('Tips12'.tr),
      // child: Text('功能正在开发中...'),
    );
    // return Scaffold(
    //   appBar: AppBar(
    //     title:const Text("消息",style:  TextStyle(color: Colors.black,fontWeight: FontWeight.w900),),
    //     backgroundColor: Colors.white,
    //     elevation:0,
    //     centerTitle:true,
    //     // fontColor: Colors.black,
    //   ),
    //   body:ListView(
    //     children: [
    //       GestureDetector(
    //         onTap: (){
    //           Navigator.push(context, MaterialPageRoute(builder: (context) {
    //             return const NewsNoticePage();
    //           })).then((e){
    //             GetStorage().remove('unreadNotice');
    //             c.increment();
    //             ///清空角标
    //             FlutterAppBadger.removeBadge();
    //             setState(() {});
    //           });
    //         },
    //         child:Container(
    //             padding:const EdgeInsets.all(10),
    //             margin:const EdgeInsetsDirectional.all(10),
    //             decoration:  BoxDecoration(
    //               borderRadius: BorderRadius.circular(6),
    //               color:const Color.fromRGBO(0, 0, 0, 0.05),
    //             ),
    //             child:Row(
    //               mainAxisAlignment: MainAxisAlignment.spaceBetween,
    //               crossAxisAlignment: CrossAxisAlignment.start,
    //               children: [
    //                 Row(
    //                   children: [
    //                     Padding(
    //                       padding:const EdgeInsetsDirectional.only(end: 20),
    //                       child: badges.Badge(
    //                         position: badges.BadgePosition.topEnd(top: -4, end: -8),
    //                         showBadge: true,
    //                         ignorePointer: false,
    //                         badgeStyle: badges.BadgeStyle(
    //                           shape: badges.BadgeShape.circle,
    //                           badgeColor: Colors.red,
    //                           borderRadius: BorderRadius.circular(10),
    //                           borderSide: const BorderSide(color: Colors.white, width: 2),
    //                           elevation: 0,
    //                         ),
    //                         badgeContent: Text(
    //                           GetStorage().read('unreadNotice')!=null
    //                               ?GetStorage().read('unreadNotice').length<99?GetStorage().read('unreadNotice').length.toString():"99+"
    //                               :"  ",
    //                           style:const TextStyle(
    //                             color: Colors.white,
    //                             fontSize: 12,
    //                           ),
    //                         ),
    //                         child:const Image(
    //                           image: AssetImage("assets/icons/news/zygg.png"),
    //                           width: 54,
    //                           height: 54,
    //                         ),
    //                       ),
    //                     ),
    //                     Column(
    //                       mainAxisAlignment: MainAxisAlignment.start,
    //                       crossAxisAlignment: CrossAxisAlignment.start,
    //                       children: [
    //                         const Text(
    //                           "重要公告",
    //                           style: TextStyle(
    //                             color: Colors.black,
    //                             fontSize: 16,
    //                             fontWeight: FontWeight.bold,
    //                           ),
    //
    //                         ),
    //                         const SizedBox(
    //                           height: 10,
    //                         ),
    //                         SizedBox(
    //                           width: MediaQuery.of(context).size.width -200,
    //                           child:Text(
    //                             GetStorage().read('unreadNotice')!=null
    //                                 ?GetStorage().read('unreadNotice')[0]["title"]:"暂无未读最新通知",
    //                             style: const TextStyle(
    //                               color: Colors.grey,
    //                               fontSize: 14,
    //                             ),
    //                             overflow: TextOverflow.ellipsis,
    //                           ),)
    //
    //                       ],
    //                     )
    //                   ],
    //                 ) ,
    //                 Text(
    //                   GetStorage().read('unreadNotice')!=null
    //                       ?GetStorage().read('unreadNotice')[0]["publishTime"]:"  ",
    //                   style:const TextStyle(
    //                     color: Colors.grey,
    //                     fontSize: 12,
    //                   ),
    //                 ),
    //
    //               ],
    //             )
    //         ),
    //       ),
    //       GestureDetector(
    //         onTap: (){
    //         },
    //         child:Container(
    //             padding:const EdgeInsets.all(10),
    //             margin:const EdgeInsetsDirectional.all(10),
    //             decoration:  BoxDecoration(
    //               borderRadius: BorderRadius.circular(6),
    //               color:const Color.fromRGBO(0, 0, 0, 0.05),
    //             ),
    //             child:Row(
    //               mainAxisAlignment: MainAxisAlignment.spaceBetween,
    //               crossAxisAlignment: CrossAxisAlignment.start,
    //               children: [
    //                 Row(
    //                   children: [
    //                     Padding(
    //                       padding:const EdgeInsetsDirectional.only(end: 20),
    //                       child: badges.Badge(
    //                         position: badges.BadgePosition.topEnd(top: -4, end: -8),
    //                         showBadge: true,
    //                         ignorePointer: false,
    //                         badgeStyle: badges.BadgeStyle(
    //                           shape: badges.BadgeShape.circle,
    //                           badgeColor: Colors.red,
    //                           borderRadius: BorderRadius.circular(10),
    //                           borderSide: const BorderSide(color: Colors.white, width: 2),
    //                           elevation: 0,
    //                         ),
    //                         badgeContent: Text(
    //                           '  ',
    //                           style: TextStyle(
    //                             color: Colors.white,
    //                             fontSize: 12,
    //                           ),
    //                         ),
    //                         child: Image(
    //                           image: AssetImage("assets/icons/news/5s.png"),
    //                           width: 54,
    //                           height: 54,
    //                         ),
    //                       ),
    //                     ),
    //
    //                     Column(
    //                       mainAxisAlignment: MainAxisAlignment.start,
    //                       crossAxisAlignment: CrossAxisAlignment.start,
    //                       children: [
    //                         Text(
    //                           "待办事项",
    //                           style: TextStyle(
    //                             color: Colors.black,
    //                             fontSize: 16,
    //                             fontWeight: FontWeight.bold,
    //                           ),
    //
    //                         ),
    //                         const SizedBox(
    //                           height: 10,
    //                         ),
    //                         SizedBox(
    //                           width: MediaQuery.of(context).size.width -200,
    //                           child:Text(
    //                             '您的体检报告已经生成请注意查看',
    //                             style: TextStyle(
    //                               color: Colors.grey,
    //                               fontSize: 14,
    //                             ),
    //                             overflow: TextOverflow.ellipsis,
    //                           ),)
    //
    //
    //
    //                       ],
    //                     )
    //                   ],
    //                 ) ,
    //                 Text(
    //                   "2023/4/4 9:30",
    //                   style: TextStyle(
    //                     color: Colors.grey,
    //                     fontSize: 12,
    //                   ),
    //                 ),
    //
    //               ],
    //             )
    //         ),
    //       ),
    //       GestureDetector(
    //         onTap: (){
    //         },
    //         child:Container(
    //             padding:const EdgeInsets.all(10),
    //             margin:const EdgeInsetsDirectional.all(10),
    //             decoration:  BoxDecoration(
    //               borderRadius: BorderRadius.circular(6),
    //               color:const Color.fromRGBO(0, 0, 0, 0.05),
    //             ),
    //             child:Row(
    //               mainAxisAlignment: MainAxisAlignment.spaceBetween,
    //               crossAxisAlignment: CrossAxisAlignment.start,
    //               children: [
    //                 Row(
    //                   children: [
    //                     Padding(
    //                       padding:const EdgeInsetsDirectional.only(end: 20),
    //                       child: badges.Badge(
    //                         position: badges.BadgePosition.topEnd(top: -4, end: -8),
    //                         showBadge: true,
    //                         ignorePointer: false,
    //                         badgeStyle: badges.BadgeStyle(
    //                           shape: badges.BadgeShape.circle,
    //                           badgeColor: Colors.red,
    //                           borderRadius: BorderRadius.circular(10),
    //                           borderSide: const BorderSide(color: Colors.white, width: 2),
    //                           elevation: 0,
    //                         ),
    //                         badgeContent: Text(
    //                           '  ',
    //                           style: TextStyle(
    //                             color: Colors.white,
    //                             fontSize: 12,
    //                           ),
    //                         ),
    //                         child: Image(
    //                           image: AssetImage("assets/icons/news/dbsx.png"),
    //                           width: 54,
    //                           height: 54,
    //                         ),
    //                       ),
    //                     ),
    //
    //                     Column(
    //                       mainAxisAlignment: MainAxisAlignment.start,
    //                       crossAxisAlignment: CrossAxisAlignment.start,
    //                       children: [
    //                         Text(
    //                           "5S待办事项",
    //                           style: TextStyle(
    //                             color: Colors.black,
    //                             fontSize: 16,
    //                             fontWeight: FontWeight.bold,
    //                           ),
    //
    //                         ),
    //                         const SizedBox(
    //                           height: 10,
    //                         ),
    //                         SizedBox(
    //                           width: MediaQuery.of(context).size.width -200,
    //                           child: Text(
    //                             '您的体检报告已经生成请注意查看',
    //                             style: TextStyle(
    //                               color: Colors.grey,
    //                               fontSize: 14,
    //                             ),
    //                             overflow: TextOverflow.ellipsis,
    //                           ) ,
    //                         )
    //
    //                       ],
    //                     )
    //                   ],
    //                 ) ,
    //                 Text(
    //                   "2023/4/4 9:30",
    //                   style: TextStyle(
    //                     color: Colors.grey,
    //                     fontSize: 12,
    //                   ),
    //                 ),
    //
    //               ],
    //             )
    //         ),
    //       ),
    //     ],
    //   )
    //
    //
    //
    // );
  }
}

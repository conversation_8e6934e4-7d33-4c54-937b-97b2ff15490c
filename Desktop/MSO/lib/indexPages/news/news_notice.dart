
import 'package:amkor/Notice_xq.dart';
import 'package:flutter/material.dart';
import 'package:badges/badges.dart' as badges;
import 'package:get_storage/get_storage.dart';
class NewsNoticePage extends StatefulWidget {
  const NewsNoticePage({Key? key }) : super(key: key);
  @override
  State<NewsNoticePage> createState() => _NewsPageState();
}

class _NewsPageState extends State<NewsNoticePage> {
  var dataList;
  int dataListLength=0;

  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    // TODO: implement activate
    super.initState();
    Listdata();
  }
  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }
  Future<void> Listdata() async {
    var r=GetStorage().read('unreadNotice');
    int newId=0;

    //是否已经被释放
    if (mounted&&r!=null) {
      setState(() {
        dataList=r;
        dataListLength=r.length;
      });
      r.map((e){
       if(e["id"]>newId)newId=e["id"];
      }).toList();
      GetStorage().write('unreadNoticeId',newId);
    }

    print("object-------------------$newId");
  }


  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title:const Text("重要公告",style:  TextStyle(color: Colors.black,fontWeight: FontWeight.w900),),
          backgroundColor: Colors.white,
          elevation:0,
          centerTitle:true,
          leading:GestureDetector(
            onTap: (){
              Navigator.of(context).pop();
            },
            child:const Icon(Icons.arrow_back_ios,color: Colors.black,),
          ) ,
        ),
        body:dataListLength>0?
        ListView.builder(
            itemCount:dataListLength,
            itemBuilder: (BuildContext context, int index) {
              return Column(
                children: [
                  Padding(padding: const EdgeInsetsDirectional.only(top: 12,bottom: 4),
                    child: Container(
                      alignment: Alignment.center,
                      padding: const EdgeInsetsDirectional.all(4),
                      width:  MediaQuery.of(context).size.width*0.24,
                      decoration: const BoxDecoration(
                        color: Color.fromRGBO(0, 0, 0, 0.1),
                      ),
                      child:Text(dataList[index]["publishTime"],style: const TextStyle(fontSize: 12,color: Colors.white),),
                    ),
                  ),
                  GestureDetector(
                    onTap: (){
                      Navigator.of(context).push(MaterialPageRoute(
                        builder: (context) => NoticeXqPage(id: "${dataList[index]["id"]}",),
                      ));
                    },
                    child:Container(
                        padding:const EdgeInsets.all(10),
                        margin:const EdgeInsetsDirectional.all(10),
                        decoration:  BoxDecoration(
                          borderRadius: BorderRadius.circular(6),
                          color:const Color.fromRGBO(0, 0, 0, 0.05),
                        ),
                        child:Column(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Padding(
                                  padding:const EdgeInsetsDirectional.only(end: 20),
                                  child: badges.Badge(
                                    position: badges.BadgePosition.topEnd(top: -1, end: -3),
                                    showBadge: true,
                                    ignorePointer: false,
                                    badgeStyle: badges.BadgeStyle(
                                      shape: badges.BadgeShape.circle,
                                      badgeColor: Colors.red,
                                      borderRadius: BorderRadius.circular(20),
                                      borderSide: const BorderSide(color: Colors.white, width: 2),
                                      elevation: 0,
                                    ),
                                    badgeContent: SizedBox(width: 4,height: 4,),
                                    child: Image(
                                      image: AssetImage("assets/icons/news/5s.png"),
                                      width: 34,
                                      height: 34,
                                    ),
                                  ),
                                ),
                                Expanded(child: Text(
                                  dataList[index]["title"],
                                  style:const TextStyle(
                                    color: Colors.black,
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),),
                                const Text(
                                  "查看详情",
                                  style: TextStyle(
                                    color: Colors.grey,
                                    fontSize: 12,
                                  ),
                                ),

                              ],
                            ) ,
                            const Divider(height: 20,),
                            Text(
                              dataList[index]["summary"],
                              style:const TextStyle(
                                color: Colors.grey,
                                fontSize: 14,
                              ),
                            ),

                          ],
                        )
                    ),
                  ),

                ],
              );
            }

        ):
        const Center(
          child: Text(
            "暂无未读最新通知",
            style: TextStyle(
              color: Colors.grey,
              fontSize: 14,
            ),
          ),
        )
    );
  }
}

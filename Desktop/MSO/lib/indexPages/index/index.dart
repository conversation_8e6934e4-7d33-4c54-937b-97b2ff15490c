import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:amkor/indexPages/news/news.dart';
import 'package:amkor/indexPages/news/news_notice.dart';
import 'package:amkor/indexPages/notice.dart';
import 'package:amkor/indexPages/workbench.dart';
import 'package:amkor/indexPages/person.dart';
import 'package:amkor/indexPages/home.dart';
import 'package:amkor/model/MyBinding.dart';
import 'package:amkor/model/kvPair.dart';
import 'package:amkor/services/employeeService.dart';
import 'package:amkor/utils/gKey.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import '../../components/security_manager.dart';
// import '../../components/screenshot_callback.dart';
// import 'package:screenshot_callback/screenshot_callback.dart';
import 'package:badges/badges.dart' as badges;

GlobalKey? jtKey = GlobalKey();

class IndexPage extends StatefulWidget {
  const IndexPage({Key? key}) : super(key: key);
  @override
  State<IndexPage> createState() => _IndexPageState();
}

class _IndexPageState extends State<IndexPage> {
  int _selectedIndex = 0;
  //底部导航栏组件列表
  late final List<Widget> _pageList = [
    const HomePage(),
    const NoticePage(),
    WorkbenchPage(),
    const NewsPage(),
    PersonPage()
  ];
  // 缓存页面
  late PageController _pageController;
  final UIMessage c = Get.put(UIMessage());
  final GlobalKey _repaintKey = GlobalKey(); // 可以获取到被截图组件状态的 GlobalKey
  Uint8List? _imageFile;
  final SecurityManager _securityManager = SecurityManager.instance;

  ///初始化iOS截屏监听（记录截屏事件）
  Future<void> initScreenshotCallback() async {
    // 初始化安全管理器
    await _securityManager.initialize();

    // 添加截屏监听器
    _securityManager.addScreenshotListener(() async {
      jtKey ??= _repaintKey;
      _imageFile = await getImageData(jtKey!);
      if (_imageFile != null) {
        print("===原数据大小==${_imageFile!.toList().length}"); // 原数据大小
        _imageFile = await FlutterImageCompress.compressWithList(
          _imageFile!,
          quality: 70, // 修改压缩质量，通常在 50 到 90 之间，数值越大压缩率越低
        );
        print("===输出压缩后的数据大小===${_imageFile!.toList().length}"); // 输出压缩后的数据
        final imageEncoded = base64.encode(_imageFile!.toList());
        print("-----$imageEncoded-------$_imageFile");
        HistoryApp()
            .UploadHistoryCutScreenBase64(imageEncoded, NoticeTitle.title);
        setState(() {});
      }
    });
  }

  ///初始化Android防截屏（直接阻止截屏）
  Future<void> initAndroidScreenshotPrevention() async {
    // 初始化安全管理器并启用防截屏
    await _securityManager.initialize();

    // Android直接阻止截屏，不需要监听事件
    // secure_application 会在Android上设置 FLAG_SECURE 标志
    print("Android防截屏已启用");
  }

  /// 获取截取图片的数据
  Future<Uint8List?> getImageData(GlobalKey repaintKey) async {
    BuildContext? buildContext = repaintKey.currentContext;
    if (buildContext != null) {
      RenderRepaintBoundary? boundary =
          buildContext.findRenderObject() as RenderRepaintBoundary?;
      // 获取当前设备的像素比
      double pixelRatio = MediaQuery.of(buildContext).devicePixelRatio;
      // pixelRatio 代表截屏之后的模糊程度，因为不同设备的像素比不同
      ui.Image image = await boundary!.toImage(pixelRatio: pixelRatio);
      // 定义一个固定数值显然不是最佳方案，所以以当前设备的像素为目标值
      ByteData? byteData =
          await image.toByteData(format: ui.ImageByteFormat.png);
      Uint8List imageBytes = byteData!.buffer.asUint8List();
      // 返回图片的数据
      return imageBytes;
    }
    return null;
  }

  @override
  void initState() {
    // 注意：不再需要手动设置context，GlobalKeys.currentContext会自动获取
    jtKey = _repaintKey;
    // iOS: 监听截屏事件记录，Android: 直接阻止截屏
    if (Platform.isIOS) {
      initScreenshotCallback(); // iOS监听截屏事件
    } else if (Platform.isAndroid) {
      initAndroidScreenshotPrevention(); // Android阻止截屏
    }
    super.initState();
    _pageController = PageController(initialPage: _selectedIndex);
  }

  @override
  void dispose() {
    // 清理安全管理器的监听器
    _securityManager.clearScreenshotListeners();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      key: _repaintKey,
      child: Scaffold(
          body: Stack(children: <Widget>[
            PageView(
                onPageChanged: _pageChange,
                controller: _pageController,
                children: _pageList),
            // if(_imageFile!=null)Image.memory(_imageFile!, fit: BoxFit.cover),
          ]),
          floatingActionButtonLocation:
              FloatingActionButtonLocation.centerDocked,
          floatingActionButton: FloatingActionButton(
            backgroundColor: const Color(0xFFECEFF1),
            child: ImageIcon(
              const AssetImage("assets/icons/nav/work_n.png"),
              size: _selectedIndex == 2 ? 24 : 20,
              color: const Color.fromRGBO(61, 92, 255, 1),
            ),
            onPressed: () {
              setState(() {
                _selectedIndex = 2;
                _pageController.jumpToPage(2);
              });
            },
          ),
          extendBody: true,
          bottomNavigationBar: ClipRRect(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20.0),
              topRight: Radius.circular(20.0),
            ),
            child: BottomAppBar(
              shape: const CircularNotchedRectangle(),
              notchMargin: 6.0,
              // color: Colors.white,
              child: Row(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: <Widget>[
                  buildBotomItem(_selectedIndex, 0, "home", "首页".tr),
                  buildBotomItem(_selectedIndex, 1, "gg", "公告".tr),
                  buildBotomItem(_selectedIndex, 2, "work", "工作台"),
                  buildBotomItem(_selectedIndex, 3, "xx", "消息".tr),
                  buildBotomItem(_selectedIndex, 4, "my", "我的".tr),
                ],
              ),
            ),
          )),
    );
  }

  Widget buildBotomItem(
      int selectIndex, int index, String iconData, String title) {
    TextStyle textStyle = const TextStyle(fontSize: 12.0, color: Colors.grey);
    Color iconColor = const Color(0xFFB0BEC5);
    double iconSize = 20;
    EdgeInsetsGeometry padding = const EdgeInsets.only(top: 8.0);
    bool shx = false;
    if (selectIndex == index) {
      textStyle = const TextStyle(
          fontSize: 12.0, color: Color.fromRGBO(61, 92, 255, 1));
      iconColor = const Color.fromRGBO(61, 92, 255, 1);
      iconSize = 20;
      padding = const EdgeInsets.only(top: 8.0);
      shx = true;
    }
    Widget padItem = const SizedBox();
    if (title != '工作台') {
      padItem = Padding(
        padding: const EdgeInsets.only(left: 16, right: 16),
        child: Container(
          padding: padding,
          decoration: BoxDecoration(
              border: Border(
            top: shx
                ? const BorderSide(color: Colors.indigo, width: 2)
                : const BorderSide(color: Colors.transparent, width: 2),
          )),
          child: title == "消息"
              ? badges.Badge(
                  position: badges.BadgePosition.topEnd(top: -6, end: -4),
                  showBadge: true,
                  ignorePointer: false,
                  badgeStyle: badges.BadgeStyle(
                    shape: badges.BadgeShape.circle,
                    // badgeColor: Colors.red,
                    badgeColor: Colors.transparent,
                    borderRadius: BorderRadius.circular(10),
                    borderSide: const BorderSide(color: Colors.white, width: 2),
                    elevation: 0,
                  ),
                  badgeContent: Obx(() => Text(
                        "${c.count.value == 0 ? ' ' : c.count.value}",
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                        ),
                      )),
                  child: Column(
                    children: [
                      ImageIcon(
                        AssetImage("assets/icons/nav/${iconData}_n.png"),
                        size: iconSize,
                        color: iconColor,
                      ),
                      const Padding(padding: EdgeInsets.only(top: 4.0)),
                      Text(
                        title,
                        style: textStyle,
                      )
                    ],
                  ))
              : Column(
                  children: <Widget>[
                    ImageIcon(
                      AssetImage("assets/icons/nav/${iconData}_n.png"),
                      size: iconSize,
                      color: iconColor,
                    ),
                    const Padding(padding: EdgeInsets.only(top: 4.0)),
                    Text(
                      title,
                      style: textStyle,
                    )
                  ],
                ),
        ),
      );
    }
    Widget item = Expanded(
      flex: 1,
      child: GestureDetector(
        onTap: () {
          if (index != _selectedIndex) {
            setState(() {
              _selectedIndex = index;
              _pageController.jumpToPage(index);
            });
          }
        },
        child: SizedBox(
          height: 62,
          child: padItem,
        ),
      ),
    );
    return item;
  }

  void _pageChange(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }
}

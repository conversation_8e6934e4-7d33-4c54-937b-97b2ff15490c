import 'package:amkor/Notice_xq.dart';
import 'package:amkor/services/employeeService.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get_utils/src/extensions/internacionalization.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../services/permission/main_page.dart';
import '../utils/adapt.dart';

class NoticePage extends StatefulWidget {
  const NoticePage({Key? key }) : super(key: key);

  @override
  State<NoticePage> createState() => _NoticePageState();
}

class _NoticePageState extends State<NoticePage> {
  var dataList;
  int total1=0;
  int total=0;
  int i=1;
  bool kg=true;
  final ScrollController _scrollController = ScrollController();
  @override
  void initState() {
    // TODO: implement activate
    super.initState();
    if(!kIsWeb) {
      Adapt.setAndroid();
    }
    data(i);
    // 监听滚动事件
    _scrollController.addListener((){
      // 获取滚动条下拉的距离
      if(_scrollController.position.pixels==_scrollController.position.maxScrollExtent&&!kg){
        setState(() {
          kg=true;
        });
      }
    });

  }
  @override
  void dispose() {
    _refreshController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> data(int i) async {
    var r=await AnnouncementApp().PostAnnouncementList(i,10);
    //是否已经被释放
    if (mounted) {
      setState(() {
      dataList!=null?dataList.addAll(r?.data):dataList=r?.data;
      total=r!.total;
      total1=dataList?.length;
    });
    }
  }
  //加载中的圈圈
  Widget _loadMoreWidget() {
    Future.delayed(const Duration(milliseconds:2000),(){
      if(i<(total-total%10)/10+1) {
        i++;
        kg=false;
        data(i);
      }
    });
      //还有更多数据可以加载
      return Center(
        child: Padding(
          padding: const EdgeInsetsDirectional.only(top: 10,bottom: 60),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: const <Widget>[
              Text("内容即将呈现......"),
              SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                ),
              )
            ],
          ),
        ),
      );
  }

  final RefreshController _refreshController = RefreshController(initialRefresh: false);
  void _onRefresh() async{
    await Future.delayed(const Duration(milliseconds: 400));
      dataList=null;
      total=0;
      total1=0;
      i=1;
    data(i);
    _refreshController.refreshCompleted();
  }


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("bulletin".tr ,style:  TextStyle(color: Colors.black,fontWeight: FontWeight.w900),),
        // title:const Text("公告",style:  TextStyle(color: Colors.black,fontWeight: FontWeight.w900),),
        backgroundColor: Colors.white,
        elevation:0,
        centerTitle:true,
        // fontColor: Colors.black,
      ),

      body: roles == "1"?
      Padding(padding:const EdgeInsetsDirectional.only(bottom: 40),
          child: SmartRefresher(
            enablePullDown: true,
            enablePullUp: false,
            header: const WaterDropHeader(
                waterDropColor:Colors.transparent,
                complete:Text('加载成功'),
                failed:Text('刷新失败'),
                ///默认动画
                // refresh:
                idleIcon: Icon(
                  Icons.autorenew,
                  size: 20,
                  color: Colors.black,
                ),
            ),
            footer: CustomFooter(
              builder: ( context, mode){
                Widget body ;
                if(mode==LoadStatus.idle){
                  body =  const Text("上拉加载");
                }
                else if(mode==LoadStatus.loading){
                  body =  const CupertinoActivityIndicator();
                }
                else if(mode == LoadStatus.failed){
                  body = const Text("加载失败");
                }
                else if(mode == LoadStatus.canLoading){
                  body = const Text("刷新成功");
                }
                else{
                  body = const Text("没有数据");
                }
                return SizedBox(
                  height: 55.0,
                  child: Center(child:body),
                );
              },
            ),
            controller: _refreshController,
            onRefresh: _onRefresh,
            onLoading: null,
            child: ListView.builder(
                itemCount: total1,
                controller: _scrollController,
                itemBuilder: (context, index) {
                  return Column(
                    children: [
                      Padding(padding: const EdgeInsetsDirectional.only(top: 20,bottom: 10),
                        child: Container(
                          alignment: Alignment.center,
                          padding: const EdgeInsetsDirectional.all(4),
                          width:  MediaQuery.of(context).size.width*0.24,
                          decoration: const BoxDecoration(
                            color: Color.fromRGBO(0, 0, 0, 0.1),
                          ),
                          child:Text("${dataList[index]["publishTime"]}",style: const TextStyle(fontSize: 12,color: Colors.white),),
                        ),
                      ),
                      GestureDetector(
                        child:Container(
                          clipBehavior: Clip.hardEdge,
                          decoration: const BoxDecoration(
                            borderRadius: BorderRadius.all(Radius.circular(4)),
                            color:  Colors.white,
                          ),
                          width: MediaQuery.of(context).size.width*0.9,
                          // padding: EdgeInsets.all(25),
                          child:Column(
                            children: [
                              Stack(
                                children: [
                                  Image.network(dataList[index]["cover"].replaceAll("\\","/"),
                                    width: MediaQuery.of(context).size.width *0.9,
                                    height: MediaQuery.of(context).size.height *0.2,
                                    fit: BoxFit.cover,
                                    errorBuilder: (a,b,c){
                                      return Image(
                                        image: const AssetImage('assets/images/error_pic_long.png'),
                                        width: MediaQuery.of(context).size.width *0.92,
                                        height: MediaQuery.of(context).size.height *0.2,
                                        fit: BoxFit.cover,);
                                    },
                                  ),
                                  Positioned(
                                      bottom: 0,
                                      left: 0,
                                      child: Container(
                                        padding: const EdgeInsetsDirectional.all(10),
                                        width:  MediaQuery.of(context).size.width*0.9,
                                        decoration: const BoxDecoration(
                                          color: Color.fromRGBO(0, 0, 0, 0.3),
                                        ),
                                        child:Text("${dataList[index]["title"]}",
                                          style: const TextStyle(fontSize: 16,color: Colors.white),
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,),
                                      )),
                                  if(dataList[index]["isPreview"])
                                  Positioned(
                                      top: 0,
                                      right: 0,
                                      child: Container(
                                        alignment: Alignment.center,
                                        padding: const EdgeInsetsDirectional.all(2),
                                        width:  MediaQuery.of(context).size.width*0.12,
                                        decoration: const BoxDecoration(
                                          image: DecorationImage(
                                              image: AssetImage('assets/icons/yl.png'),
                                            fit: BoxFit.fill, ),
                                        ),
                                        child:const Text('预览',
                                          style:  TextStyle(fontSize: 14,color: Colors.white),),
                                      )),
                                  if(!dataList[index]["isTop"])
                                  Positioned(
                                      top: 0,
                                      right: 0,
                                      child: Container(
                                        alignment: Alignment.center,
                                        padding: const EdgeInsetsDirectional.all(2),
                                        width:  MediaQuery.of(context).size.width*0.12,
                                        decoration: const BoxDecoration(
                                          image: DecorationImage(
                                            image: AssetImage('assets/icons/yl.png'),
                                            fit: BoxFit.fill, ),
                                        ),
                                        // decoration:const BoxDecoration(
                                        //   borderRadius: BorderRadius.all(Radius.circular(3)),
                                        //   color: Color.fromRGBO(255, 152, 57, 0.3),
                                        // ),
                                        child:const Text('置顶',
                                          style:  TextStyle(fontSize: 14,color: Colors.white),),
                                      )),
                                ],
                              ),

                              Padding(
                                padding: const EdgeInsets.all(10),
                                child:Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Padding(
                                      padding: const EdgeInsets.fromLTRB(0, 4, 0, 8),
                                      child:Text("${dataList[index]["summary"]} ",style: const TextStyle(fontSize: 14,color: Colors.grey,fontWeight: FontWeight.w300)),),
                                    const Divider(),
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: const [
                                        Text("查看详情 ",style: TextStyle(color: Colors.black,fontSize: 12)),
                                        Icon(
                                          Icons.arrow_forward_ios,
                                          color: Colors.grey,
                                          size: 16,
                                        ),
                                      ],
                                    )
                                  ],
                                ),
                              )
                            ],
                          ),
                        ),
                        onTap: () {
                          Navigator.of(context).push(MaterialPageRoute(
                            builder: (context) => NoticeXqPage(id: "${dataList[index]["id"]}",),
                          ));
                        },
                      ),
                      if(index==total1-1&&kg&&index!=total-1)
                        _loadMoreWidget(),
                      if (index==total-1)
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children:  [
                            Container(
                              color: const Color(0xF8F8F1FF),
                              width: 50,
                              height: 1,
                            ),
                            const Padding(padding: EdgeInsetsDirectional.only(top: 10,bottom: 60),
                              child:  Text("已无更多公告消息",style: TextStyle(fontSize: 12,color: Colors.grey),) ,),
                            Container(
                              color: const Color(0xF8F8F1FF),
                              width: 50,
                              height: 1,
                            )
                          ],
                        )
                    ],
                  );

                })
          )

      ):
          Text('Tips11'.tr, style: TextStyle(fontSize: 18,color: Colors.black),)
    );

  }
}

import 'package:amkor/components/tabbar_v2.dart';
import 'package:amkor/login.dart';
import 'package:amkor/model/employee.dart';
import 'package:amkor/utils/adapt.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import '../components/routes.dart';

class PersonPage extends StatefulWidget {
  const PersonPage({Key? key}) : super(key: key);

  @override
  _PersonState createState() => _PersonState();
}

class _PersonState extends State<PersonPage> {
  Employee? _employee;

  @override
  void initState() {
    super.initState();
    var userInfo = GetStorage().read<Employee>("userinfo");
    if (!kIsWeb) {
      Adapt.setAndroid();
    }
    setState(() {
      _employee = userInfo;
    });
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.dark);
    return Scaffold(
      appBar: TabberV2(
        title: "title4".tr,
        // title: "个人中心".tr,
        showBack: false,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            _buildTop(),
            // const Divider(
            //   height: 1,
            // ),
            _buildMenus(),
          ],
        ),
      ),
    );
  }

  ///顶部个人信息
  Widget _buildTop() {
    return ListTile(
      leading: const Image(
        image: AssetImage("assets/icons/qr_embedded.png"),
        height: 48,
        width: 48,
      ),
      title: Text(
        _employee?.employeeName ?? "",
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: Row(
        children: [
          const Icon(
            Icons.pages_outlined,
            size: 15,
            color: Color.fromRGBO(136, 136, 136, 1),
          ),
          Text(_employee?.employeeId ?? "")
        ],
      ),
    );
  }

  ///菜单列表
  Widget _buildMenus() {
    return Container(
      width: MediaQuery.of(context).size.width,
      padding: const EdgeInsets.fromLTRB(24, 10, 24, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(padding: EdgeInsets.only(bottom: 10)),
          Text(
            "text4_1".tr,
            style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Color(0xff808080)),
          ),
          _buildMenuItem(MyMenuKeys.xgdz, "text4_2".tr),
          _buildMenuItem(MyMenuKeys.xgmm, "text4_3".tr),
          _buildMenuItem(MyMenuKeys.securityTest, "安全功能测试"),
          // _buildMenuItem(MyMenuKeys.gy, "关于"),
          _buildMenuItem(MyMenuKeys.logout, "text4_4".tr),
          // _buildMenuItem(MyMenuKeys.xgdz, "我的地址"),
          // _buildMenuItem(MyMenuKeys.xgmm, "修改密码"),
          // // _buildMenuItem(MyMenuKeys.gy, "关于"),
          // _buildMenuItem(MyMenuKeys.logout, "退出登录"),
        ],
      ),
    );
  }

  ///退出登陆弹窗
  Future<void> _logOutAppDialog(MyMenuKeys key) {
    return showDialog<void>(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return CupertinoAlertDialog(
            title: Row(
              children: const <Widget>[
                Image(
                  width: 34,
                  height: 34,
                  image: AssetImage("assets/icons/qr_embedded.png"),
                ),
                Padding(
                    padding: EdgeInsets.fromLTRB(10.0, 0.0, 10.0, 0.0),
                    child: Text("amkor"))
              ],
            ),
            content: const Text(
              "您正在退出app请确认。 ",
              style: TextStyle(fontSize: 14),
            ),
            actions: <Widget>[
              CupertinoDialogAction(
                child: const Text("取消"),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
              CupertinoDialogAction(
                child: const Text("确定"),
                onPressed: () {
                  _navTo(key);
                  Fluttertoast.showToast(msg: "退出登录成功");
                },
              )
            ],
          );
        });
  }

  Widget _buildMenuItem(MyMenuKeys key, String desc) {
    return InkWell(
      onTap: () {
        if (key == MyMenuKeys.logout) {
          _logOutAppDialog(key);
        } else {
          _navTo(key);
        }
      },
      child: Container(
        key: UniqueKey(),
        padding: const EdgeInsets.only(top: 15, bottom: 15),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Image(
                  image: AssetImage("assets/icons/my_${key.name}.png"),
                  height: 15,
                  width: 15,
                  // fit: BoxFit.scaleDown,
                ),
                const Padding(padding: EdgeInsets.only(right: 10)),
                Text(
                  desc,
                  style: const TextStyle(
                    fontSize: 14,
                  ),
                ),
              ],
            ),
            const Icon(
              Icons.chevron_right,
              color: Color.fromRGBO(187, 187, 187, 1),
              size: 25,
            )
          ],
        ),
      ),
    );
  }

  void _navTo(MyMenuKeys key) {
    switch (key) {
      case MyMenuKeys.ys:
        //
        break;
      case MyMenuKeys.xgdz:
        Navigator.of(context).pushNamed(RouteNames.personAddress);
        break;
      case MyMenuKeys.xgmm:
        Navigator.of(context).pushNamed(RouteNames.updatePwd);
        break;
      case MyMenuKeys.gy:
        Navigator.of(context).pushNamed(RouteNames.about);
        break;
      case MyMenuKeys.xy:
        // 处理新增的枚举值
        break;
      case MyMenuKeys.securityTest:
        Navigator.of(context).pushNamed(RouteNames.securityTestPage);
        break;
      case MyMenuKeys.logout:
        Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(
                builder: (BuildContext context) => const LoginPage()),
            (route) => false);
        return;
    }
  }
}

enum MyMenuKeys { xy, ys, xgmm, logout, xgdz, gy, securityTest }

import 'dart:io';
import 'package:amkor/Notice_xq.dart';
import 'package:amkor/components/tabbar.dart';
import 'package:amkor/draggablegridview/flutter_draggable_gridview.dart';
import 'package:amkor/model/MyBinding.dart';
import 'package:amkor/services/employeeService.dart';
import 'package:amkor/utils/adapt.dart';
import 'package:amkor/utils/crypt.dart';
import 'package:amkor/utils/enums.dart';
import 'package:amkor/workPages/webview.dart';
import 'package:external_app_launcher/external_app_launcher.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
// import 'package:outline_search_bar/outline_search_bar.dart';  // 已移除，使用 Flutter 内置 SearchBar
import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';
import 'package:url_launcher/url_launcher.dart';
import '../components/routes.dart';
import '../services/permission/main_page.dart';

class HomePage extends StatefulWidget {
  const HomePage({Key? key}) : super(key: key);
  @override
  _HomeState createState() => _HomeState();
}

class _HomeState extends State<HomePage> {
  FocusNode focusNode = FocusNode();
  var newNotice;
  var newNoticeId;
  int? blankBlock;
  var homeData = GetStorage().read("cyl");
  var homeData1 = GetStorage().read("spl");
  var homeData2 = GetStorage().read("COVID_19");
  var homeData3 = GetStorage().read("dsf");
  var homeDataRe = GetStorage().read("class_0");
  var homeDataOther = GetStorage().read("class_Other");
  var homePage = GetStorage().read("top1");

  final UIMessage c = Get.find();

  _getAppNewAnnouncement() async {
    newNotice = await AnnouncementApp().GetAppNewAnnouncement();
    newNoticeId = newNotice.data["id"];
    setState(() {});
  }

  _getNew() async {
    if (GetStorage().read('unreadNoticeId') == null) {
      GetStorage().write('unreadNoticeId', newNoticeId);
    } else if (newNoticeId > GetStorage().read('unreadNoticeId')) {
      ///读取最近100条公告
      var _read = await AnnouncementApp().PostAnnouncementList(1, 100);
      var unreadNotice = [];
      var unreadNoticeId = GetStorage().read('unreadNoticeId');
      _read!.data.map((e) {
        if (e["id"] > unreadNoticeId) {
          unreadNotice.add(e);
        }
      }).toList();
      GetStorage().write("unreadNotice", unreadNotice);
    }
    print("----${GetStorage().read('unreadNoticeId')}-----new$newNoticeId---");
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    // GetStorage().remove('unreadNoticeId');
    // GetStorage().write('unreadNoticeId',139);
    _getAppNewAnnouncement().then((_) {
      _getNew().then((_) => c.increment());
    });

    if (!kIsWeb) {
      Adapt.setAndroid();
    }
    refreshStatus();
  }

  @override
  Widget build(BuildContext context) {
    var w = MediaQuery.of(context).size.width;
    return Scaffold(
      body: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          refreshStatus();
        },
        child: SingleChildScrollView(
          child: Stack(
            alignment: Alignment.topCenter,
            children: [
              Image(
                image: const AssetImage("assets/images/shanghai.jpg"),
                // image: const AssetImage("assets/images/ta_bg2.png"), // 首页头图片
                width: MediaQuery.of(context).size.width,
                fit: BoxFit.fitHeight,
              ),
              SizedBox(
                ///高度适配web
                width: kIsWeb &&
                        (defaultTargetPlatform == TargetPlatform.macOS ||
                            defaultTargetPlatform == TargetPlatform.windows)
                    ? MediaQuery.of(context).size.width * 0.24
                    : w,
                child: Column(
                  children: [
                    Tabbar(),
                    _buildsyxx(),
                    // _buildSearchBar(),
                    homePage.length > 0 ? _buildTopBtn() : SizedBox(),
                    const Padding(
                      padding: EdgeInsets.all(4.0),
                    ),
                    // newNotice!=null?_buildNewNotice():const SizedBox(),
                    roles == '1' && newNotice != null
                        ? _buildNewNotice()
                        : const SizedBox(), // Leo 2023-12-25
                    const Padding(
                      padding: EdgeInsets.all(4.0),
                    ),
                    // _buildCOVID_19(),
                    // const Padding(padding: EdgeInsets.all(4.0),),
                    // a == 'HR' ? _buildWorkbench(): SizedBox(), //权限控制 By Leo
                    homeData.length != 1 ? _buildWorkbench() : SizedBox(),
                    const Padding(
                      padding: EdgeInsets.all(4.0),
                    ),
                    // _buildApproval(),
                    // const Padding(padding: EdgeInsets.all(4.0),),
                    homeData3.length > 1
                        ? _buildThirdParty()
                        : const SizedBox(),
                    const Padding(
                      padding: EdgeInsets.only(bottom: 16),
                    ),
                    homeDataOther.length > 1 ? _buildOther() : const SizedBox(),
                    Padding(
                      padding: EdgeInsets.only(
                          bottom: MediaQuery.of(context).padding.bottom),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildsyxx() {
    return Padding(
        padding: EdgeInsets.fromLTRB(
            Adapt.widthPt(24), 0, Adapt.widthPt(24), Adapt.widthPt(24)),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Hi, ${GetStorage().read('employee')['employeeName']}',
                    style: TextStyle(
                        color: Colors.white,
                        fontSize: Adapt.widthPt(22),
                        fontWeight: FontWeight.w600)),
                const Padding(
                  padding: EdgeInsets.all(4.0),
                ),
                Text('welcome Login'.tr,
                    style: TextStyle(
                        color: Colors.white, fontSize: Adapt.widthPt(14)))
                // Text('欢迎登录',style: TextStyle(color: Colors.white,fontSize:Adapt.widthPt(14)))
              ],
            ),
          ],
        ));
  }

  Widget _buildTopBtn() {
    double w = MediaQuery.of(context).size.width;
    return Container(
      width: w * 0.92,
      padding: const EdgeInsets.all(8),
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(10)),
        color: Colors.white,
      ),
      child: Row(
        mainAxisAlignment: homePage.length == 1
            ? MainAxisAlignment.start
            : MainAxisAlignment.spaceEvenly,
        children: <Widget>[
          for (int i = 0; i < homePage.length; i++)
            _buildTopBtnItem(homePage[i][1], homePage[i][0], homePage[i][2]),
          // _buildTopBtnItem("sys", "扫一扫".tr, RouteNames.uploadLocation),
          // _buildTopBtnItem("jcm", "进厂码".tr, RouteNames.showInCode),
          // _buildTopBtnItem("jc", "付款码".tr, "/KongPage"),
          // _buildTopBtnItem("gp", "员工码".tr, RouteNames.showPassportCode),
        ],
      ),
    );
  }

  ///首页公告
  Widget _buildNewNotice() {
    return GestureDetector(
      onTap: () {
        _getAppNewAnnouncement().then((e) {
          if (newNotice.data != null) {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => NoticeXqPage(
                  id: newNotice?.data['id'].toString(),
                  direction: Direction.newestNotice,
                ),
              ),
            );
          } else {
            Fluttertoast.cancel();
            Fluttertoast.showToast(msg: "暂无最新公告");
          }
        });
      },
      child: Container(
        clipBehavior: Clip.hardEdge,
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(6)),
          color: Colors.white,
        ),
        width: MediaQuery.of(context).size.width * 0.92,
        height: MediaQuery.of(context).size.height * 0.12,
        alignment: Alignment.center,
        child: Stack(children: [
          Positioned(
            top: 0,
            left: 0,
            child:
                // Transform.scale(
                //   scale: 1, // 缩放比例
                //   child:
                // ),
                Image.network(
              newNotice?.data['cover'].replaceAll("\\", "/"),
              width: MediaQuery.of(context).size.width * 0.92,
              // height: MediaQuery.of(context).size.height *0.2,
              fit: BoxFit.cover,
              errorBuilder: (a, b, c) {
                return Image(
                  image: const AssetImage('assets/images/error_pic_long.png'),
                  width: MediaQuery.of(context).size.width * 0.92,
                  height: MediaQuery.of(context).size.height * 0.12,
                  fit: BoxFit.cover,
                );
              },
            ),
          ),
          Positioned(
            bottom: 0,
            left: 0,
            child: Container(
              alignment: Alignment.center,
              width: MediaQuery.of(context).size.width * 0.92,
              height: MediaQuery.of(context).size.height * 0.044,
              padding: const EdgeInsetsDirectional.all(4),
              decoration: const BoxDecoration(
                color: Color.fromRGBO(60, 77, 102, 0.5),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    flex: 8,
                    child: Text(
                      "最新消息:${newNotice != null && newNotice.data != null ? newNotice?.data['title'] : ''}",
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: Adapt.widthPt(12),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  // Expanded(
                  //     flex: 2,
                  //     child: GestureDetector(
                  //     onTap: (){
                  //       // launchUrl(Uri.parse('alipays://platformapi/startapp?appId=2021001132656455'));
                  //     },
                  //     child: Container(
                  //       padding:const EdgeInsetsDirectional.all(2),
                  //       alignment: Alignment.center,
                  //       decoration: const BoxDecoration(
                  //         borderRadius: BorderRadius.all(Radius.circular(8)),
                  //         color: Colors.white,
                  //       ),
                  //       child: Text("立即前往",style: TextStyle(color:const Color.fromRGBO(151, 151, 151, 1),fontSize: Adapt.widthPt(12))),
                  //     )
                  // ))
                ],
              ),
            ),
          ),
        ]),
      ),
    );
  }

  Widget _buildTopBtnItem(String icon, String desc, String key) {
    return Padding(
      padding:
          EdgeInsets.only(right: Adapt.widthPt(14), left: Adapt.widthPt(14)),
      child: GestureDetector(
        child: Column(
          children: [
            const Padding(padding: EdgeInsets.only(top: 10)),
            Image(
              image: AssetImage("assets/icons/work_${icon}_t.png"),
              width: Adapt.widthPt(40),
              fit: BoxFit.fitWidth,
            ),
            const Padding(padding: EdgeInsets.only(top: 0)),
            Text(
              desc.tr,
              style: const TextStyle(
                color: Colors.black,
              ),
            ),
            const Padding(padding: EdgeInsets.only(top: 4)),
          ],
        ),
        onTap: () => _navigatorPush(key, desc.tr),
      ),
    );
  }

  //COVID-19类
  Widget _buildCOVID_19() {
    double w = MediaQuery.of(context).size.width;
    return Container(
      width: w * 0.92,
      decoration: const BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(6)),
          color: Colors.white),
      padding: EdgeInsets.only(
          top: Adapt.widthPt(10),
          bottom: Adapt.widthPt(10),
          left: Adapt.widthPt(0),
          right: Adapt.widthPt(0)),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.only(left: Adapt.widthPt(12)),
            alignment: Alignment.bottomLeft,
            child: Text(
              "防疫类".tr,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Container(
            padding: EdgeInsets.only(top: Adapt.widthPt(16)),
            child: AnimationLimiter(
              child: GridView.count(
                shrinkWrap: true,
                //设置滚动方向
                scrollDirection: Axis.vertical,
                primary: false,
                //设置列数
                crossAxisCount: 4,
                //设置内边距
                padding: const EdgeInsets.all(0),
                //设置横向间距
                crossAxisSpacing: 10,
                //设置主轴间距
                mainAxisSpacing: 10,
                children:
                    // List.generate(
                    //   homeData1.length,
                    //       (int index) {
                    //     return AnimationConfiguration.staggeredGrid(
                    //       position: index,
                    //       duration: const Duration(milliseconds: 375),
                    //       columnCount: 4,
                    //       child: ScaleAnimation(
                    //         child: FadeInAnimation(
                    //           child:
                    //           _buildWorkbenchItem(
                    //               index,
                    //               homeData1
                    //           ),
                    //         ),
                    //       ),
                    //     );
                    //   },
                    // ),
                    [
                  for (int j = 0; j < homeData1.length; j++)
                    _buildWorkbenchItem(j, homeData1),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }

  ///HR类
  Widget _buildWorkbench() {
    double w = MediaQuery.of(context).size.width;
    return Container(
      width: w * 0.92,
      decoration: const BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(6)),
          color: Colors.white),
      padding: EdgeInsets.only(
          top: Adapt.widthPt(10),
          bottom: Adapt.widthPt(10),
          left: Adapt.widthPt(0),
          right: Adapt.widthPt(0)),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.only(left: Adapt.widthPt(12)),
            alignment: Alignment.bottomLeft,
            child: Text(
              "title1_1".tr,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Container(
            padding: EdgeInsets.only(top: Adapt.widthPt(20)),
            child: GridView.count(
              shrinkWrap: true,
              scrollDirection: Axis.vertical,
              primary: false,
              crossAxisCount: 4,
              padding: const EdgeInsets.all(0),
              crossAxisSpacing: 10,
              mainAxisSpacing: 10,
              children: [
                for (int j = 0; j < homeData.length; j++)
                  _buildWorkbenchItem(j, homeData),
              ],
            ),

            // DraggableGridViewBuilder(
            //   shrinkWrap:true,
            //   primary:false,
            //   padding:const EdgeInsets.all(0),
            //   gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            //     crossAxisCount: 4,
            //     crossAxisSpacing: 10,
            //     mainAxisSpacing: 10,
            //     // childAspectRatio: MediaQuery.of(context).size.width / (MediaQuery.of(context).size.height / 3),
            //   ),
            //   children: [
            //     for(int j=0;j<homeData.length;j++)
            //     DraggableGridItem(
            //       child:_buildWorkbenchItem(j, homeData),
            //       isDraggable: true,
            //       dragCallback: (context, isDragging) {
            //       },
            //     ),
            //   ],
            //   isOnlyLongPress: false,
            //   dragCompletion: (List<DraggableGridItem> list, int beforeIndex, int afterIndex) {
            //     print( 'onDragAccept: $beforeIndex -> $afterIndex');
            //   },
            //   dragFeedback: (List<DraggableGridItem> list, int index) {
            //     return Container(
            //       child: list[index].child,
            //     );
            //   },
            //   dragPlaceHolder: (List<DraggableGridItem> list, int index) {
            //     return PlaceHolderWidget(
            //       child: Container(
            //         color: Colors.white,
            //       ),
            //     );
            //   },
            // ),
          )
        ],
      ),
    );
  }

  //审批类
  Widget _buildApproval() {
    double w = MediaQuery.of(context).size.width;

    return Container(
      width: w * 0.92,
      decoration: const BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(6)),
          color: Colors.white),
      padding: EdgeInsets.only(
          top: Adapt.widthPt(10),
          bottom: Adapt.widthPt(10),
          left: Adapt.widthPt(0),
          right: Adapt.widthPt(0)),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.only(left: Adapt.widthPt(12)),
            alignment: Alignment.bottomLeft,
            child: Text(
              "审批类".tr,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Container(
            padding: EdgeInsets.only(top: Adapt.widthPt(20)),
            child: GridView.count(
              shrinkWrap: true,
              scrollDirection: Axis.vertical,
              primary: false,
              crossAxisCount: 4,
              padding: const EdgeInsets.all(0),
              crossAxisSpacing: 10,
              mainAxisSpacing: 10,
              children: [
                for (int j = 0; j < homeData2.length; j++)
                  _buildWorkbenchItem(j, homeData2),
              ],
            ),
          )
        ],
      ),
    );
  }

  //第三方应用
  Widget _buildThirdParty() {
    double w = MediaQuery.of(context).size.width;

    return Container(
      width: w * 0.92,
      decoration: const BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(6)),
          color: Colors.white),
      padding: EdgeInsets.only(
          top: Adapt.widthPt(10),
          bottom: Adapt.widthPt(10),
          left: Adapt.widthPt(0),
          right: Adapt.widthPt(0)),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.only(left: Adapt.widthPt(12)),
            alignment: Alignment.bottomLeft,
            child: Text(
              "第三方应用".tr,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Container(
            padding: EdgeInsets.only(top: Adapt.widthPt(20)),
            child: GridView.count(
              shrinkWrap: true,
              scrollDirection: Axis.vertical,
              primary: false,
              crossAxisCount: 4,
              padding: const EdgeInsets.all(0),
              crossAxisSpacing: 10,
              mainAxisSpacing: 10,
              children: [
                for (int j = 0; j < homeData3.length; j++)
                  _buildWorkbenchItem(j, homeData3),
              ],
            ),
          )
        ],
      ),
    );
  }

  //其他类
  Widget _buildOther() {
    double w = MediaQuery.of(context).size.width;

    return Container(
      width: w * 0.92,
      decoration: const BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(6)),
          color: Colors.white),
      padding: EdgeInsets.only(
          top: Adapt.widthPt(10),
          bottom: Adapt.widthPt(10),
          left: Adapt.widthPt(0),
          right: Adapt.widthPt(0)),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.only(left: Adapt.widthPt(12)),
            alignment: Alignment.bottomLeft,
            child: Text(
              "其他类".tr,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Container(
            padding: EdgeInsets.only(top: Adapt.widthPt(20)),
            child: GridView.count(
              shrinkWrap: true,
              scrollDirection: Axis.vertical,
              primary: false,
              crossAxisCount: 4,
              padding: const EdgeInsets.all(0),
              crossAxisSpacing: 10,
              mainAxisSpacing: 10,
              children: [
                for (int j = 0; j < homeDataOther.length; j++)
                  _buildWorkbenchItem(j, homeDataOther),
              ],
            ),
          )
        ],
      ),
    );
  }

  //普通
  Widget _buildWorkbenchItems(int xl, List<dynamic> data) {
    var img = "assets/icons/${data[xl][1]}";
    return Padding(
      padding: EdgeInsets.only(left: Adapt.widthPt(1.5)),
      child: GestureDetector(
        onTap: data[xl][3] == "true"
            ? () => _navigatorPush(data[xl][2], data[xl][0])
            : () {
                setState(() {
                  if (data[xl][0] != '') {
                    homeDataRe.add(data[xl]);
                    data.removeAt(xl);

                    ///本地缓存
                    writeCache();
                  }
                });
              },
        onLongPress: () {
          setState(() {
            if (data[xl][0] != '') {
              data[xl][3] = "false";
            }
          });
        },
        child: Column(
          children: [
            Stack(
              children: [
                Image(
                  image: AssetImage(img),
                  width: Adapt.widthPt(50),
                  height: Adapt.widthPt(50),
                ),
                if (data[xl][3] == "false")
                  Image(
                    image: const AssetImage('assets/icons/delete_j.png'),
                    width: Adapt.widthPt(50),
                    height: Adapt.widthPt(50),
                  ),
              ],
            ),
            Container(
              alignment: Alignment.center,
              padding: const EdgeInsets.only(top: 5),
              width: Adapt.widthPt(84),
              child: Text(
                data[xl][0],
                style: TextStyle(
                  color: Colors.black,
                  fontSize: Adapt.widthPt(12),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  //拖拽
  Widget _buildWorkbenchItem(int xl, List<dynamic> data) {
    var img = "assets/icons/${data[xl][1]}";

    return LongPressDraggable(
      axis: data[xl][0] == '' ? Axis.horizontal : null,
      data: [data, xl],
      feedback: Image(
        image: AssetImage(img),
        width: Adapt.widthPt(50),
        height: Adapt.widthPt(50),
      ),
      //拖动时候调用
      onDragStarted: () {
        data.add(["", "", "", "", ""]);
        if (data[xl + 1][0] != '') {
          data[xl + 1][3] = "false";
        }

        ///
        homeDataRe.add(data[xl + 1]);
        data.removeAt(xl + 1);

        // if(homeData1.length==0) {
        //   homeData1.add(["","add_pic1.png","/addFunction","true","COVID-19"],);
        // }else{
        //   print('----9999999-------${homeData1[0].contains("")}');
        //   if(homeData1[0].contains("")) homeData1.removeAt(homeData1.indexWhere((element) => element[0] == ''));
        // }
        setState(() {});
        print('$data ---3---');
      },
      feedbackOffset: Offset.zero,
      onDragEnd: (detail) {
        print("onDraEnd:${detail.offset}");
        //放手时候
      },
      // 未拖动到DragTarget控件上时回调
      onDraggableCanceled: (Velocity velocity, Offset offset) {
        data.removeLast();
        if (xl == data.length) {
          data.insert(xl, homeDataRe[homeDataRe.length - 1]);
          homeDataRe.removeLast();
        } else {
          data.insert(xl, homeDataRe[homeDataRe.length - 1]);
          homeDataRe.removeLast();
        }
        setState(() {});
      },
      // 拖动到DragTarget控件上时回调
      onDragCompleted: () {},
      //当拖拽的时候就展示空
      childWhenDragging: null,
      ignoringFeedbackSemantics: false,
      child: DragTarget<List<dynamic>>(
        builder: (context, receiveData, rejects) {
          return Padding(
              padding: EdgeInsets.only(left: Adapt.widthPt(1.5)),
              child: data[xl][4] != ''
                  ? GestureDetector(
                      onTap: data[xl][3] == "true"
                          ? () => _navigatorPush(data[xl][2], data[xl][0])
                          : () {
                              setState(() {
                                if (data[xl][0] != '') {
                                  homeDataRe.add(data[xl]);
                                  data.removeAt(xl);
                                }
                              });

                              ///本地缓存
                              writeCache();
                            },
                      child: Column(
                        children: [
                          Stack(
                            children: [
                              Image(
                                image: AssetImage(img),
                                width: Adapt.widthPt(50),
                                height: Adapt.widthPt(50),
                              ),
                              if (data[xl][3] == "false")
                                Image(
                                  image: const AssetImage(
                                      'assets/icons/delete_j.png'),
                                  width: Adapt.widthPt(50),
                                  height: Adapt.widthPt(50),
                                ),
                            ],
                          ),
                          Container(
                            alignment: Alignment.center,
                            padding: const EdgeInsets.only(top: 5),
                            width: Adapt.widthPt(84),
                            child: Text(
                              // 'xxxx',
                              data[xl][0].toString().tr, //Leo APP 图标文字
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: Adapt.widthPt(12),
                              ),
                            ),
                          )
                        ],
                      ),
                    )
                  : Container(
                      alignment: Alignment.center,
                      padding: const EdgeInsets.only(top: 5),
                      width: Adapt.widthPt(84),
                      height: Adapt.widthPt(50),
                    ));
        },
        // 手指拖着一个widget从另一个widget头上滑走时会调用
        onLeave: (receiveData) {
          data.removeAt(blankBlock!);
          setState(() {});
          print('$data ---1---');
        },
        // 松手时，是否需要将数据给这个widget，因为需要在拖动时改变UI，所以在这里直接修改数据源
        onWillAccept: (receiveData) {
          data.insert(xl, ["", "", "", "", ""]);
          blankBlock = xl;
          setState(() {});
          return true;
        },
        // 松手时，如果onWillAccept返回true，那么就会调用
        onAccept: (receiveData) {
          // TODO 松手时交换数据并刷新 UI
          receiveData[0].removeLast();
          data.removeAt(blankBlock!);
          if (xl == data.length) {
            data.insert(xl, homeDataRe[homeDataRe.length - 1]);
            homeDataRe.removeLast();
          } else {
            data.insert(xl, homeDataRe[homeDataRe.length - 1]);
            homeDataRe.removeLast();
          }

          ///本地缓存
          writeCache();
          setState(() {});
        },
      ),
    );
  }

  Future<void> _navigatorPush(String key, String title) async {
    if (!mounted) return;
    if (key == '/KongPage') {
      final UIMessage controller = Get.find();
      // controller.initWebSocket();

      // controller.disposeWebSocket();

      ///发布新公告
      // await Announcement().addAnnouncement();

      Fluttertoast.cancel();
      Fluttertoast.showToast(msg: "此功能开发建设中");
    } else if (key.split("||")[0] == 'URL') {
      if (Platform.isIOS) {
        _launchUrl(key.split("||")[1]);
      } else {
        await LaunchApp.openApp(
          androidPackageName: key.split("||")[2],
        );
      }
    } else if (key.split("||")[0] == '报表') {
      String employeeId = Uri.encodeComponent(
          MyCrypt.aesEncrypt(GetStorage().read('employee')['employeeId']));
      String mobile = Uri.encodeComponent(
          MyCrypt.aesEncrypt(GetStorage().read('employee')['mobile']));
      String employeeId2 = Uri.encodeComponent(
          MyCrypt.encryptData(GetStorage().read('employee')['employeeId']));

      print(
          "---->${key.split("||")[1]}?employeeId=$employeeId2&mobile=$mobile");
      if (mounted) {
        if (title == 'Wallet' || title == '钱包') {
          Navigator.push(context, MaterialPageRoute(builder: (context) {
            return WebviewPage(
                title: title,
                url: "${key.split("||")[1]}?employeeId=$employeeId2");
          }));
        } else if (title == 'DL绩效考核') {
          print("----DL绩效考核---->${key.split("||")[1]}?employeeId=$employeeId2");
          Navigator.push(context, MaterialPageRoute(builder: (context) {
            return WebviewPage(
                title: title,
                url: "${key.split("||")[1]}?employeeId=$employeeId2");
          }));
        } else {
          Navigator.push(context, MaterialPageRoute(builder: (context) {
            return WebviewPage(
                title: title,
                url:
                    "${key.split("||")[1]}?employeeId=$employeeId&mobile=$mobile");
          }));
        }
      }
    } else {
      Navigator.of(context).pushNamed(key).then((value) {
        if (!kIsWeb) {
          Adapt.setAndroid(isDark: false);
        }
        refreshStatus();
      });
    }
  }

  Future<void> _launchUrl(String key) async {
    try {
      if (!await launchUrl(Uri.parse(key))) {
        Fluttertoast.cancel();
        Fluttertoast.showToast(msg: "请先行安装此应用");
        throw Exception('Could not launch ${Uri.parse(key)}');
      }
    } on PlatformException catch (e) {
      if (e.code == 'ERROR_INVALID_URL') {
        print('Invalid URL: ${e.toString()}');
      } else if (e.code == 'ERROR_LAUNCH_FAILED') {
        print('Failed to launch URL: ${e.toString()}');
      } else if (e.code == 'ACTIVITY_NOT_FOUND') {
        Fluttertoast.cancel();
        Fluttertoast.showToast(msg: "请先行安装此应用");
        print('Unknown error: ${e.toString()}');
      }
    }
  }

  //刷新状态
  void refreshStatus() {
    setState(() {
      for (var e in homeDataRe) {
        e[3] = 'true';
      }
      for (var e in homeData) {
        e[3] = 'true';
      }
      for (var e in homeData1) {
        e[3] = 'true';
      }
      for (var e in homeData2) {
        e[3] = 'true';
      }
      for (var e in homeData3) {
        e[3] = 'true';
      }
      for (var e in homeDataOther) {
        e[3] = 'true';
      }
    });
  }

  void writeCache() {
    //写入本地缓存
    var s = GetStorage();
    s.write('cyl', homeData);
    s.write('spl', homeData1);
    s.write('COVID_19', homeData2);
    s.write('dsf', homeData3);
    s.write('class_0', homeDataRe);
    s.write('class_Other', homeDataOther);
  }
}

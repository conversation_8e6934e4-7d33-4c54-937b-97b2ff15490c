class Employee {
  int? id;
  String? employeeId;
  String? employeeName;
  String? cscText;
  String? detailAddress;
  String liveType = "";
  String? unitName;
  String? mobile;
  Employee({this.employeeId, this.employeeName});

  Map<String, dynamic> toJson() {
    return {
      "id": id,
      "employeeId": employeeId,
      "employeeName": employeeName,
      "cscText": cscText,
      "detailAddress": detailAddress,
      "unitName": unitName,
      "liveType": liveType,
      "mobile": mobile
    };
  }

  Employee.fromJson(Map<String, dynamic> json)
      : id = json["id"],
        employeeId = json["employeeId"],
        cscText = json["cscText"],
        detailAddress = json["detailAddress"],
        liveType = json["liveType"],
        unitName = json["unitName"],
        employeeName = json["employeeName"],
        mobile= json["mobile"];
}


import 'dart:async';
import 'dart:convert';
import 'package:amkor/services/employeeService.dart';

import 'package:amkor/services/sendLocalNotification.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:web_socket_channel/io.dart';

class MyBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => ShuttleBusData());
    Get.lazyPut(() => UIMessage());
  }
}

class ShuttleBusData extends GetxController{
  ShuttleBusData(){
    busType.value="";
  }
  var data=[].obs;
  RxBool loading=false.obs;
  RxList<bool> line=<bool>[].obs;
  RxString busType="".obs;
  // RxList<bool> tj=<bool>[].obs;
  List<dynamic> indexData=<dynamic>[].obs;
  Future<void> initData(String search,String busType) async{
    loading.value=true;
    var r=await BusApp().GetBusLines(search,busType);
    // print("===========================${r!.data[0]}");
    await Future.delayed(const Duration(milliseconds: 100));
    data.value=r!.data;
    line.value=[];
    // tj.value=[];
    for(int i=0;i<data.length;i++){
      line.add(!data[i]["isShowDown"]);
      // tj.add(true);
    }
    loading.value=false;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      update();
    });
  }
  @override
  void onInit() {
    initData("","");
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    ever(data, (_) {
      if (data!=[]) {
        update();
      }
    });
  }

}


class UIMessage extends GetxController{

  var newNotice;
  var newNoticeId;
  late IOWebSocketChannel channel ;
  var count=0.obs;
  void increment(){
    count.value=GetStorage().read('unreadNotice')==null?0:GetStorage().read('unreadNotice').length;
    ///更新角标
    // FlutterAppBadger.updateBadgeCount(count.value);
    print("--------------------$count");
  }



  //初始化连接webSocket
  initWebSocket() {
    channel = IOWebSocketChannel.connect('ws://10.20.1.131:8088/ws');
    channel.stream.listen((message) {
      print("====接收到消息有最新公告=======$message");
      _getAppNewAnnouncement().then((_){
        _getNew().then((_){
          increment();
          LocalNotification().showNotification(id:newNoticeId,title: newNotice.data["title"],body: newNotice.data["author"],payload:"$newNoticeId" );
        });
      });
      update();
      },
        onError: (error) {
      print('Error: $error');
    },
        onDone: () {
    print('WebSocket 断开');
    });

  }


  ///获取最新公告
  _getAppNewAnnouncement() async{
    newNotice=await AnnouncementApp().GetAppNewAnnouncement();
    newNoticeId=newNotice.data["id"];
  }
  _getNew() async{
    if(GetStorage().read('unreadNoticeId')==null){
      GetStorage().write('unreadNoticeId',newNoticeId);
    }else if(newNoticeId>GetStorage().read('unreadNoticeId')) {
      ///读取最近100条公告
      var _read = await AnnouncementApp().PostAnnouncementList(1, 100);
      var unreadNotice = [];
      var unreadNoticeId = GetStorage().read('unreadNoticeId');
      _read!.data.map((e) {
        if (e["id"] > unreadNoticeId) {
          unreadNotice.add(e);
        }
      }).toList();
      GetStorage().write("unreadNotice", unreadNotice);
    }
    print("----${GetStorage().read('unreadNoticeId')}-----------new2$newNoticeId-----------------------");
  }

  addMessage({required Map<String, dynamic> value}) {

    var msgJson=jsonEncode(value);
    channel.sink.add(msgJson);
  }

  disposeWebSocket() {
    // 关闭StreamController
    channel.sink.close();
  }

}
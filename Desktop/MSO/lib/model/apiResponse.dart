class ApiResponse<T> {
  late int code;
  late String message;
  late bool success;
  dynamic data;

  ApiResponse.fromJson(Map<String, dynamic> json) {
    code = json["code"];
    message = json["message"];
    success = json["success"];
    if (json.containsKey("data")) data = json["data"];
  }

  @override
  String toString() {
    return '{"code":$code, "message":$message, "success":$success, "data":$data}';
  }
}
class ApiResponsegg<T> {
  late int code;
  late String message;
  late bool success;
  dynamic data;
  late int total;

  ApiResponsegg.fromJson(Map<String, dynamic> json) {
    code = json["code"];
    message = json["message"];
    success = json["success"];
    if (json.containsKey("data")) data = json["data"];
    total = json["total"];
  }
  @override
  String toString() {
    return '{"code":$code, "message":$message, "success":$success, "data":$data,total":$total,}';
  }
}

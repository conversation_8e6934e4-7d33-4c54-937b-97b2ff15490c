import 'package:amkor/utils/Adapt.dart';
import 'package:flutter/material.dart';
import 'package:photo_view/photo_view_gallery.dart';

typedef PageChanged = void Function(int index);
class PhotoPreview extends StatefulWidget {
  final List? galleryItems; //图片列表
  final int? defaultImage; //默认第几张
  final PageChanged? pageChanged; //切换图片回调
  final Axis direction; //图片查看方向
  BoxDecoration? decoration;//背景设计

  PhotoPreview(
      {this.galleryItems,
        this.defaultImage = 0,
        this.pageChanged,
        this.direction = Axis.horizontal,
        this.decoration,
      })
      : assert(galleryItems != null);
  @override
  State<PhotoPreview> createState() => _PhotoPreviewState();
}

class _PhotoPreviewState extends State<PhotoPreview> {
  int? tempSelect;
  @override
  void initState() {
    super.initState();
    // TODO: implement initState
    Adapt.setAndroid();
    tempSelect=widget.defaultImage!+1;
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:Colors.black ,
      body:GestureDetector(
        onTap: (){

          // Navigator.of(context).pop();
          print("===111===========$tempSelect");
          Navigator.pop(context,tempSelect!-1);

        },
        child: Stack(
          children: [
            PhotoViewGallery.builder(
                scrollPhysics: const BouncingScrollPhysics(),
                builder: (BuildContext context, int index) {
                  return PhotoViewGalleryPageOptions(
                    imageProvider: NetworkImage(widget.galleryItems![index]),
                  );
                },
                scrollDirection: widget.direction,
                itemCount: widget.galleryItems?.length,
                backgroundDecoration:const BoxDecoration(color: Colors.transparent),
                pageController: PageController(initialPage: widget.defaultImage!),
                onPageChanged: (index) =>setState(() {
                  tempSelect=index+1;
                  if( widget.pageChanged!=null)
                  {widget.pageChanged!(index);}
                })),
            Positioned(
              right: 18,
              top: 18,
              child: Text('$tempSelect/${widget.galleryItems?.length}',style:const TextStyle(color:Colors.white),),
            )
          ],
        ),
      ),


    );
  }
}
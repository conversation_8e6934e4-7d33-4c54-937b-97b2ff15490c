import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:amkor/components/tabbar.dart';
import 'package:amkor/login.dart';
import 'package:amkor/services/authService.dart';
import 'package:amkor/services/dto/loginDto.dart';
import 'package:amkor/services/employeeService.dart';
import 'package:amkor/services/permission/main_page.dart';
import 'package:amkor/utils/adapt.dart';
import 'package:amkor/utils/addj.dart';
import 'package:amkor/utils/crypt.dart';
import 'package:amkor/utils/reg.dart';
// import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:pinput/pinput.dart';

import '../components/tabbar_v2.dart';
import '../utils/colors.dart';
import 'components/routes.dart';

class DeviceRelogin extends StatefulWidget {
  DeviceRelogin(
      {Key? key,
      required this.emp_id,
      required this.emp_phonenumber,
      required this.platform,
      required this.version,
      required this.lang,
      required this.uuid,
      required this.freeLogin,
      required this.encryptPwd})
      : super(key: key);
  String emp_id;
  String emp_phonenumber;
  String platform;
  String version;
  String lang;
  String uuid;
  bool freeLogin;
  String encryptPwd;

  @override
  _ReloginState createState() => _ReloginState();
}

class _ReloginState extends State<DeviceRelogin> {
  TextEditingController _usernameCtrl = TextEditingController();
  //工号
  TextEditingController _JobController = TextEditingController();

  //手机号
  TextEditingController _Telephonetroller = TextEditingController();
  bool _isLoading = false;
  //工号
  String _Job = "";

  //手机号
  String _Telephone = "";

  var validationCode;

  ///
  //FocusNode
  FocusNode jkNode = new FocusNode();

  GlobalKey _formKey = GlobalKey<FormState>();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: TabberV2(
          title: "新设备登陆",
          // title: "forget password".tr,

          // title: "忘记密码?".tr,
          showBack: true,
        ),
        resizeToAvoidBottomInset: false,
        body: Center(
          child: _buildBody(),
        ));
  }

  @override
  void reassemble() {
    // TODO: implement reassemble
    super.reassemble();
  }

  final d = DateTime.now();
  bool int1 = true;
  Timer? timer_;
  int int_ = 0;
  @override
  void initState() {
    super.initState();
    _JobController.text = widget.emp_id;
    if (widget.emp_phonenumber.length > 1)
      _Telephonetroller.text =
          "${widget.emp_phonenumber?.substring(0, 3)}****${widget.emp_phonenumber?.substring(7)}";
    // WidgetsBinding.instance.addObserver(this);
  }

  Widget _buildBody() {
    FocusNode focusNode = FocusNode();
    return Container(
      alignment: Alignment.center,
      width: kIsWeb &&
              (defaultTargetPlatform == TargetPlatform.macOS ||
                  defaultTargetPlatform == TargetPlatform.windows)
          ? MediaQuery.of(context).size.width * 0.26
          : null,
      padding: const EdgeInsets.fromLTRB(25, 10, 25, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            height: 20,
          ),
          Image(
            image: const AssetImage("assets/images/logo.png"),
            width: Adapt.widthPt(198),
          ),
          SizedBox(
            height: 20,
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'LogInMobile'.tr,
                style: TextStyle(fontSize: 24, color: CustomColors.ThemeColor),
                // textAlign: TextAlign.right,
              ),
            ],
          ),

          SizedBox(
            height: 40,
          ),

          ///旧密码，新密码修改,验证码
          _buildForm(),

          // TextFormField(
          //   // autofocus: true,
          //   controller: _usernameCtrl,
          //   onChanged: (e) {
          //     setState(() {
          //     });
          //   },
          //
          //   decoration: InputDecoration(
          //       hintText: '请输入短信验证码',
          //       // hintText: 'Job number Tips'.tr,
          //       labelText: 'Code',
          //       // labelText: 'Job number'.tr,
          //       labelStyle: TextStyle(color: CustomColors.ThemeColor),
          //       prefixIcon: Icon(
          //         Icons.security,
          //         color: CustomColors.ThemeColor,
          //         size: 25,
          //       ),
          //       focusColor: CustomColors.ThemeColor,
          //       focusedBorder: const UnderlineInputBorder(),
          //       suffixIcon: Offstage(
          //         offstage: false,
          //         child: TextButton(
          //           onPressed: true?() async {
          //             FocusScope.of(context).requestFocus(focusNode);
          //             await Future.delayed(const Duration(milliseconds: 1000));
          //             if (!mounted) {
          //               return;
          //             }
          //
          //           }:(){},
          //           autofocus: false,
          //           style: ButtonStyle(
          //             textStyle: MaterialStateProperty.all(
          //                 const TextStyle(fontSize: 16, )),
          //
          //             //更优美的方式来设置
          //             foregroundColor: MaterialStateProperty.resolveWith(
          //                   (states) {
          //                 if (states.contains(MaterialState.focused) &&
          //                     !states.contains(MaterialState.pressed)) {
          //                   //获取焦点时的颜色
          //                   return Colors.blue;
          //                 } else if (states.contains(MaterialState.pressed)) {
          //                   //按下时的颜色
          //                   return Colors.white;
          //                 }
          //                 //默认状态使用灰色
          //                 return Colors.blue;
          //               },
          //             ),
          //             //背景颜色
          //             backgroundColor: MaterialStateProperty.resolveWith((states) {
          //               //设置按下时的背景颜色
          //               if (states.contains(MaterialState.pressed)) {
          //                 return Colors.black;
          //               }
          //               //默认不使用背景颜色
          //               return null;
          //             }),
          //             //设置水波纹颜色
          //             overlayColor: MaterialStateProperty.all(Colors.black),
          //             //设置阴影  不适用于这里的TextButton
          //             elevation: MaterialStateProperty.all(0),
          //             //设置按钮内边距
          //             padding: MaterialStateProperty.all(const EdgeInsets.all(10)),
          //             //设置按钮的大小
          //             minimumSize: MaterialStateProperty.all(const Size(60, 30)),
          //
          //             //设置边框
          //             side: MaterialStateProperty.all(const BorderSide(color: Colors.grey, width: 1)),
          //             //外边框装饰 会覆盖 side 配置的样式
          //             shape: MaterialStateProperty.all(const StadiumBorder()),
          //           ),
          //
          //           child: int_==0? (Text("发送验证码",style:
          //           const TextStyle(color:  Color.fromRGBO(151, 151, 151, 1)),)):Text(int_.toString()),
          //         ),
          //       )),
          //   autovalidateMode: AutovalidateMode.onUserInteraction,
          //   // 校验用户名
          //   validator: (v) {
          //     return v!.trim().isNotEmpty ? null : "Job number Tips1".tr;
          //   },
          // ),

          buildPinPut(),
          const SizedBox(
            height: 20,
          ),
          TextButton(
            onPressed: int1
                ? () async {
                    FocusScope.of(context).requestFocus(focusNode);
                    int1 = false;
                    print('立即清除点击事件');
                    setState(() {});
                    await Future.delayed(const Duration(milliseconds: 1000));
                    if (!mounted) {
                      return;
                    }

                    ///请求短信验证
                    var f_code = await AuthService().GetLoginSmsCode(
                        _JobController.text, widget.emp_phonenumber);

                    if (f_code?.code == 200) {
                      int1 = false;
                      int_ = 60;
                      timer_ = Timer.periodic(
                          const Duration(
                            milliseconds: 1000,
                          ), (timer) {
                        int_--;
                        if (int_ == 0) {
                          int1 = true;
                          timer_?.cancel();
                        }
                        setState(() {});
                      });
                    } else {
                      int1 = true;
                      setState(() {});
                    }
                  }
                : () {},
            autofocus: false,
            style: ButtonStyle(
              textStyle: MaterialStateProperty.all(const TextStyle(
                fontSize: 16,
              )),

              //更优美的方式来设置
              foregroundColor: MaterialStateProperty.resolveWith(
                (states) {
                  if (states.contains(MaterialState.focused) &&
                      !states.contains(MaterialState.pressed)) {
                    //获取焦点时的颜色
                    return Colors.blue;
                  } else if (states.contains(MaterialState.pressed)) {
                    //按下时的颜色
                    return Colors.white;
                  }
                  //默认状态使用灰色
                  return Colors.blue;
                },
              ),
              //背景颜色
              backgroundColor: MaterialStateProperty.resolveWith((states) {
                //设置按下时的背景颜色
                if (states.contains(MaterialState.pressed)) {
                  return Colors.black;
                }
                //默认不使用背景颜色
                return null;
              }),
              //设置水波纹颜色
              overlayColor: MaterialStateProperty.all(Colors.black),
              //设置阴影  不适用于这里的TextButton
              elevation: MaterialStateProperty.all(0),
              //设置按钮内边距
              padding: MaterialStateProperty.all(const EdgeInsets.all(10)),
              //设置按钮的大小
              minimumSize: MaterialStateProperty.all(const Size(60, 30)),

              //设置边框
              side: MaterialStateProperty.all(
                  const BorderSide(color: Colors.grey, width: 1)),
              //外边框装饰 会覆盖 side 配置的样式
              shape: MaterialStateProperty.all(const StadiumBorder()),
            ),
            child: int_ == 0
                ? (Text(
                    "SendSMS".tr,
                    style:
                        // child: int_==0? (Text("发送验证码",style:
                        TextStyle(
                            color: Color.fromRGBO(
                      151,
                      151,
                      151,
                      1,
                    )),
                  ))
                : Text(int_.toString()),
          ),

          const Padding(padding: EdgeInsets.only(top: 30)),

          Container(
            width: MediaQuery.of(context).size.width,
            padding: EdgeInsets.only(top: Adapt.heightPt(30)),
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: CustomColors.ThemeColor,
                minimumSize: Size(MediaQuery.of(context).size.width, 48),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
              onPressed: _isLoading
                  ? null
                  : () {
                      setState(() {
                        _isLoading = true;
                      });
                      _smsLoginClick();
                    },
              child: _isLoading
                  ? SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text("Button3".tr, style: TextStyle(color: Colors.white)),
            ),
          )
        ],
      ),
    );
  }

  Widget _buildForm() {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          const Padding(padding: EdgeInsets.only(top: 20)),
          _buildJob(),
          const Padding(padding: EdgeInsets.only(top: 20)),
          _buildTelephone(),
          const Padding(padding: EdgeInsets.only(top: 20)),
        ],
      ),
    );
  }

  Widget _buildJob() {
    return TextFormField(
      autofocus: true,
      controller: _JobController,
      // inputFormatters: [FilteringTextInputFormatter.allow(RegExp("[0-9.]")),],
      onChanged: (e) {
        setState(() {
          _Job = e;
        });
      },
      readOnly: true,
      decoration: InputDecoration(
        // hintText: 'Job number Tips'.tr,
        // hintText: "请输入工号".tr,
        hintText: "45812",
        floatingLabelBehavior: FloatingLabelBehavior.auto,
        label: Text(
          'Job number'.tr,
          // "工号:".tr,
          style: TextStyle(color: CustomColors.ThemeColor),
        ),
        focusColor: const Color.fromRGBO(29, 64, 133, 1),
        focusedBorder: const UnderlineInputBorder(),
        suffixIcon: Offstage(
          offstage: _Job == "",
        ),
      ),
      autovalidateMode: AutovalidateMode.onUserInteraction,
      validator: (v) {
        if (!v!.trim().isNotEmpty) {
          return 'Job number Tips1'.tr;
          // return "工号不能为空".tr;
        }
        return null;
      },
    );
  }

  Widget _buildTelephone() {
    return TextFormField(
      autofocus: true,
      readOnly: true,
      controller: _Telephonetroller,
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp("[0-9.()_]")),
      ],
      onChanged: (e) {
        setState(() {
          _Telephone = e;
        });
      },
      decoration: InputDecoration(
        // hintText: 'Code',
        // hintText: 'phone number Tips'.tr,
        // hintText: "请输入手机号".tr,
        // hintText: "138****2258",
        floatingLabelBehavior: FloatingLabelBehavior.auto,
        label: Text(
          "phone number".tr,
          // "phone number".tr,
          // "手机号".tr,
          style: TextStyle(color: CustomColors.ThemeColor),
        ),
        focusColor: const Color.fromRGBO(29, 64, 133, 1),
        focusedBorder: const UnderlineInputBorder(),
        suffixIcon: Offstage(
          offstage: _Telephone == "",
        ),
      ),
      autovalidateMode: AutovalidateMode.onUserInteraction,
      validator: (v) {
        if (!v!.trim().isNotEmpty) {
          return "phone number Tips1".tr;
          // return "手机号不能为空".tr;
        }
        return null;
      },
    );
  }

  void _smsLoginClick() async {
    if ((_formKey.currentState as FormState).validate() == false) {
      setState(() {
        _isLoading = false;
      });
      return;
    }

    var Job = _JobController.text;

    ///设备绑定短信验证
    var resp = await AuthService().DeviceLogin(
        Job,
        widget.emp_phonenumber,
        validationCode,
        widget.platform,
        widget.version,
        widget.lang,
        widget.uuid);
    print("===================================$resp");

    setState(() {
      _isLoading = false;
    });

    if (resp!.code == 200) {
      var s = GetStorage();
      var _encryptPwd = MyCrypt.pwdEncrypt(widget.encryptPwd);
      var input = LoginInputDto(
          Job,
          _encryptPwd,
          widget.platform == '' ? 'wap' : widget.platform,
          widget.version,
          widget.version,
          widget.uuid);
      // var input = LoginInputDto(u, encryptPwd,platform==''?'wap':platform,_version,lang, device_str);
      var r = !kIsWeb
          ? await AuthService().login(input)
          : await AuthService().Weblogin(input);

      if (r == null) {
        Fluttertoast.showToast(msg: "网络异常，请稍后再试");
        return;
      }

      if (!r.success) {
        Fluttertoast.showToast(msg: r.message);
        return;
      }

      // 权限打开方法 By Leo 2023-12-25
      String supplier_str;
      roles = r.data["roles"] ?? "0"; //0:供应商, 1:ATC员工
      // supplier_str = r.data["menus"]??"";  //不需要显示的功能
      supplier_str = r.data["menus"] ??
          "内部推荐,班车考勤,运动生活,班车线路,公告,Teams,Outlook,E-mobile7,问卷调查,DL绩效考核"; //不需要显示的功能
      supplier = supplier_str.split(",");

      var loginInfo = LoginOutputDto.fromJson(r.data!);
      s.write("token", loginInfo.token);
      loginInfo.employee!.employeeId = Job;

      ///传值
      ///添加了一个供app store审核的账号该账号电话号等参数为空所以需要加一个判定
      if (r.data['employee']['mobile'] != null)
        Routes(r.data['employee']['mobile'], r.data['employee']['maxPicSize']);
      s.write("employee", loginInfo.employee!.toJson());
      s.write("userinfo", loginInfo.employee);

      ///判断是否有新的应用来替换缓存
      bool renew = true;

      s.remove('cyl');
      print('print homedata');
      print(homeData);
      var homeDatas = copyData(homeData);
      var homeData1s = copyData(homeData1);
      var homeData2s = copyData(homeData2);
      var homeData3s = copyData(homeData3);
      var homeDataRes = copyData(homeDataRe);
      var homeDataOthers = copyData(homeDataOther);
      var homePages = copyData(homePage);

      ///暂时缓存本地的数据
      // bool flag_a=false; // 登陆权限控制 by Leo
      if (renew) {
        s.write('cyl', homeDatas);
        s.write('spl', homeData1s);
        s.write('COVID_19', homeData2s);
        s.write('dsf', homeData3s);
        s.write('class_0', homeDataRes);
        s.write('class_Other', homeDataOthers);
        s.write('top1', homePages);
      }

      supplier.map((e) {
        complexDataRemoveAt(s.read('cyl'), e);
        complexDataRemoveAt(s.read('spl'), e);
        complexDataRemoveAt(s.read('COVID_19'), e);
        complexDataRemoveAt(s.read('dsf'), e);
        complexDataRemoveAt(s.read('class_0'), e);
        complexDataRemoveAt(s.read('class_Other'), e);
        complexDataRemoveAt(s.read("top1"), e);
      }).toList();

      if (roles == "0") {
        var e = s.read("top1");
        for (int i = 0; i < e.length; i++) {
          if (e[i][0] == '员工码') {
            e[i] = ["通行码", "gp", "/showPassportCode"];
            print('->-->${e}');
          }
        }
      }

      var responses = await EmployeeService().Getquestionnaire(2);
      responses!.data.map((e) {
        s.read('cyl').map((s) {
          if (s[0] == e["title"]) {
            s[2] = "报表||${e["url"]}";
          }
        }).toList();
        s.read('spl').map((s) {
          if (s[0] == e["title"]) {
            s[2] = "报表||${e["url"]}";
          }
        }).toList();
        s.read('top1').map((s) {
          if (s[0] == e["title"]) {
            s[2] = "报表||${e["url"]}";
          }
        }).toList();

        s.read('COVID_19').map((s) {
          if (s[0] == e["title"]) {
            s[2] = "报表||${e["url"]}";
          }
        }).toList();
        s.read('dsf').map((s) {
          if (s[0] == e["title"]) {
            s[2] = "报表||${e["url"]}";
          }
        }).toList();
        s.read('class_0').map((s) {
          if (s[0] == e["title"]) {
            s[2] = "报表||${e["url"]}";
          }
        }).toList();
        s.read('class_Other').map((s) {
          if (s[0] == e["title"]) {
            s[2] = "报表||${e["url"]}";
          }
        }).toList();
      }).toList();

      if (widget.freeLogin) {
        s.write(
            "loginInfo",
            LoginInputDto(widget.emp_id, widget.encryptPwd, widget.platform,
                widget.version, widget.lang, widget.uuid));
      } else {
        s.remove("loginInfo");
      }

      // 登陆成功之后的页面
      Navigator.of(context).pushReplacementNamed(RouteNames.indexPage);

      return;
    }
    Fluttertoast.showToast(msg: resp.message);
  }

  Widget buildPinPut() {
    final defaultPinTheme = PinTheme(
      width: 36,
      height: 36,
      textStyle: const TextStyle(
          fontSize: 20,
          color: Color.fromRGBO(30, 60, 87, 1),
          fontWeight: FontWeight.w600),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.black),
        // border: Border.all(color: const Color.fromRGBO(234, 239, 243, 1)),
        borderRadius: BorderRadius.circular(20),
      ),
    );

    final focusedPinTheme = defaultPinTheme.copyDecorationWith(
      border: Border.all(color: const Color.fromRGBO(114, 178, 238, 1)),
      borderRadius: BorderRadius.circular(8),
    );

    final submittedPinTheme = defaultPinTheme.copyWith(
      decoration: defaultPinTheme.decoration?.copyWith(
        color: const Color.fromRGBO(234, 239, 243, 1),
      ),
    );

    return Pinput(
        length: 6,
        defaultPinTheme: defaultPinTheme,
        focusedPinTheme: focusedPinTheme,
        submittedPinTheme: submittedPinTheme,
        inputFormatters: [
          FilteringTextInputFormatter.allow(RegExp("[0-9.]")),
        ],
        // validator: (s) {
        //   return s == '2222' ? null : 'Pin is incorrect';
        // },
        pinputAutovalidateMode: PinputAutovalidateMode.onSubmit,
        showCursor: true,
        onChanged: (value) {
          validationCode = value;
          print('-------------$validationCode');
        },
        onCompleted: (pin) => () {
              validationCode = pin;
              print('-------------$pin');
            });
  }

  @override
  void dispose() {
    _JobController.dispose();
    _Telephonetroller.dispose();
    timer_?.cancel();
    // WidgetsBinding.instance.removeObserver(this);

    super.dispose();
  }

  copyData(var data) {
    List<dynamic> cpData = [];
    for (var element in data) {
      cpData.add(element);
    }
    return cpData;
  }
}

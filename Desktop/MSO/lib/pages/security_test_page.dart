import 'package:flutter/material.dart';
import 'package:amkor/components/security_manager.dart';

/// 安全功能测试页面
class SecurityTestPage extends StatefulWidget {
  const SecurityTestPage({Key? key}) : super(key: key);

  @override
  State<SecurityTestPage> createState() => _SecurityTestPageState();
}

class _SecurityTestPageState extends State<SecurityTestPage> {
  bool _isProtectionEnabled = false;

  @override
  void initState() {
    super.initState();
    _checkProtectionStatus();
  }

  void _checkProtectionStatus() {
    setState(() {
      _isProtectionEnabled = SecurityManager.instance.isInitialized;
    });
  }

  Future<void> _toggleProtection() async {
    try {
      if (_isProtectionEnabled) {
        await SecurityManager.instance.dispose();
      } else {
        await SecurityManager.instance.initialize();
      }
      _checkProtectionStatus();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_isProtectionEnabled ? '防截屏已启用' : '防截屏已禁用'),
            backgroundColor:
                _isProtectionEnabled ? Colors.green : Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('操作失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('安全功能测试'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          _isProtectionEnabled
                              ? Icons.security
                              : Icons.security_outlined,
                          color:
                              _isProtectionEnabled ? Colors.green : Colors.grey,
                          size: 24,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          '防截屏状态',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _isProtectionEnabled ? '已启用' : '已禁用',
                      style: TextStyle(
                        color: _isProtectionEnabled ? Colors.green : Colors.red,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 16),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _toggleProtection,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: _isProtectionEnabled
                              ? Colors.orange
                              : Colors.green,
                        ),
                        child: Text(
                          _isProtectionEnabled ? '禁用防截屏' : '启用防截屏',
                          style: const TextStyle(color: Colors.white),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '测试说明',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      '1. 启用防截屏后，尝试截屏应该会失败或显示黑屏\n'
                      '2. 在最近任务中，应用预览应该显示为黑屏或模糊\n'
                      '3. 禁用防截屏后，截屏功能应该恢复正常',
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),
            Card(
              color: Colors.yellow[50],
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(Icons.info, color: Colors.orange),
                        const SizedBox(width: 8),
                        Text(
                          '敏感信息区域',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      '这里是一些敏感信息：\n'
                      '• 用户ID: 123456789\n'
                      '• 密码: ********\n'
                      '• 银行卡号: **** **** **** 1234\n'
                      '• 身份证号: 110101********1234',
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

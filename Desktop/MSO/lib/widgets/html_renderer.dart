import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:url_launcher/url_launcher.dart';

/// Custom HTML renderer widget that replaces flutter_html functionality
/// Uses webview_flutter to render HTML content with better performance and compatibility
class HtmlRenderer extends StatefulWidget {
  final String htmlContent;
  final Function(String url)? onLinkTap;
  final Function(String url)? onImageTap;
  final double? height;
  final Map<String, String>? customStyles;

  const HtmlRenderer({
    Key? key,
    required this.htmlContent,
    this.onLinkTap,
    this.onImageTap,
    this.height,
    this.customStyles,
  }) : super(key: key);

  @override
  State<HtmlRenderer> createState() => _HtmlRendererState();
}

class _HtmlRendererState extends State<HtmlRenderer> {
  late WebViewController _controller;
  double _webViewHeight = 200.0;
  bool _isLoading = false;
  bool _isControllerReady = false;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }

  void _initializeWebView() {
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            if (mounted) {
              setState(() {
                _isLoading = true;
              });
            }
          },
          onPageFinished: (String url) {
            Future.delayed(const Duration(milliseconds: 300), () {
              if (mounted) {
                setState(() {
                  _isLoading = false;
                });
                _updateWebViewHeight();
              }
            });
          },
          onNavigationRequest: (NavigationRequest request) {
            // Handle link taps
            if (request.url.startsWith('http') ||
                request.url.startsWith('https')) {
              if (widget.onLinkTap != null) {
                widget.onLinkTap!(request.url);
              } else {
                _launchUrl(request.url);
              }
              return NavigationDecision.prevent;
            }

            // Handle image taps (custom protocol)
            if (request.url.startsWith('image://')) {
              final imageUrl =
                  request.url.substring(8); // Remove 'image://' prefix
              if (widget.onImageTap != null) {
                widget.onImageTap!(imageUrl);
              }
              return NavigationDecision.prevent;
            }

            return NavigationDecision.navigate;
          },
        ),
      );

    _isControllerReady = true;

    // Load HTML content
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        setState(() {
          _isLoading = true;
        });
        _controller.loadHtmlString(_buildHtmlContent());
      }
    });
  }

  String _buildHtmlContent() {
    final customStyles = widget.customStyles ?? {};
    final defaultStyles = {
      'body':
          'margin: 0; padding: 16px; font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; line-height: 1.6;',
      'img': 'max-width: 100%; height: auto; cursor: pointer;',
      'a': 'color: #007AFF; text-decoration: none;',
      'iframe': 'width: 100%; border: none;',
      'video': 'width: 100%; height: auto;',
      'table': 'width: 100%; border-collapse: collapse;',
      'td, th': 'border: 1px solid #ddd; padding: 8px; text-align: left;',
      'blockquote':
          'border-left: 4px solid #ddd; margin: 0; padding-left: 16px; color: #666;',
      'pre':
          'background-color: #f5f5f5; padding: 12px; border-radius: 4px; overflow-x: auto;',
      'code':
          'background-color: #f5f5f5; padding: 2px 4px; border-radius: 2px; font-family: monospace;',
    };

    final allStyles = {...defaultStyles, ...customStyles};
    final styleString = allStyles.entries
        .map((entry) => '${entry.key} { ${entry.value} }')
        .join('\n');

    return '''
<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <style>
        $styleString
    </style>
    <script>
        // 页面加载完成标志
        let pageLoaded = false;
        let imagesLoaded = 0;
        let totalImages = 0;

        // Handle image clicks
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM Content Loaded');

            const images = document.querySelectorAll('img');
            totalImages = images.length;

            images.forEach(function(img) {
                img.addEventListener('click', function() {
                    window.location.href = 'image://' + img.src;
                });

                // 监听图片加载
                if (img.complete) {
                    imagesLoaded++;
                } else {
                    img.onload = function() {
                        imagesLoaded++;
                        checkAllLoaded();
                    };
                    img.onerror = function() {
                        imagesLoaded++;
                        checkAllLoaded();
                    };
                }
            });

            // Auto-resize iframe height
            const iframes = document.querySelectorAll('iframe');
            iframes.forEach(function(iframe) {
                iframe.onload = function() {
                    try {
                        const height = iframe.contentWindow.document.body.scrollHeight;
                        iframe.style.height = height + 'px';
                    } catch (e) {
                        // Cross-origin iframe, use default height
                        iframe.style.height = '300px';
                    }
                    checkAllLoaded();
                };
            });

            // 检查所有内容是否加载完成
            function checkAllLoaded() {
                if (!pageLoaded && (totalImages === 0 || imagesLoaded >= totalImages)) {
                    pageLoaded = true;
                    console.log('All content loaded');
                    // 延迟一点时间确保渲染完成
                    setTimeout(function() {
                        console.log('Page ready');
                    }, 200);
                }
            }

            // 初始检查
            setTimeout(checkAllLoaded, 100);
        });

        // 页面完全加载后的处理
        window.addEventListener('load', function() {
            console.log('Window loaded');
            setTimeout(function() {
                pageLoaded = true;
                console.log('Page fully loaded');
            }, 300);
        });
    </script>
</head>
<body>
    ${widget.htmlContent}
</body>
</html>
    ''';
  }

  void _updateWebViewHeight() {
    if (!_isControllerReady || !mounted) return;

    // 延迟执行高度计算，确保DOM完全渲染
    Future.delayed(const Duration(milliseconds: 500), () {
      if (!mounted || !_isControllerReady) return;

      _controller.runJavaScriptReturningResult('''
        Math.max(
          document.body.scrollHeight,
          document.body.offsetHeight,
          document.documentElement.clientHeight,
          document.documentElement.scrollHeight,
          document.documentElement.offsetHeight
        );
      ''').then((result) {
        if (!mounted) return;

        try {
          final height = int.tryParse(result.toString()) ?? 0;
          if (height > 0 && height < 10000) {
            // 添加合理性检查
            setState(() {
              _webViewHeight = height.toDouble() + 40; // 增加更多padding
            });
          }
        } catch (e) {
          // Fallback height calculation
          if (mounted) {
            setState(() {
              _webViewHeight = widget.height ?? 600.0; // 增加默认高度
            });
          }
        }
      }).catchError((error) {
        // Fallback height calculation
        if (mounted) {
          setState(() {
            _webViewHeight = widget.height ?? 600.0; // 增加默认高度
          });
        }
      });
    });
  }

  Future<void> _launchUrl(String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      }
    } catch (e) {
      debugPrint('Error launching URL: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: widget.height ?? _webViewHeight,
      child: Stack(
        children: [
          WebViewWidget(controller: _controller),
          if (_isLoading)
            const Center(
              child: CircularProgressIndicator(),
            ),
        ],
      ),
    );
  }
}

/// Simplified HTML renderer for basic HTML content
class SimpleHtmlRenderer extends StatelessWidget {
  final String htmlContent;
  final Function(String url)? onLinkTap;
  final Function(String url)? onImageTap;
  final double? height;

  const SimpleHtmlRenderer({
    Key? key,
    required this.htmlContent,
    this.onLinkTap,
    this.onImageTap,
    this.height = 400.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return HtmlRenderer(
      htmlContent: htmlContent,
      onLinkTap: onLinkTap,
      onImageTap: onImageTap,
      height: height,
      customStyles: const {
        'body': 'margin: 0; padding: 12px; font-size: 14px; color: #333;',
        'img': 'max-width: 100%; height: auto; border-radius: 4px;',
        'a': 'color: #007AFF;',
      },
    );
  }
}

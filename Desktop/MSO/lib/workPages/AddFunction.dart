
import 'dart:io';

import 'package:amkor/components/tabbar.dart';
import 'package:amkor/services/employeeService.dart';
import 'package:amkor/utils/Adapt.dart';
import 'package:amkor/utils/crypt.dart';
import 'package:amkor/workPages/webview.dart';
import 'package:external_app_launcher/external_app_launcher.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:url_launcher/url_launcher.dart';
class AddFunction extends StatefulWidget {
  const AddFunction({Key? key}) : super(key: key);

  @override
  State<AddFunction> createState() => _AddFunctionState();
}



class _AddFunctionState extends State<AddFunction> {
  bool qh=true;
  bool gl=false;
  var homeData = GetStorage().read("cyl");
  var homeData1 = GetStorage().read("spl");
  var homeData2 = GetStorage().read("COVID_19");
  var homeData3 = GetStorage().read("dsf");
  var homeDataRe = GetStorage().read("class_0");
  var homeDataOther = GetStorage().read("class_Other");

  var homeDatas =[];
  var homeData1s = [];
  var homeData2s = [];
  var homeData3s = [];
  var homeDataRes = [];
  var homeDataOthers = [];

  copyData(var data){
    List<dynamic> cpData=[];
    for (var element in data) {
      cpData.add(element);
    }
    return cpData;
  }
  @override
  void initState() {
    // TODO: implement activate
    super.initState();
    if(!kIsWeb) Adapt.setAndroid();
    // homeDatas =copyData(homeData);
    // homeData1s = copyData(homeData1);
    // homeData2s = copyData(homeData2);
    // homeData3s = copyData(homeData3);
    // homeDataRes = copyData(homeDataRe);
    // homeDataOthers = copyData(homeDataOther);
    refreshStatus(qh);
  }

  ///HR类
  Widget _buildCommonClass() {
    return Container(
      decoration: const BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(6)),
          color: Colors.white
      ),
      padding: EdgeInsets.only(top:Adapt.widthPt(10),bottom: Adapt.widthPt(10),left: Adapt.widthPt(0),right: Adapt.widthPt(0)),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.only(left: Adapt.widthPt(12)),
            alignment: Alignment.bottomLeft,
            child: Text(
              "title1_1".tr,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Container(
              padding: EdgeInsets.only(top: Adapt.widthPt(20)),
              child:GridView.count(
                shrinkWrap:true,
                scrollDirection: Axis.vertical,
                primary:false,
                crossAxisCount: 4,
                padding:const EdgeInsets.all(0),
                crossAxisSpacing: 10,
                mainAxisSpacing: 10,
                children: [
                  for(int j=0;j<homeData.length-1;j++)
                    _buildWorkbenchItems(
                        j,
                        homeData
                    ),
                ],

              ),
          )
        ],
      ),
    );
  }

  //COVID-19类
  Widget _buildCOVID_19() {
    return Container(
      decoration: const BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(6)),
          color: Colors.white
      ),
      padding: EdgeInsets.only(top:Adapt.widthPt(10),bottom: Adapt.widthPt(10),left: Adapt.widthPt(0),right: Adapt.widthPt(0)),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.only(left: Adapt.widthPt(12)),
            alignment: Alignment.bottomLeft,
            child: Text(
              "防疫类".tr,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Container(
              padding: EdgeInsets.only(top: Adapt.widthPt(10)),
              child:GridView.count(
                shrinkWrap:true,
                scrollDirection: Axis.vertical,
                primary:false,
                crossAxisCount: 4,
                padding:const EdgeInsets.all(0),
                crossAxisSpacing: 10,
                mainAxisSpacing: 10,
                children: [
                  for(int j=0;j<homeData1.length;j++)
                    _buildWorkbenchItems(
                        j,
                        homeData1
                    ),
                ],

              ),

          )
        ],
      ),
    );


  }

  //审批类
  Widget _buildApproval() {
    return Container(
      decoration: const BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(6)),
          color: Colors.white
      ),
      padding: EdgeInsets.only(top:Adapt.widthPt(10),bottom: Adapt.widthPt(10),left: Adapt.widthPt(0),right: Adapt.widthPt(0)),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.only(left: Adapt.widthPt(12)),
            alignment: Alignment.bottomLeft,
            child: Text(
              "审批类".tr,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Container(
              padding: EdgeInsets.only(top: Adapt.widthPt(20)),
              child:GridView.count(
                shrinkWrap:true,
                scrollDirection: Axis.vertical,
                primary:false,
                crossAxisCount: 4,
                padding:const EdgeInsets.all(0),
                crossAxisSpacing: 10,
                mainAxisSpacing: 10,
                children: [
                  for(int j=0;j<homeData2.length-1;j++)
                    _buildWorkbenchItems(
                        j,
                        homeData2
                    ),
                ],

              ),

          )
        ],
      ),
    );


  }
  //第三方应用
  Widget _buildThirdParty() {
    return Container(
      decoration: const BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(6)),
          color: Colors.white
      ),
      padding: EdgeInsets.only(top:Adapt.widthPt(10),bottom: Adapt.widthPt(10),left: Adapt.widthPt(0),right: Adapt.widthPt(0)),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.only(left: Adapt.widthPt(12)),
            alignment: Alignment.bottomLeft,
            child: Text(
              "第三方应用".tr,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Container(
            padding: EdgeInsets.only(top: Adapt.widthPt(20)),
            child: GridView.count(
              shrinkWrap:true,
              scrollDirection: Axis.vertical,
              primary:false,
              crossAxisCount: 4,
              padding:const EdgeInsets.all(0),
              crossAxisSpacing: 10,
              mainAxisSpacing: 10,
              children: [
                for(int j=0;j<homeData3.length-1;j++)
                  _buildWorkbenchItems(
                      j,
                      homeData3
                  ),
              ],

            ),
          )
        ],
      ),
    );


  }
  //其他类
  Widget _buildOther() {
    double w = MediaQuery.of(context).size.width;

    return Container(
      width: w*0.92,
      decoration: const BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(6)),
          color: Colors.white
      ),
      padding: EdgeInsets.only(top:Adapt.widthPt(10),bottom: Adapt.widthPt(10),left: Adapt.widthPt(0),right: Adapt.widthPt(0)),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.only(left: Adapt.widthPt(12)),
            alignment: Alignment.bottomLeft,
            child: Text(
              "其他类".tr,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),

          Container(
            padding: EdgeInsets.only(top: Adapt.widthPt(20)),
            child: GridView.count(
              shrinkWrap:true,
              scrollDirection: Axis.vertical,
              primary:false,
              crossAxisCount: 4,
              padding:const EdgeInsets.all(0),
              crossAxisSpacing: 10,
              mainAxisSpacing: 10,
              children: [
                for(int j=0;j<homeDataOther.length-1;j++)
                  _buildWorkbenchItems(
                      j,
                      homeDataOther
                  ),
              ],

            ),

          )
        ],
      ),
    );


  }
  Widget _buildWorkbenchItems(int xl,List<dynamic> data) {
    String img ="assets/icons/${data[xl][1]}";
    return Padding(
      padding: EdgeInsets.only(
          left: Adapt.widthPt(1)),
      child: GestureDetector(
        onTap:data[xl][3]=="true"
            ?(){
          if(data[xl][0]!='') {
            _navigatorPush(data[xl][2]);
          }}
            :(){
          setState(() {
            if(data[xl][0]!='') {
              homeDataRe.add(data[xl]);
              data.removeAt(xl);
              writeCache();
            }

          });
        },
        onLongPress:(){
          qh=false;
          setState(() {
            if(data[xl][0]!='') {
              refreshStatus(qh);
            }
          });
        },
        child: Column(
          children: [
            Stack(
              children: [
                Image(
                  image: AssetImage(img),
                  width: Adapt.widthPt(50),
                  height: Adapt.widthPt(50),
                ),
                if(data[xl][3]=="false"&&data[xl][0]!='')Image(
                  image: const AssetImage('assets/icons/delete_j.png'),
                  width: Adapt.widthPt(50),
                  height: Adapt.widthPt(50),
                ),
              ],
            ),
            Container(
              alignment: Alignment.center,
              padding: EdgeInsets.only(top: 5),
              width: Adapt.widthPt(81),
              child: Text(
                data[xl][0],
                style: TextStyle(
                  color: Colors.black,
                  fontSize: Adapt.widthPt(12),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
  ///ATC管理
  Widget _buildATC() {
    return Container(
      decoration: const BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(6)),
          color: Colors.white
      ),
      padding: EdgeInsets.all(Adapt.widthPt(12)),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.only(left: Adapt.widthPt(1.5)),
            alignment: Alignment.bottomLeft,
            child: Text(
              "ATC管理".tr,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Container(
              padding: EdgeInsets.only(top: Adapt.widthPt(20)),
              child:GridView.count(
                shrinkWrap:true,
                scrollDirection: Axis.vertical,
                primary:false,
                crossAxisCount: 3,
                padding:const EdgeInsets.all(0),
                crossAxisSpacing: 10,
                mainAxisSpacing: 10,
                children: [
                  for(int j=0;j<homeDataRe.length;j++)
                    _buildWorkbenchItem(
                      homeDataRe[j][0],
                      homeDataRe[j][1],
                      homeDataRe[j][2],
                      j,
                      homeDataRe[j][4],
                    ),

                ],

              ),
          )

        ],
      ),
    );


  }

  Widget _buildWorkbenchItem(String desc, String img, String key,int xl,String classLei) {
    img = "assets/icons/$img";
    return Padding(
      padding: EdgeInsets.only(right: Adapt.widthPt(19),left: Adapt.widthPt(19)),
      child: GestureDetector(
        onTap:homeDataRe[xl][3]=="true"?() => _navigatorPush(key):(){
          setState(() {
            if(classLei=='HR') {
              homeData.insert(homeData.length-1,homeDataRe[xl]);
            } else if(classLei=='COVID-19'){
              homeData1.insert(homeData1.length,homeDataRe[xl]);
            }
            else if(classLei=='审批类'){
              homeData2.insert(homeData2.length-1,homeDataRe[xl]);
            }
            else if(classLei=='第三方应用'){
              homeData3.insert(homeData3.length-1,homeDataRe[xl]);
            }
            else {
            homeDataOther.insert(homeDataOther.length-1,homeDataRe[xl]);
            }

            homeDataRe.removeAt(xl);
            writeCache();
          });
        },
        onLongPress: (){
          setState(() {
            qh=false;
            if(desc!='') {
              refreshStatus(qh);
            }
          });
        },
        child:Column(
          children: [
            Stack(
              children: [
                Image(
                  image: AssetImage(img),
                  width: Adapt.widthPt(50),
                  height: Adapt.widthPt(50),
                ),
                if(homeDataRe[xl][3]=="false")Image(
                  image: const AssetImage('assets/icons/add_j.png'),
                  width: Adapt.widthPt(50),
                  height: Adapt.widthPt(50),
                ),
              ],
            ),
            Container(
              alignment: Alignment.center,
              padding: const EdgeInsets.only(top: 5),
              width: Adapt.widthPt(64),
              child: Text(
                desc,
                style: TextStyle(
                  color: Colors.black,
                  fontSize: Adapt.widthPt(10),
                ),
              ),
            )
          ],
        ),

      ),
    );
  }

  Future<void> _navigatorPush(String key) async {
    if (!mounted) return;
    if(key=='/KongPage') {
      Fluttertoast.cancel();
      Fluttertoast.showToast(msg: "此功能开发建设中");
    }else if(key.split("||")[0] =='URL'){
      if (Platform.isIOS) {
        _launchUrl(key.split("||")[1]);
      }else{
        await LaunchApp.openApp(
          androidPackageName: key.split("||")[2],
        );
      }

    }else if(key.split("||")[0] =='报表'){
      String employeeId = Uri.encodeComponent(MyCrypt.aesEncrypt(GetStorage().read('employee')['employeeId']));
      String mobile = Uri.encodeComponent(MyCrypt.aesEncrypt(GetStorage().read('employee')['mobile']));
      print("---->${key.split("||")[1]}?employeeId=$employeeId&mobile=$mobile");
      if (mounted) {
        Navigator.push(context, MaterialPageRoute(builder: (context) {
          return WebviewPage(title: "", url: "${key.split("||")[1]}?employeeId=$employeeId&mobile=$mobile");
        }));
      }
    } else {
      Navigator.of(context).pushNamed(key).then((value) {
        if(!kIsWeb) {
          Adapt.setAndroid(isDark: false);
        }
        refreshStatus(qh);
      });
    }
  }
  Future<void> _launchUrl(String key) async {
    try {
      if (!await launchUrl(Uri.parse(key))) {
        Fluttertoast.cancel();
        Fluttertoast.showToast(msg: "请先行安装此应用");
        throw Exception('Could not launch ${Uri.parse(key)}');
      }
    } on PlatformException catch (e) {
      if (e.code == 'ERROR_INVALID_URL') {
        print('Invalid URL: ${e.toString()}');
      } else if (e.code == 'ERROR_LAUNCH_FAILED') {
        print('Failed to launch URL: ${e.toString()}');
      } else if (e.code == 'ACTIVITY_NOT_FOUND') {
        Fluttertoast.cancel();
        Fluttertoast.showToast(msg: "请先行安装此应用");
        print('Unknown error: ${e.toString()}');
      }
    }

  }
  @override
  Widget build(BuildContext context) {

    return Scaffold(
      body:Stack(
        children: [
          qh?
          Tabbar(
            title: "ATC应用",
            showBack: true,
            rightBtn: Align(
                alignment: Alignment.center,
                child: GestureDetector(
                  child: const Text(
                    '管理',style: TextStyle(fontSize:16 ,color: Colors.blueAccent,),
                  ),
                  onTap:(){
                    homeDatas =copyData(homeData);
                    homeData1s = copyData(homeData1);
                    homeData2s = copyData(homeData2);
                    homeData3s = copyData(homeData3);
                    homeDataRes = copyData(homeDataRe);
                    homeDataOthers = copyData(homeDataOther);

                    setState(() {
                    });
                    gl=true;
                    qh=false;
                    refreshStatus(qh);

                  },
                )
            ),
            fontColor: Colors.black,
          ):
          Tabbar(
            title: "应用管理",
            leftBtn: Align(
              alignment: Alignment.centerLeft,
              child: TextButton(
                style: ButtonStyle(
                  padding: MaterialStateProperty.all(EdgeInsets.all(4)),
                  minimumSize: MaterialStateProperty.all(Size(44,22)),
                  foregroundColor:MaterialStateProperty.resolveWith(
                        (states) {
                      if (states.contains(MaterialState.focused) &&
                          !states.contains(MaterialState.pressed)) {
                        return Colors.blue;
                      } else if (states.contains(MaterialState.pressed)) {
                        return Colors.grey;
                      }
                      return Colors.blueAccent;
                    },
                  ),
                ),
                onPressed: () {
                  if(gl){
                    homeData = homeDatas;
                    homeData1 = homeData1s;
                    homeData2 = homeData2s;
                    homeData3 = homeData3s;
                    homeDataRe = homeDataRes;
                    homeDataOther = homeDataOthers;
                    ///本地缓存
                    var s = GetStorage();
                    s.write('cyl', homeDatas);
                    s.write('spl', homeData1s);
                    s.write('COVID_19', homeData2s);
                    s.write('dsf', homeData3s);
                    s.write('class_0', homeDataRes);
                    s.write('class_Other', homeDataOthers);
                  }
                  setState(() {
                    qh=true;
                    refreshStatus(qh);
                  });
                },
                child: const Text(
                  '取消',style: TextStyle(fontSize:16 ),
                ),
              ),
            ),
            rightBtn: Align(
              alignment: Alignment.center,
              child: TextButton(
                style: ButtonStyle(
                  padding: MaterialStateProperty.all(EdgeInsets.all(4)),
                  minimumSize: MaterialStateProperty.all(Size(44,22)),
                  foregroundColor:MaterialStateProperty.resolveWith(
                        (states) {
                      if (states.contains(MaterialState.focused) &&
                          !states.contains(MaterialState.pressed)) {
                        return Colors.blue;
                      } else if (states.contains(MaterialState.pressed)) {
                        return Colors.indigo;
                      }
                      return Colors.black;
                    },
                  ),
                  backgroundColor: MaterialStateProperty.resolveWith((states) {
                    if (states.contains(MaterialState.pressed)) {
                      return Colors.indigo;
                    }
                    return Colors.blueAccent;
                  }),
                ),
                onPressed: () {
                  setState(() {
                    qh=true;
                    refreshStatus(qh);
                  });
                },
                child: const Text(
                  '保存',style: TextStyle(fontSize:14 ,color: Colors.white,),
                ),
              ),
            ),
            fontColor: Colors.black,
          ),
          Center(
            child:Container(
              width: MediaQuery.of(context).size.width*0.92,
              alignment: Alignment.topCenter,
              padding: EdgeInsets.only(top: Adapt.widthPt(46)+MediaQuery.of(context).padding.top),
              child: SingleChildScrollView(
                child:Column(
                  children: [
                    _buildCommonClass(),
                    const Padding(padding: EdgeInsets.only(bottom: 16),),
                    // _buildCOVID_19(),
                    // const Padding(padding: EdgeInsets.only(bottom: 16),),
                    // _buildApproval(),
                    // const Padding(padding: EdgeInsets.only(bottom: 16),),
                    _buildThirdParty(),
                    const Padding(padding: EdgeInsets.only(bottom: 16),),
                    _buildOther(),
                    const Padding(padding: EdgeInsets.only(bottom: 16),),
                    _buildATC(),
                    Padding(padding: EdgeInsets.only(bottom: MediaQuery.of(context).padding.bottom),),
                  ],
                ) ,
              ) ,),
          )
        ],
      ) ,
    );

  }
  //刷新状态
  void refreshStatus(bool _bool){
      for (var e in homeDataRe) {
        e[3]='$_bool';
      }
      for (var e in homeData) {
        e[3]='$_bool';
      }
      for (var e in homeData1) {
        e[3]='$_bool';
      }
      for (var e in homeData2) {
        e[3]='$_bool';
      }
      for (var e in homeData3) {
        e[3]='$_bool';
      }
      for (var e in homeDataOther) {
        e[3]='$_bool';
      }
  }
  void writeCache(){
    //写入本地缓存
    var s = GetStorage();
    s.write('cyl', homeData);
    s.write('spl', homeData1);
    s.write('COVID_19', homeData2);
    s.write('dsf', homeData3);
    s.write('class_0', homeDataRe);
    s.write('class_Other', homeDataOther);
  }
}
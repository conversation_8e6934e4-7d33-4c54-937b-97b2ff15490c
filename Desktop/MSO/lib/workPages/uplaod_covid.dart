import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:amkor/components/tabbar_v2.dart';
import 'package:amkor/services/dto/uploadCovidDto.dart';
import 'package:amkor/services/employeeService.dart';
// import 'package:date_time_picker/date_time_picker.dart'; // 已移除，使用Flutter内置日期选择器
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
// import 'package:flutter_image_compress/flutter_image_compress.dart';
// import 'package:flutter_picker/Picker.dart'; // 暂时注释，等待兼容版本
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:open_file/open_file.dart';
// import 'package:wechat_assets_picker/wechat_assets_picker.dart';
import '../model/kvPair.dart';
import '../utils/colors.dart';

class UplaodCovidPage extends StatefulWidget {
  UplaodCovidPage({Key? key, this.maxPicSize}) : super(key: key);
  num? maxPicSize = 204800;
  @override
  _UplaodCovidPageState createState() => _UplaodCovidPageState();
}

class _UplaodCovidPageState extends State<UplaodCovidPage> {
  final ImagePicker _imagePicker = ImagePicker();
  bool _isLoading = false;
  final _testResultCtrl = TextEditingController(text: "text2_5".tr);
  final formdata = UploadCovidInputDto(testResult: "text2_5".tr);

  ///web
  final formdataweb = UploadCovidInputDtoweb(testResult: "text2_5".tr);
  final _empService = EmployeeService();
  bool isChoosed = false;
  String filePath = "assets/icons/add_pic.png";

  @override
  void initState() {
    super.initState();
    formdata.testTime =
        "${DateTime.now().toString().split(':')[0]}:${DateTime.now().toString().split(':')[1]}";
    formdataweb.testTime =
        "${DateTime.now().toString().split(':')[0]}:${DateTime.now().toString().split(':')[1]}";
  }

  @override
  Widget build(BuildContext context) {
    var q = MediaQuery.of(context);
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.dark);
    return Scaffold(
      appBar: TabberV2(
        title: "title2".tr,
        showBack: true,
        themeIsDark: false,
      ),
      body: SingleChildScrollView(
        child: Container(
            constraints: BoxConstraints(
              minHeight: q.size.height - 60 - q.padding.top,
            ),
            padding: const EdgeInsets.all(25),
            child: _buildForm()),
      ),
    );
  }

  Widget _buildForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTestTime(),
        const Padding(padding: EdgeInsets.only(top: 20)),
        _buildTestResult(),
        const Padding(padding: EdgeInsets.only(top: 20)),
        _buildUploadResult(),
        const Padding(padding: EdgeInsets.only(top: 40)),
        const Padding(padding: EdgeInsets.only(top: 20)),
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              "text2_2".tr,
              style: const TextStyle(
                fontSize: 16,
                color: Color.fromRGBO(255, 0, 0, 1),
              ),
            ),
            Text(
              "text2_12".tr,
              style: const TextStyle(fontSize: 16),
            ),
          ],
        ),
        const Padding(padding: EdgeInsets.only(top: 20)),
        _buildBtn(),
        const Padding(padding: EdgeInsets.only(top: 20)),
        Center(
          child: Text(
            "text2_14".tr,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xff414141),
            ),
          ),
        ),
      ],
    );
  }

  /// 采样时间
  Widget _buildTestTime() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              "text2_1".tr,
              style: const TextStyle(fontSize: 16),
            ),
            Text(
              "text2_2".tr,
              style: const TextStyle(
                fontSize: 16,
                color: Color.fromRGBO(255, 0, 0, 1),
              ),
            ),
            Text(
              "text2_3".tr,
              style: const TextStyle(
                fontSize: 14,
                color: Color(0xff979797),
              ),
            ),
          ],
        ),
        const Padding(padding: EdgeInsets.only(top: 10)),
        ConstrainedBox(
          constraints: const BoxConstraints(
            maxHeight: 40,
          ),
          child: InkWell(
            onTap: () async {
              final DateTime? pickedDate = await showDatePicker(
                context: context,
                initialDate: DateTime.now().add(Duration(hours: -1)),
                firstDate: DateTime(2000),
                lastDate: DateTime.now(),
              );

              if (pickedDate != null) {
                final TimeOfDay? pickedTime = await showTimePicker(
                  context: context,
                  initialTime: TimeOfDay.now(),
                );

                if (pickedTime != null) {
                  final selectedDateTime = DateTime(
                    pickedDate.year,
                    pickedDate.month,
                    pickedDate.day,
                    pickedTime.hour,
                    pickedTime.minute,
                  );

                  final formattedDateTime =
                      "${selectedDateTime.year.toString().padLeft(4, '0')}-${selectedDateTime.month.toString().padLeft(2, '0')}-${selectedDateTime.day.toString().padLeft(2, '0')} ${selectedDateTime.hour.toString().padLeft(2, '0')}:${selectedDateTime.minute.toString().padLeft(2, '0')}";

                  setState(() {
                    formdata.testTime = formattedDateTime;
                    formdataweb.testTime = formattedDateTime;
                  });
                }
              }
            },
            child: Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    formdata.testTime ??
                        "${DateTime.now().toString().split(':')[0]}:${DateTime.now().toString().split(':')[1]}",
                    style: TextStyle(
                      color: formdata.testTime != null
                          ? Colors.black
                          : Colors.grey[600],
                    ),
                  ),
                  const Icon(
                    Icons.arrow_forward_ios,
                    size: 15,
                    color: Colors.grey,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  ///选择检测结果
  Widget _buildTestResult() {
    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              "text2_15".tr,
              style: TextStyle(fontSize: 16),
            ),
            Text(
              "text2_2".tr,
              style: const TextStyle(
                fontSize: 16,
                color: Color.fromRGBO(255, 0, 0, 1),
              ),
            )
          ],
        ),
        const Padding(padding: EdgeInsets.only(top: 10)),
        ConstrainedBox(
          constraints: const BoxConstraints(
            maxHeight: 40,
          ),
          child: TextFormField(
            controller: _testResultCtrl,
            readOnly: true,
            onTap: () {
              chooseCovidResult(context);
            },
            decoration: InputDecoration(
              hintText: "text2_16".tr,
              focusColor: CustomColors.ThemeColor,
              contentPadding: const EdgeInsets.all(10),
              border: const OutlineInputBorder(),
              focusedErrorBorder: const OutlineInputBorder(),
              suffixIcon: const Icon(
                Icons.arrow_forward_ios,
                size: 15,
              ),
            ),
            autovalidateMode: AutovalidateMode.onUserInteraction,
            // 校验用户名
            validator: (v) {
              return v!.trim().isNotEmpty ? null : "text2_17".tr;
            },
          ),
        ),
      ],
    );
  }

  ///上传检测结果截图
  Widget _buildUploadResult() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              "text2_11".tr,
              style: const TextStyle(fontSize: 16),
              maxLines: 2,
              softWrap: true,
            ),
            Text(
              "text2_2".tr,
              style: const TextStyle(
                fontSize: 16,
                color: Color.fromRGBO(255, 0, 0, 1),
              ),
            )
          ],
        ),
        const Padding(padding: EdgeInsets.only(top: 10)),
        InkWell(
          onTap: () async {
            final XFile? img = await _imagePicker.pickImage(
                source: ImageSource.gallery,
                maxHeight: 2400,
                maxWidth: 1080,
                imageQuality: 50);
            if (img == null) {
              return;
            }

            ///获取图片base64
            List<int> imagedata = await img.readAsBytes();
            final imageEncoded = base64.encode(imagedata);
            print("========Uint8List格式${await img.readAsBytes()}");
            print('========base64格式$imageEncoded');

            ///取出文件名

            ///取出文件格式
            var namepic = img.path.toString().split(".").last;

            var imgSize = await img.length();
            print("choose res:${img.path}   ${img.name} 核酸压缩后图片大小为 ${imgSize}");
            print('核酸图片限制大小为${widget.maxPicSize}');

            File? compressedFile;

            ///移动app端再次压缩
            if (!kIsWeb) {
              final compressedXFile =
                  await FlutterImageCompress.compressAndGetFile(
                img.path,
                img.path
                    .replaceAll('.jpg', '_compressed.jpg')
                    .replaceAll('.png', '_compressed.png'),
                quality: 50,
                minWidth: 1080,
                minHeight: 1080,
              );
              if (compressedXFile != null) {
                compressedFile = File(compressedXFile.path);
                imgSize = compressedFile.lengthSync();
                print("choose res:核酸压缩后图片大小为 ${imgSize}");
              }
            }

            ///压缩后的图片限制200kb
            if (imgSize >= widget.maxPicSize!) {
              Fluttertoast.showToast(msg: "Tips1".tr);
              return;
            }

            formdata.filePath = !kIsWeb ? compressedFile!.path : img.path;
            formdataweb.fileName = img.name;
            formdataweb.fileBase64 =
                !kIsWeb ? compressedFile!.path : imageEncoded;
            print('-=-=-=${img.path}=====${img.path}');

            setState(() {
              isChoosed = true;
              filePath = !kIsWeb ? compressedFile!.path : img.path;
            });
          },
          child: Container(
            width: 80,
            height: 80,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              image: DecorationImage(
                image: AssetImage(filePath),
                fit: BoxFit.fill, // 完全填充
              ),
            ),
            child: !kIsWeb
                ? isChoosed
                    ? Image.file(
                        File(filePath),
                        height: 80,
                        width: 80,
                      )
                    : Image(
                        image: AssetImage(filePath),
                        height: 80,
                        width: 80,
                      )
                : Image.network(
                    filePath,
                    height: 80,
                    width: 80,
                  ),
          ),
        )
      ],
    );
  }

  ///压缩图片方案三
//   Future<Uint8List> testCompressFile(File file) async {
//     var result = await FlutterImageCompress.compressWithFile(
//       file.absolute.path,
//       minWidth: 2300,
//       minHeight: 1500,
//       quality: 94,
//       rotate: 90,
//     );
//     print(file.lengthSync());
//     print(result!.length);
//     return result;
//   }

  ///上报按钮
  Widget _buildBtn() {
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        backgroundColor: CustomColors.ThemeColor,
        minimumSize: Size(MediaQuery.of(context).size.width, 48),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(6),
        ),
      ),
      onPressed: _isLoading
          ? null
          : () {
              setState(() {
                _isLoading = true;
              });
              upload();
            },
      child: _isLoading
          ? SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          : Text("text2_13".tr, style: TextStyle(color: Colors.white)),
    );
  }

  ///核酸结果
  void chooseCovidResult(BuildContext context) {
    List<KVPair> origianData = [
      KVPair("negative", "text2_5".tr),
      KVPair("positive", "text2_6".tr),
      // KVPair("waitingReview", "text2_7".tr),
      // KVPair("waitingUpload", "text2_8".tr),
    ];

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text("text2_15".tr),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: origianData.map((item) {
              return ListTile(
                title: Text(item.value),
                onTap: () {
                  _testResultCtrl.text = item.value;
                  formdata.testResult = item.value;
                  formdataweb.testResult = item.value;
                  setState(() {});
                  Navigator.of(context).pop();
                },
              );
            }).toList(),
          ),
        );
      },
    );
  }

  void upload() async {
    if (formdata.testTime == null || formdata.testTime!.isEmpty) {
      Fluttertoast.showToast(msg: "Tips2".tr);
      setState(() {
        _isLoading = false;
      });
      return;
    }
    var time = DateTime.parse(formdata.testTime!);
    if (time.isAfter(DateTime.now())) {
      Fluttertoast.showToast(msg: "Tips3".tr);
      setState(() {
        _isLoading = false;
      });
      return;
    }

    if (formdata.testResult == null || formdata.testResult!.isEmpty) {
      Fluttertoast.showToast(msg: "Tips4".tr);
      setState(() {
        _isLoading = false;
      });
      return;
    }

    if (!kIsWeb
        ? (formdata.filePath == null || formdata.filePath!.isEmpty)
        : (formdataweb.fileBase64 == null || formdataweb.fileBase64!.isEmpty)) {
      Fluttertoast.showToast(msg: "Tips5".tr);
      setState(() {
        _isLoading = false;
      });
      return;
    }

    var r = !kIsWeb
        ? await _empService.UploadCovid(formdata.clone())
        : await _empService.UploadCovidBase64(formdataweb.clone());

    ///web

    print('-=-=-=--=-=-=--=-=-=-=-=-=-$r');
    print(r);
    setState(() {
      _isLoading = false;
    });
    if (r == null) {
      Fluttertoast.showToast(msg: "Tips6".tr);
      return;
    }

    Fluttertoast.showToast(msg: r.message);
    if (!r.success) {
      return;
    }
    if (mounted) Navigator.of(context).pop();
  }

  @override
  void dispose() {
    _testResultCtrl.dispose();
    super.dispose();
  }
}

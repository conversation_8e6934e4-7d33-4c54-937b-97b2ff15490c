import 'dart:io';

import 'package:amkor/components/routes.dart';
import 'package:amkor/services/employeeService.dart';
import 'package:amkor/workPages/webview.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:amkor/utils/crypt.dart';
import 'package:get_storage/get_storage.dart';

import '../utils/adapt.dart';


class QaPage extends StatefulWidget {
  const QaPage({Key? key}) : super(key: key);
  @override
  _QaPageState createState() => _QaPageState();
}

class _QaPageState extends State<QaPage> {

  var response;
  String employeeId = Uri.encodeComponent(MyCrypt.aesEncrypt(GetStorage().read('employee')['employeeId']));
  String mobile = Uri.encodeComponent(MyCrypt.aesEncrypt(GetStorage().read('employee')['mobile']));
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    Wjdata();

  }
  Future<void> Wjdata() async {
    var r = await EmployeeService().Getquestionnaire(1);
    response=r?.data;

    setState(() {
    });
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.dark);
    return  Scaffold(
      appBar:  AppBar(
        backgroundColor: Colors.white,
        title:const Text('问卷调查',style: TextStyle(color: Colors.black),),
        centerTitle:true,
        leading:  IconButton(
          color: Colors.black,
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
        elevation: 0,
      ),


      body: Container(
        alignment: Alignment.topCenter,
        color:const Color.fromRGBO(240, 240, 240, 1),
        child:response!=null?
        ListView.builder(
          // physics:const NeverScrollableScrollPhysics(),
            shrinkWrap:true,
            itemCount:response.length,
            itemBuilder: (_, index) {
              return Padding(
                padding:const EdgeInsets.only(left: 12,top: 0,right: 12,bottom: 16),
                child: Container(
                  clipBehavior: Clip.hardEdge,
                  decoration: const BoxDecoration(
                    borderRadius: BorderRadius.all(Radius.circular(4)),
                    color:  Colors.white,
                  ),
                  height: MediaQuery.of(context).size.height *0.14,
                  width: MediaQuery.of(context).size.width*0.92,
                  child: GestureDetector(
                    child:Stack(
                      children: [
                        Image.network(response[index]['imageUrl']!=null?response[index]['imageUrl'].replaceAll("\\","/"):'',
                          width: MediaQuery.of(context).size.width *0.92,
                          height: MediaQuery.of(context).size.height *0.14,
                          fit: BoxFit.fill,
                          errorBuilder: (a,b,c){
                            return Image(
                              image: const AssetImage('assets/images/qa1.png'),
                              width: MediaQuery.of(context).size.width*0.92,
                              height: MediaQuery.of(context).size.height *0.14,
                              fit: BoxFit.fill,);
                          },
                        ),
                        Positioned(
                          bottom: 0,
                          left: 0,
                          child: Container(
                            alignment: Alignment.centerLeft,
                            width: MediaQuery.of(context).size.width ,
                            padding: const EdgeInsetsDirectional.all(4),
                            decoration: const BoxDecoration(
                              color: Color.fromRGBO(60, 77, 102, 0.5),
                            ),
                            child:Text(response[index]['title'] ,style:const TextStyle(fontSize: 16,color: Colors.white)
                            ),
                          ),
                        ),

                      ],
                    ),
                    ///报表
                    onTap: () {
                      _navWebview("", '${response[index]['url']}?employeeId=$employeeId&mobile=$mobile');
                      // _navWebViewZdy('',response[i]['url']);
                    },
                  ),

                ),
              );
            }): Padding(
          padding: const EdgeInsetsDirectional.only(top: 100),
          child: Column(
            children: [
              Image(
                image:const AssetImage('assets/images/wjdcnull.png'),
                width: Adapt.widthPt(220),
              ),
              const Text(
                "暂无问卷",
                style:TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Color(0xff808080)),
              ),
            ],
          ) ,
        ),



      ),

    );

  }


  ///网页跳转
  void _navWebview(String title, String url) {
    Navigator.push(context, MaterialPageRoute(builder: (context) {
      return WebviewPage(title: title, url: url);
    }));
  }
  ///自定义报表跳转
  void _navWebViewZdy(String title, String url) {
    // Navigator.push(context, MaterialPageRoute(builder: (context) {
    //   return WebviewPage(title: title, url: url);
    // }));
    Navigator.of(context).pushNamed(RouteNames.customReportPage).then((value) {});
  }
}

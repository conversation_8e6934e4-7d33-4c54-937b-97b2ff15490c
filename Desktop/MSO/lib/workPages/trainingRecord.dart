

import 'package:amkor/components/tabbar_v2.dart';
import 'package:amkor/model/MyBinding.dart';
import 'package:amkor/services/employeeService.dart';
import 'package:amkor/utils/Adapt.dart';
import 'package:amkor/utils/TreeView.dart';
import 'package:amkor/utils/verticalText.dart';
import 'package:amkor/workPages/ShuttleBusDetails.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:open_file/open_file.dart';



class TrainingRecord extends StatelessWidget {
  const TrainingRecord({Key? key}) : super(key: key);

  Widget build(BuildContext context) {
    double w=MediaQuery.of(context).size.width;
    double h=MediaQuery.of(context).size.height;
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        title:const Text('培训记录',style: TextStyle(color: Colors.black),),
        centerTitle:true,
        leading:  IconButton(
          color: Colors.black,
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: (){
            Get.delete<ShuttleBusData>();
            Navigator.of(context).pop();
          },
        ),
        elevation: 0,
      ),
      body:SingleChildScrollView(
          scrollDirection: Axis.vertical,
        padding: EdgeInsetsDirectional.only(start:w*0.02,end: w*0.02 ),
        child: Column(
            children:[
              _buildBalance(w),
              Padding(
                padding: EdgeInsets.only(top: Adapt.widthPt(20)),
              ),
              _buildRecord(w),
            ]
      )

      ),

    );
  }
}

/// 页面顶部的内容
Widget _buildBalance(w) {
  const color1 = const Color(0xFF2F60D7);
  const color2 = const Color(0xFF688BFF);
  const wordsColor=const Color(0xFFFFFFFF);
  const rateColor=const Color(0xFF00FF00);
  return SizedBox(
    child: Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.0),
      ),
      margin: EdgeInsets.fromLTRB(
          Adapt.widthPt(5), Adapt.widthPt(10), Adapt.widthPt(5), Adapt.widthPt(5)
     ),
      child: Column(
        mainAxisSize:  MainAxisSize.max,
        children: [
                     Container(
    margin: EdgeInsets.fromLTRB(
        Adapt.widthPt(10), Adapt.widthPt(25), Adapt.widthPt(10), Adapt.widthPt(25)
    ),
  width:  w,
  height: 120,
  decoration: BoxDecoration(
    borderRadius: BorderRadius.circular(16),
    gradient: LinearGradient(
  begin: Alignment.centerLeft,
  end: Alignment.centerRight,
  colors: [color1, color2],
  ),
  ),

child: Row(
  children: [
    Expanded(
        child:Image(
    image:const AssetImage("assets/images/tariningrecordpageicon.png"),

  )),
    Expanded(
      child:Column(
        mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('完成率',style: TextStyle(color:wordsColor,fontSize: 14.0,),),
            SizedBox(height: 5,),
            Text('100%',style: TextStyle(color:rateColor,fontSize: 22.0,fontWeight: FontWeight.bold,),),
          ],
      )
    ),

    Expanded(
        child:Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('未完成课程',style: TextStyle(color:wordsColor,fontSize: 14.0,),),
            SizedBox(height: 5,),
            Text('0',style: TextStyle(color:rateColor,fontSize: 22.0,fontWeight: FontWeight.bold,),),
          ],
        )
    ),
  ],
),
    

  ),

          ],

      ),
    ),
  );
}

Widget _buildRecord(w) {
  const wordsColor=const Color(0xFF979797);
  const ResultColor=const Color(0xFF3629B7);

  return Container(
      width:  w,
    alignment: Alignment.centerLeft,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.0),
        // boxShadow: [
        //   BoxShadow(
        //     color: Colors.black54,
        //     offset: Offset(5.0, 5.0),
        //     blurRadius: 15.0,
        //   ),
        // ],
      ),
      padding: EdgeInsets.fromLTRB(
          Adapt.widthPt(5), Adapt.widthPt(10), Adapt.widthPt(5), Adapt.widthPt(15)
      ),
    child:Column(
      children: [
        Container(
          child: Align(
            alignment: Alignment.centerLeft,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text('完成率明细',style: TextStyle(color: Colors.black,fontSize: 16.0,fontWeight: FontWeight.bold)),
            ),
          ),


        ),
        Container(
            width:  w,
            margin: EdgeInsets.fromLTRB(
                Adapt.widthPt(5), 0, Adapt.widthPt(5), Adapt.widthPt(15)
            ),
            padding: EdgeInsets.fromLTRB(
                Adapt.widthPt(25), Adapt.widthPt(15), Adapt.widthPt(25), Adapt.widthPt(15)
            ),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Colors.red,
                width: 1.0,
              ),
            ),

            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('课程名称',style: TextStyle(color: Colors.black,fontSize: 16.0,fontWeight: FontWeight.bold)),
                Padding(
                  padding: EdgeInsets.only(top: Adapt.widthPt(7)),
                ),
                Text('Bumping Process Introduction',style: TextStyle(color: Colors.black,fontSize: 16.0,fontWeight: FontWeight.bold)),
                Padding(
                  padding: EdgeInsets.only(top: Adapt.widthPt(5)),
                ),
                Row(
                  children: <Widget>[
                    Expanded(
                           child: Align(
                             alignment: Alignment.centerLeft, // 内容居左
                              child: Text('课程编号',style: TextStyle(color: wordsColor)),
                           ),

                    ),
                    Expanded(
                       child: Align(
                          alignment: Alignment.centerRight, // 内容居左
                          child: Text('HT0008',style: TextStyle(color: ResultColor,fontWeight: FontWeight.bold)),
                        ),

                    ),

                   ]
                 ),
                Padding(
                  padding: EdgeInsets.only(top: Adapt.widthPt(5)),
                ),
                Row(
                    children: <Widget>[
                      Expanded(
                        child: Align(
                          alignment: Alignment.centerLeft, // 内容居左
                          child: Text('课程类型',style: TextStyle(color: wordsColor)),
                        ),

                      ),
                      Expanded(
                        child: Align(
                          alignment: Alignment.centerRight, // 内容居左
                          child: Text('必修课',style: TextStyle(color: ResultColor,fontWeight: FontWeight.bold)),
                        ),

                      ),

                    ]
                ),
                Padding(
                  padding: EdgeInsets.only(top: Adapt.widthPt(5)),
                ),
                Row(
                    children: <Widget>[
                      Expanded(
                        child: Align(
                          alignment: Alignment.centerLeft, // 内容居左
                          child: Text('完成日期',style: TextStyle(color: wordsColor)),
                        ),

                      ),
                      Expanded(
                        child: Align(
                          alignment: Alignment.centerRight, // 内容居左
                          child: Text('2024-02-25 12:00',style: TextStyle(color: ResultColor,fontWeight: FontWeight.bold)),
                        ),

                      ),

                    ]
                ),
                Padding(
                  padding: EdgeInsets.only(top: Adapt.widthPt(5)),
                ),
                Row(
                    children: <Widget>[
                      Expanded(
                        child: Align(
                          alignment: Alignment.centerLeft, // 内容居左
                          child: Text('培训结果',style: TextStyle(color: wordsColor)),
                        ),

                      ),
                      Expanded(
                        child: Align(
                          alignment: Alignment.centerRight, // 内容居左
                          child: Text('已通过',style: TextStyle(color: ResultColor,fontWeight: FontWeight.bold)),
                        ),

                      ),

                    ]
                ),

              ],
            )
        ),
        Container(
            width:  w,
            margin: EdgeInsets.fromLTRB(
                Adapt.widthPt(5), 0, Adapt.widthPt(5), 0
            ),
            padding: EdgeInsets.fromLTRB(
                Adapt.widthPt(25), Adapt.widthPt(15), Adapt.widthPt(25), Adapt.widthPt(15)
            ),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: wordsColor,
                width: 1.0,
              ),
            ),

            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('课程名称',style: TextStyle(color: Colors.black,fontSize: 16.0,fontWeight: FontWeight.bold)),
                Padding(
                  padding: EdgeInsets.only(top: Adapt.widthPt(7)),
                ),
                Text('Bumping Process Introduction',style: TextStyle(color: Colors.black,fontSize: 16.0,fontWeight: FontWeight.bold)),
                Padding(
                  padding: EdgeInsets.only(top: Adapt.widthPt(5)),
                ),
                Row(
                    children: <Widget>[
                      Expanded(
                        child: Align(
                          alignment: Alignment.centerLeft, // 内容居左
                          child: Text('课程编号',style: TextStyle(color: wordsColor)),
                        ),

                      ),
                      Expanded(
                        child: Align(
                          alignment: Alignment.centerRight, // 内容居左
                          child: Text('HT0008',style: TextStyle(color: ResultColor,fontWeight: FontWeight.bold)),
                        ),

                      ),

                    ]
                ),
                Padding(
                  padding: EdgeInsets.only(top: Adapt.widthPt(5)),
                ),
                Row(
                    children: <Widget>[
                      Expanded(
                        child: Align(
                          alignment: Alignment.centerLeft, // 内容居左
                          child: Text('课程类型',style: TextStyle(color: wordsColor)),
                        ),

                      ),
                      Expanded(
                        child: Align(
                          alignment: Alignment.centerRight, // 内容居左
                          child: Text('必修课',style: TextStyle(color: ResultColor,fontWeight: FontWeight.bold)),
                        ),

                      ),

                    ]
                ),
                Padding(
                  padding: EdgeInsets.only(top: Adapt.widthPt(5)),
                ),
                Row(
                    children: <Widget>[
                      Expanded(
                        child: Align(
                          alignment: Alignment.centerLeft, // 内容居左
                          child: Text('完成日期',style: TextStyle(color: wordsColor)),
                        ),

                      ),
                      Expanded(
                        child: Align(
                          alignment: Alignment.centerRight, // 内容居左
                          child: Text('2024-02-25 12:00',style: TextStyle(color: ResultColor,fontWeight: FontWeight.bold)),
                        ),

                      ),

                    ]
                ),
                Padding(
                  padding: EdgeInsets.only(top: Adapt.widthPt(5)),
                ),
                Row(
                    children: <Widget>[
                      Expanded(
                        child: Align(
                          alignment: Alignment.centerLeft, // 内容居左
                          child: Text('培训结果',style: TextStyle(color: wordsColor)),
                        ),

                      ),
                      Expanded(
                        child: Align(
                          alignment: Alignment.centerRight, // 内容居左
                          child: Text('已通过',style: TextStyle(color: ResultColor,fontWeight: FontWeight.bold)),
                        ),

                      ),

                    ]
                ),

              ],
            )
        ),
        Container(
            width:  w,
            margin: EdgeInsets.fromLTRB(
                Adapt.widthPt(5), Adapt.widthPt(15), Adapt.widthPt(5), Adapt.widthPt(5)
            ),
            padding: EdgeInsets.fromLTRB(
                Adapt.widthPt(25), Adapt.widthPt(15), Adapt.widthPt(25), Adapt.widthPt(15)
            ),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: wordsColor,
                width: 1.0,
              ),
            ),

            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('课程名称',style: TextStyle(color: Colors.black,fontSize: 16.0,fontWeight: FontWeight.bold)),
                Padding(
                  padding: EdgeInsets.only(top: Adapt.widthPt(7)),
                ),
                Text('Bumping Process Introduction',style: TextStyle(color: Colors.black,fontSize: 16.0,fontWeight: FontWeight.bold)),
                Padding(
                  padding: EdgeInsets.only(top: Adapt.widthPt(5)),
                ),
                Row(
                    children: <Widget>[
                      Expanded(
                        child: Align(
                          alignment: Alignment.centerLeft, // 内容居左
                          child: Text('课程编号',style: TextStyle(color: wordsColor)),
                        ),

                      ),
                      Expanded(
                        child: Align(
                          alignment: Alignment.centerRight, // 内容居左
                          child: Text('HT0008',style: TextStyle(color: ResultColor,fontWeight: FontWeight.bold)),
                        ),

                      ),

                    ]
                ),
                Padding(
                  padding: EdgeInsets.only(top: Adapt.widthPt(5)),
                ),
                Row(
                    children: <Widget>[
                      Expanded(
                        child: Align(
                          alignment: Alignment.centerLeft, // 内容居左
                          child: Text('课程类型',style: TextStyle(color: wordsColor)),
                        ),

                      ),
                      Expanded(
                        child: Align(
                          alignment: Alignment.centerRight, // 内容居左
                          child: Text('必修课',style: TextStyle(color: ResultColor,fontWeight: FontWeight.bold)),
                        ),

                      ),

                    ]
                ),
                Padding(
                  padding: EdgeInsets.only(top: Adapt.widthPt(5)),
                ),
                Row(
                    children: <Widget>[
                      Expanded(
                        child: Align(
                          alignment: Alignment.centerLeft, // 内容居左
                          child: Text('完成日期',style: TextStyle(color: wordsColor)),
                        ),

                      ),
                      Expanded(
                        child: Align(
                          alignment: Alignment.centerRight, // 内容居左
                          child: Text('2024-02-25 12:00',style: TextStyle(color: ResultColor,fontWeight: FontWeight.bold)),
                        ),

                      ),

                    ]
                ),
                Padding(
                  padding: EdgeInsets.only(top: Adapt.widthPt(5)),
                ),
                Row(
                    children: <Widget>[
                      Expanded(
                        child: Align(
                          alignment: Alignment.centerLeft, // 内容居左
                          child: Text('培训结果',style: TextStyle(color: wordsColor)),
                        ),

                      ),
                      Expanded(
                        child: Align(
                          alignment: Alignment.centerRight, // 内容居左
                          child: Text('已通过',style: TextStyle(color: ResultColor,fontWeight: FontWeight.bold)),
                        ),

                      ),

                    ]
                ),

              ],
            )
        ),
        Container(
            width:  w,
            margin: EdgeInsets.fromLTRB(
                Adapt.widthPt(5), Adapt.widthPt(10), Adapt.widthPt(5), Adapt.widthPt(15)
            ),
            padding: EdgeInsets.fromLTRB(
                Adapt.widthPt(25), Adapt.widthPt(15), Adapt.widthPt(25), Adapt.widthPt(15)
            ),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: wordsColor,
                width: 1.0,
              ),
            ),

            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('课程名称',style: TextStyle(color: Colors.black,fontSize: 16.0,fontWeight: FontWeight.bold)),
                Padding(
                  padding: EdgeInsets.only(top: Adapt.widthPt(7)),
                ),
                Text('Bumping Process Introduction',style: TextStyle(color: Colors.black,fontSize: 16.0,fontWeight: FontWeight.bold)),
                Padding(
                  padding: EdgeInsets.only(top: Adapt.widthPt(5)),
                ),
                Row(
                    children: <Widget>[
                      Expanded(
                        child: Align(
                          alignment: Alignment.centerLeft, // 内容居左
                          child: Text('课程编号',style: TextStyle(color: wordsColor)),
                        ),

                      ),
                      Expanded(
                        child: Align(
                          alignment: Alignment.centerRight, // 内容居左
                          child: Text('HT0008',style: TextStyle(color: ResultColor,fontWeight: FontWeight.bold)),
                        ),

                      ),

                    ]
                ),
                Padding(
                  padding: EdgeInsets.only(top: Adapt.widthPt(5)),
                ),
                Row(
                    children: <Widget>[
                      Expanded(
                        child: Align(
                          alignment: Alignment.centerLeft, // 内容居左
                          child: Text('课程类型',style: TextStyle(color: wordsColor)),
                        ),

                      ),
                      Expanded(
                        child: Align(
                          alignment: Alignment.centerRight, // 内容居左
                          child: Text('必修课',style: TextStyle(color: ResultColor,fontWeight: FontWeight.bold)),
                        ),

                      ),

                    ]
                ),
                Padding(
                  padding: EdgeInsets.only(top: Adapt.widthPt(5)),
                ),
                Row(
                    children: <Widget>[
                      Expanded(
                        child: Align(
                          alignment: Alignment.centerLeft, // 内容居左
                          child: Text('完成日期',style: TextStyle(color: wordsColor)),
                        ),

                      ),
                      Expanded(
                        child: Align(
                          alignment: Alignment.centerRight, // 内容居左
                          child: Text('2024-02-25 12:00',style: TextStyle(color: ResultColor,fontWeight: FontWeight.bold)),
                        ),

                      ),

                    ]
                ),
                Padding(
                  padding: EdgeInsets.only(top: Adapt.widthPt(5)),
                ),
                Row(
                    children: <Widget>[
                      Expanded(
                        child: Align(
                          alignment: Alignment.centerLeft, // 内容居左
                          child: Text('培训结果',style: TextStyle(color: wordsColor)),
                        ),

                      ),
                      Expanded(
                        child: Align(
                          alignment: Alignment.centerRight, // 内容居左
                          child: Text('已通过',style: TextStyle(color: ResultColor,fontWeight: FontWeight.bold)),
                        ),

                      ),

                    ]
                ),

              ],
            )
        ),

      ],
    )
  );
}



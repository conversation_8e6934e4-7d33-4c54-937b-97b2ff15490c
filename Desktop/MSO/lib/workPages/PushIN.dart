import 'dart:io';
import 'package:amkor/services/dto/uploadCovidDto.dart';
import 'package:amkor/utils/addj.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:image_picker/image_picker.dart';
import 'package:amkor/components/tabbar_v2.dart';
import 'package:amkor/utils/colors.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';

class PushInPage extends StatefulWidget {
  const PushInPage({Key? key}) : super(key: key);

  @override
  State<PushInPage> createState() => _PushInPagePageState();
}

class _PushInPagePageState extends State<PushInPage> {
  //被推荐人身份证号
  TextEditingController _JobController = TextEditingController();
  //被推荐人手机号
  TextEditingController _Telephonetroller = TextEditingController();
  bool _isLoading = false;
  //被推荐人姓名
  TextEditingController _nameController = TextEditingController();
  //工作经历
  TextEditingController recentWorkController = TextEditingController();
  //反馈
  TextEditingController feedbackController = TextEditingController();
  //被推荐人身份证号
  String _Job = "";
  //被推荐人手机号
  String _Telephone = "";
  //被推荐人姓名
  String _name = "";
  //工作经历
  String recentWork = "";
  //反馈
  String feedback = "";

  ///
  //FocusNode
  FocusNode jkNode = new FocusNode();

  GlobalKey _formKey = GlobalKey<FormState>();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: TabberV2(
          title: "内部推荐".tr,
          showBack: true,
        ),
        resizeToAvoidBottomInset: false,
        body: SingleChildScrollView(
            child: Center(
          child: _buildBody(),
        )));
  }

  @override
  void reassemble() {
    // TODO: implement reassemble
    super.reassemble();
  }

  final d = DateTime.now();
  bool int1 = true;

  int int_ = 0;
  final List<List<String>> _sportValueData = [];
  final ImagePicker _imagePicker = ImagePicker();

  ///传路径
  final formdata = UploadCovidInputDto();

  ///
  ///
  ///
  ///
  String filePath = "assets/icons/add_pic.png";
  @override
  void initState() {
    // TODO: implement activate
    super.initState();
    for (int i = 0; i < Global().homeData1.length; i++) {
      _sportValueData.add([
        '',
        '',
        '',
        '',
        '',
        '',
      ]);
    }
  }

  ///表按钮
  Widget _Radio(List<Map<String, dynamic>> reportData) {
    return Column(
      children: [
        for (int i = 0; i < reportData.length; i++)
          Padding(
              padding: const EdgeInsets.only(bottom: 30),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.only(bottom: 10),
                    child: Text(Global().homeData1[i]['题目']),
                  ),
                  Container(
                    decoration: const BoxDecoration(
                      border: Border(
                          top: BorderSide(color: Colors.black12, width: 1),
                          left: BorderSide(color: Colors.black12, width: 1),
                          right: BorderSide(
                            color: Colors.black12,
                            width: 1,
                          )),
                    ),
                    child: Column(
                      children: [
                        ///单选
                        if (reportData[i]['类型'] == '单选')
                          for (int j = 0; j < reportData[i]['选项'].length; j++)
                            Container(
                              decoration: const BoxDecoration(
                                border: Border(
                                    bottom: BorderSide(
                                  color: Colors.black12,
                                  width: 1,
                                )),
                              ),
                              child: Row(
                                children: [
                                  Radio<String>(
                                    value: reportData[i]['选项'][j],
                                    groupValue: _sportValueData[i][0],
                                    onChanged: (value) {
                                      setState(() {
                                        _sportValueData[i][0] = value!;
                                      });
                                    },
                                  ),
                                  Expanded(
                                    child: Text(reportData[i]['选项'][j]),
                                  )
                                ],
                              ),
                            ),

                        ///多选
                        if (reportData[i]['类型'] == '多选')
                          for (int j = 0; j < reportData[i]['选项'].length; j++)
                            Container(
                              decoration: const BoxDecoration(
                                border: Border(
                                    bottom: BorderSide(
                                  color: Colors.black12,
                                  width: 1,
                                )),
                              ),
                              child: Row(
                                children: [
                                  Checkbox(
                                    value: _sportValueData[i][j] ==
                                        reportData[i]['选项'][j],
                                    onChanged: (value) {
                                      setState(() {
                                        _sportValueData[i][j] =
                                            _sportValueData[i][j] ==
                                                    reportData[i]['选项'][j]
                                                ? ''
                                                : reportData[i]['选项'][j];
                                      });
                                    },
                                  ),
                                  Text(reportData[i]['选项'][j]),
                                ],
                              ),
                            ),

                        ///图片
                        if (reportData[i]['类型'] == '图片')
                          Container(
                            decoration: const BoxDecoration(
                              border: Border(
                                  bottom: BorderSide(
                                color: Colors.black12,
                                width: 1,
                              )),
                            ),
                            child: Row(
                              children: [
                                InkWell(
                                  onTap: () async {
                                    final XFile? img =
                                        await _imagePicker.pickImage(
                                            source: ImageSource.gallery,
                                            maxHeight: 2400,
                                            maxWidth: 1080,
                                            imageQuality: 50);
                                    if (img == null) {
                                      return;
                                    }
                                    var imgSize = await img.length();

                                    File? compressedFile;
                                    final compressedXFile =
                                        await FlutterImageCompress
                                            .compressAndGetFile(
                                      img.path,
                                      img.path
                                          .replaceAll('.jpg', '_compressed.jpg')
                                          .replaceAll(
                                              '.png', '_compressed.png'),
                                      quality: 50,
                                      minWidth: 1080,
                                      minHeight: 1080,
                                    );
                                    if (compressedXFile != null) {
                                      compressedFile =
                                          File(compressedXFile.path);
                                      imgSize = compressedFile.lengthSync();
                                    }

                                    ///压缩后的图片限制200kb
                                    if (imgSize >= 2400000) {
                                      Fluttertoast.showToast(msg: "Tips1".tr);
                                      return;
                                    }
                                    formdata.filePath =
                                        compressedFile?.path ?? img.path;

                                    print('-=-=-=${img.path}=====${img.path}');

                                    setState(() {
                                      // filePath = compressedFile!.path;
                                      _sportValueData[i][0] =
                                          compressedFile!.path;
                                      _sportValueData[i][1] = "true";
                                    });
                                  },
                                  child: Container(
                                    width: 60,
                                    height: 60,
                                    alignment: Alignment.center,
                                    decoration: const BoxDecoration(
                                      image: DecorationImage(
                                        image: AssetImage(
                                            "assets/icons/add_pic.png"),
                                        fit: BoxFit.fill, // 完全填充
                                      ),
                                    ),
                                    child: (_sportValueData[i][1] == ''
                                            ? false
                                            : _sportValueData[i][1] == "true")
                                        ? Image.file(
                                            File(_sportValueData[i][0] == ''
                                                ? "assets/icons/add_pic.png"
                                                : _sportValueData[i][0]),
                                            height: 60,
                                            width: 60,
                                          )
                                        : Image(
                                            image: AssetImage(
                                                _sportValueData[i][0] == ''
                                                    ? "assets/icons/add_pic.png"
                                                    : _sportValueData[i][0]),
                                            height: 60,
                                            width: 60,
                                          ),
                                  ),
                                ),
                                const Padding(
                                  padding: EdgeInsets.only(left: 20),
                                ),
                                Text(reportData[i]['选项']),
                              ],
                            ),
                          )
                      ],
                    ),
                  )
                ],
              ))
      ],
    );
  }

  Widget _buildBody() {
    return Container(
      alignment: Alignment.center,
      width: kIsWeb &&
              (defaultTargetPlatform == TargetPlatform.macOS ||
                  defaultTargetPlatform == TargetPlatform.windows)
          ? MediaQuery.of(context).size.width * 0.26
          : MediaQuery.of(context).size.width * 0.92,
      child: Column(
        children: [
          Container(
            width: MediaQuery.of(context).size.width * 0.80,
            child: const Image(
              image: AssetImage("assets/images/nbsqb_bj.png"),
              fit: BoxFit.fitWidth,
            ),
          ),
          const Padding(padding: EdgeInsets.only(top: 10)),
          _buildForm(),
        ],
      ),
    );
  }

  Widget _buildForm() {
    return Form(
        key: _formKey,
        child: Container(
          padding: EdgeInsets.all(8),
          color: Colors.white,
          child: Column(
            children: [
              const Padding(padding: EdgeInsets.only(top: 20)),
              _buildJob(),
              const Padding(padding: EdgeInsets.only(top: 20)),
              _buildName(),
              const Padding(padding: EdgeInsets.only(top: 20)),
              _buildDepartment(),
              const Padding(padding: EdgeInsets.only(top: 20)),
              _buildTelephone(),
              const Padding(padding: EdgeInsets.only(top: 20)),
              // _buildFeedback(),
              _Radio(Global().homeData1),
              const Text(
                "(公司承诺将保护您的相关个人隐私信息)",
                style: TextStyle(color: Colors.grey, fontSize: 14),
              ),
              const Padding(padding: EdgeInsets.only(top: 20)),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: CustomColors.ThemeColor,
                  minimumSize: Size(double.infinity, 48),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
                onPressed: _isLoading
                    ? null
                    : () {
                        setState(() {
                          _isLoading = true;
                        });
                        _updatePwdClick();
                      },
                child: _isLoading
                    ? SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Text("提交", style: TextStyle(color: Colors.white)),
              )
            ],
          ),
        ));
  }

  Widget _buildJob() {
    return TextFormField(
      autofocus: true,
      controller: _JobController,
      // inputFormatters: [FilteringTextInputFormatter.allow(RegExp("[0-9.]")),],
      onChanged: (e) {
        setState(() {
          _Job = e;
        });
      },

      decoration: InputDecoration(
        border: const OutlineInputBorder(
            borderSide: BorderSide(
                color: Colors.black, width: 3.0, style: BorderStyle.solid)),
        // enabledBorder:const OutlineInputBorder(
        //     borderSide: BorderSide(color: Colors.black, width: 3.0, style: BorderStyle.solid)
        // ) ,
        // focusedBorder:const OutlineInputBorder(
        //     borderSide: BorderSide(color: Colors.black, width: 3.0, style: BorderStyle.solid)
        // ),
        isDense: true,
        contentPadding: EdgeInsets.fromLTRB(10, 10, 10, 0),
        hintText: "请输入身份证号".tr,
        floatingLabelBehavior: FloatingLabelBehavior.auto,
        label: Text(
          "被推荐人身份证号:".tr,
          style: TextStyle(color: CustomColors.ThemeColor, fontSize: 16),
        ),
        focusColor: const Color.fromRGBO(29, 64, 133, 1),
        // focusedBorder: const UnderlineInputBorder(),
        suffixIcon: Offstage(
          offstage: _Job == "",
        ),
      ),
      autovalidateMode: AutovalidateMode.onUserInteraction,
      validator: (v) {
        if (!v!.trim().isNotEmpty) {
          return "工号不能为空".tr;
        }
        return null;
      },
    );
  }

  Widget _buildName() {
    return TextFormField(
      autofocus: true,
      controller: _nameController,
      // inputFormatters: [FilteringTextInputFormatter.allow(RegExp("[0-9.]")),],
      onChanged: (e) {
        setState(() {
          _name = e;
        });
      },

      decoration: InputDecoration(
        border: const OutlineInputBorder(
            borderSide: BorderSide(
                color: Colors.black, width: 3.0, style: BorderStyle.solid)),
        isDense: true,
        contentPadding: EdgeInsets.fromLTRB(10, 10, 10, 0),
        hintText: "请输入被推荐人姓名".tr,
        floatingLabelBehavior: FloatingLabelBehavior.auto,
        label: Text(
          "被推荐人中文姓名:".tr,
          style: TextStyle(color: CustomColors.ThemeColor, fontSize: 16),
        ),
        focusColor: const Color.fromRGBO(29, 64, 133, 1),
        // focusedBorder: const UnderlineInputBorder(),
        suffixIcon: Offstage(
          offstage: _name == "",
        ),
      ),
      autovalidateMode: AutovalidateMode.onUserInteraction,
      validator: (v) {
        if (!v!.trim().isNotEmpty) {
          return "姓名不能为空".tr;
        }
        return null;
      },
    );
  }

  Widget _buildDepartment() {
    return TextFormField(
      autofocus: true,
      controller: recentWorkController,
      onChanged: (e) {
        setState(() {
          recentWork = e;
        });
      },
      decoration: InputDecoration(
        border: const OutlineInputBorder(
            borderSide: BorderSide(
                color: Colors.black, width: 3.0, style: BorderStyle.solid)),
        isDense: true,
        contentPadding: EdgeInsets.fromLTRB(10, 10, 10, 0),
        hintText: "举例:上海XXX公司担任一线员工".tr,
        floatingLabelBehavior: FloatingLabelBehavior.auto,
        label: Text(
          "被推荐人最近一份工作:".tr,
          style: TextStyle(color: CustomColors.ThemeColor, fontSize: 16),
        ),
        focusColor: const Color.fromRGBO(29, 64, 133, 1),
        // focusedBorder: const UnderlineInputBorder(),
        suffixIcon: Offstage(
          offstage: recentWork == "",
        ),
      ),
      autovalidateMode: AutovalidateMode.onUserInteraction,
      validator: (v) {
        if (!v!.trim().isNotEmpty) {
          return "部门不能为空".tr;
        }
        return null;
      },
    );
  }

  Widget _buildTelephone() {
    return TextFormField(
      autofocus: true,
      controller: _Telephonetroller,
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp("[0-9.()_]")),
      ],
      onChanged: (e) {
        setState(() {
          _Telephone = e;
        });
      },
      decoration: InputDecoration(
        border: const OutlineInputBorder(
            borderSide: BorderSide(
                color: Colors.black, width: 3.0, style: BorderStyle.solid)),
        isDense: true,
        contentPadding: EdgeInsets.fromLTRB(10, 10, 10, 0),
        hintText: "请输入手机号".tr,
        floatingLabelBehavior: FloatingLabelBehavior.auto,
        label: Text(
          "被推荐人手机号".tr,
          style: TextStyle(color: CustomColors.ThemeColor, fontSize: 16),
        ),
        focusColor: const Color.fromRGBO(29, 64, 133, 1),
        // focusedBorder: const UnderlineInputBorder(),
        suffixIcon: Offstage(
          offstage: _Telephone == "",
        ),
      ),
      autovalidateMode: AutovalidateMode.onUserInteraction,
      validator: (v) {
        if (!v!.trim().isNotEmpty) {
          return "手机号不能为空".tr;
        }
        return null;
      },
    );
  }

  Widget _buildFeedback() {
    return TextFormField(
      autofocus: true,
      controller: feedbackController,
      onChanged: (e) {
        setState(() {
          feedback = e;
        });
      },
      decoration: InputDecoration(
        border: const OutlineInputBorder(
            borderSide: BorderSide(
                color: Colors.black, width: 3.0, style: BorderStyle.solid)),

        hintText: "如果第10题选择“是”请填写具体的部门和工序；选择“否”的填写“无”".tr,
        floatingLabelBehavior: FloatingLabelBehavior.auto,
        label: Text(
          "如果第10题选择“是”请填写具体的部门和工序；选择“否”的填写“无”".tr,
          style: TextStyle(color: CustomColors.ThemeColor, fontSize: 16),
        ),

        focusColor: const Color.fromRGBO(29, 64, 133, 1),
        // focusedBorder: const UnderlineInputBorder(),
        suffixIcon: Offstage(
          offstage: feedback == "",
        ),
      ),
      maxLines: 3,
      minLines: 1,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      validator: (v) {
        if (!v!.trim().isNotEmpty) {
          return "问题不能为空".tr;
        }
        return null;
      },
    );
  }

  void _updatePwdClick() async {
    setState(() {
      _isLoading = false;
    });
    //   if ((_formKey.currentState as FormState).validate() == false) {
    //     setState(() {
    //       _isLoading = false;
    //     });
    //     return;
    //   }
    //
    //   var job = _JobController.text;
    //   var telephone = _Telephonetroller.text;
    //   var name = _nameController.text;
    //   var department = _departmentController.text;
    //
    //   ///忘记密码
    //   ///
    //   ///
    //   ///
    //   ///
    //   ///
    //   var resp = await AuthService().ResetPassword(job,telephone);
    //   print("===================================$resp");
    //
    //   _btnCtrl.stop();
    //
    //
    //   if (resp == null) {
    //     Fluttertoast.showToast(msg: "Tips6".tr);
    //     return;
    //   }
    //   if(resp.message=="") {
    //     Fluttertoast.showToast(msg: "text6_12".tr);
    //   }else
    //     Fluttertoast.showToast(msg: resp.message);
    //   if (resp.success) _back();
    // }
    //
    // void _back() {
    //   ///重制密码成功后回到登陆界面重新登陆
    //   AuthService().GetVerificationCode();
    //   Navigator.pop(context);
  }

  @override
  void dispose() {
    _JobController.dispose();
    _nameController.dispose();
    _Telephonetroller.dispose();
    recentWorkController.dispose();
    feedbackController.dispose();
    // WidgetsBinding.instance.removeObserver(this);

    super.dispose();
  }
}

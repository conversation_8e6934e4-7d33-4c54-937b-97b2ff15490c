import 'dart:io';

import 'package:amkor/services/employeeService.dart';
import 'package:amkor/utils/Adapt.dart';
import 'package:amkor/widget_gj/My_Icons.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
// import 'package:flutter_picker/Picker.dart'; // 暂时注释，等待兼容版本
import 'package:get/get.dart';

class RiskAreaPage extends StatefulWidget {
  const RiskAreaPage({Key? key}) : super(key: key);

  @override
  State<RiskAreaPage> createState() => _RiskAreaPageState();
}

class _RiskAreaPageState extends State<RiskAreaPage>
    with TickerProviderStateMixin {
  final TextEditingController typeCtrl = TextEditingController();
  final TextEditingController typeCtrl1 = TextEditingController();
  Animation<double>? animation;
  AnimationController? animationController;
  @override
  void dispose() {
    typeCtrl.dispose();
    typeCtrl1.dispose();
    animationController?.dispose();
    super.dispose();
  }
  // final _btnCtrl = RoundedLoadingButtonController();

  // final Map<String, List<String>>? NonShanghaidata={};
  var NonShanghaidata;
  var _item;
  var Epidemic = EpidemicPreventionInfoApp();
  Future<void> _initData() async {
    var q = await Epidemic.GetCityDictionary();
    NonShanghaidata = q?.data;

    var a = await Epidemic.GetEpidemicPreventionShInfoPage();

    _item = a?.data[0];
    for (int i = 0; i < _item['list'].length; i++) {
      _item['list'][i]['id'] = 0;
      _item['list'][i]['bool'] = false;
    }
    setState(() {});
    var aaa = QuestionApp().AskQuestion(
      'text',
      1,
    );
    print('=============');
  }

  Future<void> _initDatas() async {
    WebSocket _webSocket =
        await WebSocket.connect("wss://echo.websocket.events");
    //监听函数
    _webSocket.listen(_onReceive, onDone: () {
      print('连接关闭时响应');
    }, onError: (error) {
      print('发生错误');
    }, cancelOnError: true);
    _webSocket.add("客户端发送过去的");
    setState(() {});
  }

  void _onReceive(data) {
    print("收到服务器数据:" + data);
  }

  _changeOpacity(bool expand) {
    setState(() {
      if (expand) {
        animationController?.forward(); //正向
      } else {
        animationController?.reverse(); //反向
      }
    });
  }

  @override
  void initState() {
    // TODO: implement activate
    super.initState();
    _initData();
    if (!kIsWeb) {
      Adapt.setAndroid();
    }
    animationController = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 200) //延迟、延时
        );
    animation = Tween(begin: 0.0, end: 0.25)
        .animate(animationController!); //这里最大是1  而0.25是为了  0.25*360=90，刚好旋转90°
  }

  Widget _buildLiveTypePicker() {
    return TextFormField(
      // autofocus: true,
      readOnly: true,
      style: const TextStyle(
        fontSize: 14,
      ),
      controller: typeCtrl,
      onTap: () {
        chooseLiveType(context);
      },
      decoration: InputDecoration(
        contentPadding: const EdgeInsetsDirectional.only(start: 4),
        hintText: "上  海".tr,
        hintStyle: const TextStyle(
          fontSize: 14,
        ),
        focusColor: const Color.fromRGBO(29, 64, 133, 1),
        focusedBorder: const OutlineInputBorder(borderSide: BorderSide.none),
        enabledBorder: const OutlineInputBorder(borderSide: BorderSide.none),
        suffixIcon: Icon(
          Icons.arrow_drop_down_rounded,
          size: 20,
          color: Colors.grey[500],
        ),
        suffixIconConstraints: const BoxConstraints(minWidth: 20, maxWidth: 20),
      ),
      autovalidateMode: AutovalidateMode.onUserInteraction,
    );
  }

  Widget _buildLiveTypePicker1() {
    return TextFormField(
      // autofocus: true,
      readOnly: true,
      style: const TextStyle(
          fontSize: 16, color: Colors.white, fontWeight: FontWeight.w300),
      textAlign: TextAlign.center,
      controller: typeCtrl1,
      onTap: () {
        chooseLiveType1(context);
      },
      decoration: InputDecoration(
        contentPadding: const EdgeInsetsDirectional.only(start: 4),
        hintText: "请选择".tr,
        hintStyle: const TextStyle(
            fontSize: 16,
            color: Color.fromRGBO(203, 216, 255, 1),
            fontWeight: FontWeight.w300),
        focusColor: const Color.fromRGBO(29, 64, 133, 1),
        focusedBorder: const OutlineInputBorder(borderSide: BorderSide.none),
        enabledBorder: const OutlineInputBorder(borderSide: BorderSide.none),
      ),
      autovalidateMode: AutovalidateMode.onUserInteraction,
    );
  }

  ///弹框
  void chooseLiveType(BuildContext context) {
    // 暂时使用简单的对话框替代 flutter_picker
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text("选择地区"),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                title: Text('上  海'),
                onTap: () {
                  setState(() {
                    typeCtrl.text = '上  海';
                  });
                  Navigator.of(context).pop();
                },
              ),
              ListTile(
                title: Text('非上海'),
                onTap: () {
                  setState(() {
                    typeCtrl.text = '非上海';
                  });
                  Navigator.of(context).pop();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  ///弹框
  void chooseLiveType1(BuildContext context) {
    // 暂时使用简单的对话框替代 flutter_picker
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text("选择地区"),
          content: Container(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text("功能升级中，暂时不可用"),
                SizedBox(height: 20),
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      typeCtrl1.text = "暂时不可用";
                    });
                    Navigator.of(context).pop();
                  },
                  child: Text("确定"),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  ///上海模块聚合
  Widget shanghai() {
    return Expanded(
        child: Column(
      children: [
        Container(
            padding:
                const EdgeInsetsDirectional.only(start: 4, bottom: 14, top: 14),
            width: MediaQuery.of(context).size.width - 32,
            decoration: const BoxDecoration(
              borderRadius: BorderRadiusDirectional.only(
                  topEnd: Radius.circular(10), topStart: Radius.circular(10)),
              color: Color.fromRGBO(201, 205, 212, 0.5),
            ),
            child: Text(
              '上 海（${_item == null ? 0 : _item['count']}）个',
              style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w300,
                  color: Colors.black),
            )),
        _item != null
            ? Expanded(
                child: Container(
                    width: MediaQuery.of(context).size.width - 32,
                    padding: const EdgeInsetsDirectional.only(
                        start: 10, end: 10, top: 10, bottom: 20),
                    decoration: const BoxDecoration(
                      borderRadius: BorderRadiusDirectional.only(
                          bottomEnd: Radius.circular(10),
                          bottomStart: Radius.circular(10)),
                      color: Colors.white,
                    ),
                    child: SizedBox(
                      width: MediaQuery.of(context).size.width - 44,
                      child: ListView.builder(
                          shrinkWrap: true,
                          // itemCount: _items.length,
                          itemCount: _item['list'].length,
                          itemBuilder: (_, index) {
                            return Card(
                              // key: PageStorageKey(_item['list'][i]['id']),
                              color: const Color.fromRGBO(14, 66, 210, 1),
                              elevation: 0,
                              child: ListTileTheme(
                                  dense: true,
                                  child: ExpansionTile(
                                      key: PageStorageKey(
                                          _item['list'][index]['id']),
                                      trailing: _item['list'][index]['bool']
                                          ? RotationTransition(
                                              turns: animation!,
                                              child: const Icon(
                                                Icons.chevron_right,
                                                color: Colors.white,
                                              ),
                                            )
                                          : const Icon(
                                              Icons.chevron_right,
                                              color: Colors.white,
                                            ),
                                      iconColor: Colors.white,
                                      onExpansionChanged: (bool) {
                                        _item['list'][index]['bool'] = bool;
                                        _changeOpacity(bool);
                                      },
                                      textColor: Colors.white,
                                      // controlAffinity: ListTileControlAffinity.leading,
                                      childrenPadding:
                                          const EdgeInsets.symmetric(
                                              vertical: 0, horizontal: 0),
                                      expandedCrossAxisAlignment:
                                          CrossAxisAlignment.end,
                                      maintainState: true,
                                      backgroundColor:
                                          const Color.fromRGBO(14, 66, 210, 1),
                                      title: Text(
                                        "${_item['list'][index]['name']}(${_item['list'][index]['count']})",
                                        style: const TextStyle(
                                            fontSize: 16, color: Colors.white),
                                      ),
                                      // contents
                                      children: [
                                        for (int i = 0;
                                            i <
                                                _item['list'][index]['list']
                                                    .length;
                                            i++)
                                          Container(
                                            color: Colors.white,
                                            padding:
                                                const EdgeInsetsDirectional.all(
                                                    6),
                                            child: Column(
                                              children: [
                                                Padding(
                                                  padding:
                                                      const EdgeInsetsDirectional
                                                          .only(
                                                    bottom: 8,
                                                  ),
                                                  child: Row(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Expanded(
                                                          flex: 7,
                                                          child: Row(
                                                            crossAxisAlignment:
                                                                CrossAxisAlignment
                                                                    .start,
                                                            children: [
                                                              const Icon(
                                                                MyIcons
                                                                    .position,
                                                                color: Color
                                                                    .fromRGBO(
                                                                        21,
                                                                        119,
                                                                        254,
                                                                        1),
                                                                size: 20,
                                                              ),
                                                              Expanded(
                                                                child: Text(
                                                                  _item['list'][
                                                                              index]
                                                                          [
                                                                          'list']
                                                                      [
                                                                      i]['name'],
                                                                  style: const TextStyle(
                                                                      fontSize:
                                                                          14),
                                                                ),
                                                              )
                                                            ],
                                                          )),
                                                      const Padding(
                                                          padding:
                                                              EdgeInsetsDirectional
                                                                  .all(16)),
                                                      Expanded(
                                                        flex: 3,
                                                        child: Container(
                                                          padding:
                                                              const EdgeInsetsDirectional
                                                                  .all(2),
                                                          alignment:
                                                              Alignment.center,
                                                          decoration:
                                                              BoxDecoration(
                                                            color:
                                                                Colors.red[50],
                                                            borderRadius:
                                                                const BorderRadiusDirectional
                                                                    .all(Radius
                                                                        .circular(
                                                                            2)),
                                                            border: Border.all(
                                                                color:
                                                                    Colors.red),
                                                          ),
                                                          child: Text(
                                                            _item['list'][index]
                                                                            [
                                                                            'list'][i]
                                                                        [
                                                                        'riskLevel'] ==
                                                                    1
                                                                ? '中风险区域'
                                                                : '高风险区域',
                                                            style:
                                                                const TextStyle(
                                                                    fontSize:
                                                                        12,
                                                                    color: Colors
                                                                        .red),
                                                          ),
                                                        ),
                                                      )
                                                    ],
                                                  ),
                                                ),
                                                Container(
                                                  padding:
                                                      const EdgeInsetsDirectional
                                                          .all(4),
                                                  width: MediaQuery.of(context)
                                                          .size
                                                          .width *
                                                      0.9,
                                                  color: const Color.fromRGBO(
                                                      250, 249, 249, 1),
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      const Padding(
                                                        padding:
                                                            EdgeInsetsDirectional
                                                                .only(
                                                                    top: 10,
                                                                    bottom: 8),
                                                        child: Text(
                                                          '管理方法',
                                                          style: TextStyle(
                                                              color:
                                                                  Colors.black,
                                                              fontSize: 16,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w300),
                                                        ),
                                                      ),
                                                      Text(
                                                        _item['list'][index]
                                                                ['list'][i]
                                                            ['method'],
                                                        style: const TextStyle(
                                                            color:
                                                                Color.fromRGBO(
                                                                    78,
                                                                    89,
                                                                    105,
                                                                    1),
                                                            fontSize: 13),
                                                      ),
                                                      const Padding(
                                                        padding:
                                                            EdgeInsetsDirectional
                                                                .only(
                                                                    top: 10,
                                                                    bottom: 8),
                                                        child: Text(
                                                          '流程',
                                                          style: TextStyle(
                                                              color:
                                                                  Colors.black,
                                                              fontSize: 16,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w300),
                                                          strutStyle: StrutStyle(
                                                              forceStrutHeight:
                                                                  true,
                                                              height: 1,
                                                              leading: 0.2),
                                                        ),
                                                      ),
                                                      Text(
                                                        _item['list'][index]
                                                                ['list'][i]
                                                            ['maintenance'],
                                                        style: const TextStyle(
                                                            color:
                                                                Color.fromRGBO(
                                                                    78,
                                                                    89,
                                                                    105,
                                                                    1),
                                                            fontSize: 13),
                                                        strutStyle:
                                                            const StrutStyle(
                                                                forceStrutHeight:
                                                                    true,
                                                                height: 1,
                                                                leading: 0.2),
                                                      ),
                                                    ],
                                                  ),
                                                )
                                              ],
                                            ),
                                          )
                                      ])),
                            );
                          }),
                    )),
              )
            : Container(
                width: MediaQuery.of(context).size.width * 0.92,
                color: Colors.white,
                padding: const EdgeInsetsDirectional.all(8),
                child: const Text(
                  "暂无中高风险区",
                  style: TextStyle(
                    color: Colors.green,
                    fontSize: 14,
                  ),
                  strutStyle: StrutStyle(
                      forceStrutHeight: true, height: 1, leading: 0.3),
                ),
              ),
      ],
    ));
  }

  ///非上海模块聚合
  Widget nonShanghai() {
    return Column(
      children: [
        Container(
            padding:
                const EdgeInsetsDirectional.only(start: 4, bottom: 14, top: 14),
            width: MediaQuery.of(context).size.width * 0.92,
            decoration: const BoxDecoration(
              borderRadius: BorderRadiusDirectional.all(Radius.circular(10)),
              gradient: LinearGradient(
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
                colors: [
                  Color.fromRGBO(22, 93, 255, 1),
                  Color.fromRGBO(68, 125, 253, 1),
                ],
              ),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                        child: Column(
                      children: [
                        Container(
                          alignment: Alignment.center,
                          child: const Text(
                            '出发地',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                            ),
                          ),
                        ),
                        Container(
                          padding: const EdgeInsetsDirectional.only(
                              top: 12, bottom: 4),
                          alignment: Alignment.center,
                          child: const Text(
                            '上海市',
                            style: TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.w300),
                          ),
                        ),
                      ],
                    )),
                    Expanded(
                      child: Container(
                        alignment: Alignment.center,
                        child: const Icon(
                          MyIcons.arrow_right,
                          color: Colors.white,
                          size: 12,
                        ),
                      ),
                    ),
                    Expanded(
                        child: Column(
                      children: [
                        Container(
                          alignment: Alignment.center,
                          child: const Text(
                            '目的地',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                            ),
                          ),
                        ),
                        Container(
                          height: 30,
                          padding: const EdgeInsetsDirectional.only(top: 10),
                          alignment: Alignment.center,
                          child: _buildLiveTypePicker1(),
                        ),
                      ],
                    )),
                  ],
                ),
                const Padding(padding: EdgeInsetsDirectional.all(10)),
                // RoundedLoadingButton(
                //     controller: _btnCtrl,
                //     color: const Color.fromRGBO(21, 119, 254, 1),
                //     borderRadius: 6,
                //     onPressed: (){
                //       _btnCtrl.stop();
                //
                //     },
                //     child: const Text("查询"))
              ],
            )),
        const Padding(padding: EdgeInsetsDirectional.all(4)),
        Container(
          width: MediaQuery.of(context).size.width * 0.92,
          color: Colors.white,
          padding: const EdgeInsetsDirectional.all(8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Padding(
                padding: EdgeInsetsDirectional.only(bottom: 4),
                child: Text(
                  '中高风险区域',
                  style: TextStyle(
                      color: Colors.black,
                      fontSize: 16,
                      fontWeight: FontWeight.w300),
                ),
              ),
              typeCtrl1.text != ''
                  ? Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Icon(
                          MyIcons.position,
                          color: Color.fromRGBO(21, 119, 254, 1),
                          size: 20,
                        ),
                        const Expanded(
                            child: Text(
                          '金牛区、新都区、郫都区、天府新区、锦江区、武侯区、龙泉驿区、成华区、简阳区、青羊区、高新区',
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: 14,
                          ),
                          strutStyle: StrutStyle(
                              forceStrutHeight: true, height: 1, leading: 0.3),
                        ))
                      ],
                    )
                  : const Text(
                      '暂无中高风险区\n\n',
                      style: TextStyle(
                        color: Colors.green,
                        fontSize: 14,
                      ),
                      strutStyle: StrutStyle(
                          forceStrutHeight: true, height: 1, leading: 0.3),
                    ),
            ],
          ),
        ),
        const Padding(padding: EdgeInsetsDirectional.all(4)),
        Container(
          width: MediaQuery.of(context).size.width * 0.92,
          color: Colors.white,
          padding: const EdgeInsetsDirectional.all(6),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Padding(
                padding: EdgeInsetsDirectional.only(top: 4, bottom: 8),
                child: Text(
                  '管理方法',
                  style: TextStyle(
                      color: Colors.black,
                      fontSize: 16,
                      fontWeight: FontWeight.w300),
                ),
              ),
              typeCtrl1.text != ''
                  ? Text(
                      '7天内有该区域旅居史，离开该区域须满14天，离开后完成6次核酸，分别在第1、2、3、5、7、13天完成，凭核酸阴性报告可复工',
                      style: const TextStyle(
                          color: Color.fromRGBO(78, 89, 105, 1), fontSize: 13),
                      strutStyle: StrutStyle(
                          forceStrutHeight: true, height: 1, leading: 0.2),
                    )
                  : const Text(
                      '该目的地没有中高风险区可以正常复工\n\n\n\n',
                      style: TextStyle(
                          color: Color.fromRGBO(78, 89, 105, 1), fontSize: 13),
                      strutStyle: StrutStyle(
                          forceStrutHeight: true, height: 1, leading: 0.2),
                    ),
              if (typeCtrl1.text != '')
                const Padding(
                  padding: EdgeInsetsDirectional.only(top: 10, bottom: 8),
                  child: Text(
                    '流程',
                    style: TextStyle(
                        color: Colors.black,
                        fontSize: 16,
                        fontWeight: FontWeight.w300),
                  ),
                ),
              if (typeCtrl1.text != '')
                Text(
                  '1.通过OA流程提前向部门申请报备\n2.部门负责人批准后，到人力资源部签署相关协议\n3.返沪当天在OA系统填写实际出行及返沪信息\n4.按照OA系统流程开始隔离或安排检测\n5.隔离期满或/及取得所有检测报告后，OA系统里报告给其主管或经理，根据系统的复工通知前来公司',
                  style: const TextStyle(
                      color: Color.fromRGBO(78, 89, 105, 1), fontSize: 13),
                  strutStyle: StrutStyle(
                      forceStrutHeight: true, height: 1, leading: 0.2),
                ),
            ],
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar:
          // TabberV2(
          //   title: '风险区域',
          //   showBack: true,
          //   themeIsDark: false,
          //
          // ),
          AppBar(
        backgroundColor: Colors.white,
        title: const Text(
          '风险区域',
          style: TextStyle(color: Colors.black),
        ),
        centerTitle: true,
        leading: IconButton(
          color: Colors.black,
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
        elevation: 0,
      ),
      body: Column(
        children: [
          // AppBarSearch(
          //   onTap:(){
          //     Navigator.of(context).push(FullScreenSearchModal());
          //     FocusScope.of(context).requestFocus(FocusNode());
          //     },
          //   leading: _buildLiveTypePicker(),
          // ),
          Row(
            children: [
              Container(
                alignment: Alignment.center,
                padding: EdgeInsetsDirectional.only(
                    start: MediaQuery.of(context).size.width * 0.04),
                height: 60,
                width: 84,
                child: _buildLiveTypePicker(),
              ),
            ],
          ),

          ///非上海
          if (typeCtrl.text == "非上海") nonShanghai(),

          ///上海
          if (typeCtrl.text == "上  海" || typeCtrl.text == '') shanghai(),
        ],
      ),
    );
  }
}


import 'package:amkor/utils/Adapt.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';



class PaymentCodePage extends StatefulWidget {
  const PaymentCodePage({Key? key }) : super(key: key);

  @override
  State<PaymentCodePage> createState() => _PaymentCodePageState();
}

class _PaymentCodePageState extends State<PaymentCodePage> {
  @override
  void initState() {
    // TODO: implement activate
    super.initState();
    if(!kIsWeb) {
      Adapt.setAndroid();
    }
  }

  @override
  Widget build(BuildContext context) {
    return  Scaffold(
      appBar:AppBar(
        backgroundColor: Colors.white,
        title:const Text('付款码',style: TextStyle(color: Colors.black),),
        centerTitle:true,
        leading:  IconButton(
          color: Colors.black,
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
        elevation: 0,
      ),
      body: const Center(
        child: Text("内容即将呈现..."),
      ),
    );
  }
}

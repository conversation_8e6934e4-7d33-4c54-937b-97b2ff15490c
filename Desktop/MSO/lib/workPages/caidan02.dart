
import 'package:flutter/material.dart';
import '../services/employeeService.dart';

import 'package:amkor/utils/adapt.dart';


import 'package:amkor/Notice_Cookbook.dart';


class CaidanPage extends StatefulWidget {
  const CaidanPage({Key? key}) : super(key: key);
  @override
  _UploadAntigenPageState createState() => _UploadAntigenPageState();
}

class _UploadAntigenPageState extends State<CaidanPage> {
  // 2023-04-10 by <PERSON> add-----

  var week_map = {};
  var monday_list = [1, 2, 3, 4, 5, 6, 7];
  var week_list = [];
  var week_1 = [];
  var now =  DateTime.now();
  var date =  DateTime.now();
  dynamic r;
  var cur_cd;
  var cur_cd_tmp;
  int cur_weekday = 0;
  List listData = [];

  // 2023-04-10 by Leo end-----
  @override
  void initState() {
    // get week with date by <PERSON>
    super.initState();
    get_init_title_image();

    get_week();
  }

  /// 获取初始星期+日期 显示的数据
  void get_week() {
    for (int i = 0; i < monday_list.length; i++) {
      var monday = monday_list[i];
      if (date.weekday > monday_list[i]) {
        week_list.add(date
            .add(Duration(days: -monday_list[i]))
            .toString()
            .substring(0, 10));
        week_1.add(date
            .add(Duration(days: -monday_list[i]))
            .toString()
            .substring(0, 10));
        // week_list.add(now.toString().substring(0, 10));
      } else {
        // week_list.add(now.toString().substring(0, 10));
        week_list.add(date
            .add(Duration(days: monday_list[i] - date.weekday))
            .toString()
            .substring(0, 10));
        week_1.add(date
            .add(Duration(days: monday_list[i] - date.weekday))
            .toString()
            .substring(0, 10));
      }
    }
    week_list.sort();
    week_1.sort();

    for (int i = 0; i < week_list.length; i++) {
      if (i == 0) {
        week_list[i] = '周一(' + week_list[i].toString().substring(5, 10) + ')';
      } else if (i == 1) {
        week_list[i] = '周二(' + week_list[i].toString().substring(5, 10) + ')';
      } else if (i == 2) {
        week_list[i] = '周三(' + week_list[i].toString().substring(5, 10) + ')';
      } else if (i == 3) {
        week_list[i] = '周四(' + week_list[i].toString().substring(5, 10) + ')';
      } else if (i == 4) {
        week_list[i] = '周五(' + week_list[i].toString().substring(5, 10) + ')';
      } else if (i == 5) {
        week_list[i] = '周六(' + week_list[i].toString().substring(5, 10) + ')';
      } else if (i == 6) {
        week_list[i] = '周日(' + week_list[i].toString().substring(5, 10) + ')';
      }
    }
    week_map['week_0'] = week_list;
    week_map['week_1'] = week_1;
    List<String> cd_0 = ['早餐', '午餐', '下午茶', '晚餐', '夜宵'];
    week_map['cd_0'] = cd_0;
    List<int> cd_1 = [0, 1, 2, 3, 4];
    week_map['cd_1'] = cd_1;

    // 初始化时，加载当天的数据；
    var data_i = new DateTime.now();
    xq[data_i.weekday - 1] = false;
    cur_weekday = data_i.weekday - 1;

    // 计算当前时间所属餐段
    var cur_time = DateTime.now().toString().split(' ')[1].split('.')[0];
    if (Comparable.compare('00:00:00', cur_time) == -1 &&
        Comparable.compare('09:00:00', cur_time) == 1) {
      cur_cd = '早餐';
      cur_cd_tmp = 0;
    } else if (Comparable.compare('09:00:00', cur_time) == -1 &&
        Comparable.compare('14:00:00', cur_time) == 1) {
      cur_cd = '午餐';
      cur_cd_tmp = 1;
    } else if (Comparable.compare('14:00:00', cur_time) == -1 &&
        Comparable.compare('16:00:00', cur_time) == 1) {
      cur_cd = '下午茶';
      cur_cd_tmp = 2;
    } else if (Comparable.compare('16:00:00', cur_time) == -1 &&
        Comparable.compare('20:30:00', cur_time) == 1) {
      cur_cd = '晚餐';
      cur_cd_tmp = 3;
    } else if (Comparable.compare('20:30:00', cur_time) == -1 &&
        Comparable.compare('23:59:59', cur_time) == 1) {
      cur_cd = '夜宵';
      cur_cd_tmp = 4;
    }
    ys[cur_cd_tmp] = !ys[cur_cd_tmp];
    cd_data(week_map['week_1'][cur_weekday],cur_cd_tmp);
    // setState(() {});
  }

  List<Widget> _getListDate() {
    var tempList = listData.map((value) {
      return Container(
        padding: const EdgeInsets.fromLTRB(12, 8, 5, 8),
        decoration: const BoxDecoration(
          // 菜品分隔线
          border: Border(
            bottom: BorderSide(
              color: Color.fromRGBO(223, 229, 230, 1),
              width: 1,
            ),
          ),
        ),

        child: Row(
          children: [
            Expanded(
              flex: 3,
              child: Text(
                value['title'],
                style: const TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            const Padding(padding: EdgeInsetsDirectional.all(2)),
            Expanded(
              flex: 1,
              child: Text(
                value['price'],
                style: const TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            const Padding(padding: EdgeInsetsDirectional.all(2)),
            Expanded(
              flex: 2,
              child: Text(
                value['classification'],
                style: const TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            // const Divider(
            //   height: 3,
            //   color: Color.fromRGBO(223, 229, 230, 1),
            // ),
          ],
        ),
      );
    });
    return tempList.toList();
  }

  final List categories1 = [
    Category(
      name: "星期一",
      items: ["早餐", "午餐", "晚餐", "下午茶", "夜宵"],
    ),
    Category(
      name: "星期二",
      items: [
        "早餐",
        "午餐",
        "晚餐",
        // "下午茶",
        "夜宵"
      ],
    ),
    Category(
      name: "星期三",
      items: [
        "早餐",
        "午餐",
        "晚餐",
        // "下午茶",
        "夜宵"
      ],
    ),
    Category(
      name: "星期四",
      items: [
        "早餐",
        "午餐",
        "晚餐",
        // "下午茶",
        "夜宵"
      ],
    ),
    Category(
      name: "星期五",
      items: [
        "早餐",
        "午餐",
        "晚餐",
        // "下午茶",
        "夜宵"
      ],
    ),
    Category(
      name: "星期六",
      items: [
        "早餐",
        "午餐",
        "晚餐",
        // "下午茶",
        "夜宵"
      ],
    ),
    Category(
      name: "星期日",
      items: [
        "早餐",
        "午餐",
        "晚餐",
        // "下午茶",
        "夜宵"
      ],
    ),
  ];

  int _currentIndex = 0;

  var _item;

  List<bool> ys = [true, true, true, true, true];
  List<bool> xq = [true, true, true, true, true, true, true];

  // 2023-04-11 By Leo API
  Future<void> cd_data(String dishDate, int dishType) async {
    listData = [];
    r = await Dish().GetDishIntro(dishDate, dishType);

    setState(() {});

    var cd_len = 0;
    cd_len = r.data['dishContents'].length;
    print("-=-=-=-=-=-=-=-=-=-$r");

    for (int i = 0; i < cd_len; i++)
      listData.add({
        "title": r.data['dishContents'][i]['dishName'],
        "price": r.data['dishContents'][i]['dishPrice'],
        "classification": r.data['dishContents'][i]['category']
      });
  }

  Future<void> get_init_title_image() async {
    _imageFile = await Dish().ReadDishHeaderImage();
    setState(() {});
  }





  var _imageFile;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: true,
        centerTitle: true,
        title: Text(
          '公司菜单',
          style: TextStyle(
            color: const Color.fromRGBO(57, 57, 57, 1),
            fontSize: Adapt.widthPt(24),
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: const Color.fromRGBO(215, 189, 144, 1),
        leading: IconButton(
          color: Colors.black,
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),

      ),
      body: Column(
        children: [
          _imageFile==null? _loadMoreWidget():
          Container(
            clipBehavior: Clip.hardEdge,
            height: 100,
            width: MediaQuery.of(context).size.width,
            decoration: const BoxDecoration(
              color:  Colors.white,
            ),
            child:Image.memory(_imageFile.data, fit: BoxFit.cover) ,),
          Container(
            margin: const EdgeInsets.fromLTRB(4, 3, 4, 3),
            decoration: const BoxDecoration(
              color: Color.fromRGBO(255, 255, 255, 1), //餐厅公告背景色
              borderRadius: BorderRadius.all(Radius.circular(2)),  //餐厅公司背景圆角
            ),
            child: Row(
              children: [
                const Image(
                  image: AssetImage("assets/icons/ct_message.png"),
                  height: 30,
                  width: 30,
                ),
                GestureDetector(
                  // InkWell(
                  onTap: () {
                    //事件处理
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => NoticeCookbookPage(),
                      ),
                    );
                  },
                  child: const Text(
                    "餐厅公告",
                    style: TextStyle(
                        color: Color.fromRGBO(57, 57, 57, 1),
                        // color: Colors.green,
                        fontSize: 15,
                        fontWeight: FontWeight.w500),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: Container(
              alignment: Alignment.topLeft,
              margin: const EdgeInsets.all(4),
              constraints: const BoxConstraints(
                // maxHeight: 540,
              ),
              child: Row(
                children: [
                  SizedBox(
                    //星期
                    width: 106,
                    child: ListView.builder(
                      itemCount: week_map['week_0'].length,
                      // itemCount: categories.length,
                      itemBuilder: (BuildContext context, int index) {
                        // return ListTile(
                        return GestureDetector(
                          onTap: () {
                            //事件处理
                            _currentIndex = index;
                            for (int i = 0; i < 7; i++) xq[i] = true;
                            xq[index] = !xq[index];
                            for (int i = 0; i < 5; i++) ys[i] = true;
                            // ys[1] = false;

                            cur_weekday = index;
                            // print("--=====-->${week_map['cd_0'][index]}");
                            // var cur_cd_tmp = 0;

                            // cur_cd = '午餐';
                            switch (cur_cd) {
                              case '早餐':
                                cur_cd_tmp = 0;
                                break;
                              case '午餐':
                                cur_cd_tmp = 1;
                                break;
                              case '下午茶':
                                cur_cd_tmp = 2;
                                break;
                              case '晚餐':
                                cur_cd_tmp = 3;
                                break;
                              case '夜宵':
                                cur_cd_tmp = 4;
                                break;
                            }
                            ys[cur_cd_tmp] = false;

                            cd_data(week_map['week_1'][index],
                                cur_cd_tmp); // 调用API By Leo

                            setState(() {});
                          },
                          child: Container(
                            alignment: Alignment.center,
                            height: 50,
                            decoration: BoxDecoration(
                              color: xq[index]
                                  ? const Color.fromRGBO(248, 248, 248, 1)
                                  : const Color.fromRGBO(255, 255, 255, 1),
                              // borderRadius: BorderRadius.circular(7),
                              // 星期的圆角
                              // borderRadius: BorderRadius.only(
                              //   topLeft: Radius.circular(10),
                              //   bottomLeft: Radius.circular(10),
                              // ),
                              // border: Border.all(style: BorderStyle.solid,color: Colors.grey),
                            ),
                            child: Text(
                              week_map['week_0'][index],
                              // categories[index].name,
                              style: xq[index]
                                  ? const TextStyle(
                                  color: Colors.black, fontSize: 15)
                                  : const TextStyle(
                                  color: Colors.black,
                                  fontWeight: FontWeight.w900,
                                  fontSize: 15),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                  Expanded(
                    child: Column(
                      children: [
                        Container(
                          //餐段
                          height: 50,
                          color: const Color.fromRGBO(248, 248, 248, 1),
                          child: ListView.builder(
                            // scrollDirection: Axis.vertical,
                            scrollDirection: Axis.horizontal,
                            itemCount: week_map['cd_0'].length,
                            // itemCount: categories[_currentIndex].items.length,
                            itemBuilder: (BuildContext context, int index) {
                              final item = week_map['cd_0'][index];
                              // final item = categories[_currentIndex].items[index];
                              return GestureDetector(
                                onTap: () {
                                  ///事件处理
                                  _item = item;
                                  //改变颜色的变量值
                                  for (int i = 0; i < 5; i++) ys[i] = true;
                                  ys[index] = !ys[index];
                                  cur_cd = week_map['cd_0'][index];

                                  switch (cur_cd){
                                    case '早餐':
                                      cur_cd_tmp = 0;
                                      break;
                                    case '午餐':
                                      cur_cd_tmp = 1;
                                      break;
                                    case '下午茶':
                                      cur_cd_tmp = 2;
                                      break;
                                    case '晚餐':
                                      cur_cd_tmp = 3;
                                      break;
                                    case '夜宵':
                                      cur_cd_tmp = 4;
                                      break;
                                  }

                                  // if (cur_cd == '早餐') cur_cd_tmp = 0;
                                  // if (cur_cd == '午餐') cur_cd_tmp = 1;
                                  // if (cur_cd == '下午茶') cur_cd_tmp = 2;
                                  // if (cur_cd == '晚餐') cur_cd_tmp = 3;
                                  // if (cur_cd == '夜宵') cur_cd_tmp = 4;

                                  cd_data(week_map['week_1'][cur_weekday],
                                      cur_cd_tmp);
                                  setState(() {});
                                },
                                child: Container(
                                  alignment: Alignment.center,
                                  width: 74,
                                  margin: const EdgeInsets.fromLTRB(
                                      6, 10, 6, 10),
                                  // margin:const EdgeInsetsDirectional.all(6),
                                  decoration: BoxDecoration(
                                    color: ys[index]
                                        ? Color.fromRGBO(248, 248, 248, 1)
                                        : Color.fromRGBO(215, 189, 144, 1),
                                    // color: ys[index]?Colors.white:Colors.amber,
                                    borderRadius: BorderRadius.circular(4),
                                    border: Border.all(
                                        style: BorderStyle.solid,
                                        color: Colors.grey),
                                  ),
                                  child: Text(item),
                                ),
                              );
                            },
                          ),
                        ),
                        Container(
                          // color: Color.fromRGBO(248, 248, 248, 1),
                          padding: const EdgeInsets.fromLTRB(0, 0, 0, 3),
                        ),
                        if(_getListDate().isNotEmpty)
                        Padding(
                          padding: const EdgeInsetsDirectional.only(start: 12,bottom: 8),
                          child: Row(
                            children:const  [
                              Expanded(
                                flex: 3,
                                child:Text("菜名",style: TextStyle(fontSize: 14,color: Colors.black,fontWeight: FontWeight.w600),),
                              ),
                              Padding(padding: EdgeInsetsDirectional.all(2)),
                              Expanded(
                                flex: 1,
                                child:Text("价格",style: TextStyle(fontSize: 14,color: Colors.black,fontWeight: FontWeight.w600),),
                              ),
                              Padding(padding: EdgeInsetsDirectional.all(2)),
                              Expanded(
                                flex: 2,
                                child:Text("类型",style: TextStyle(fontSize: 14,color: Colors.black,fontWeight: FontWeight.w600),),
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: Container(
                            decoration: const BoxDecoration(
                              color: Color.fromRGBO(255, 255, 255, 1),
                              // borderRadius: BorderRadius.all(Radius.circular(10)),
                              // 菜品圆角
                              // borderRadius: BorderRadius.only(
                              //   bottomLeft: Radius.circular(10),
                              //   bottomRight: Radius.circular(10),
                              // ),

                            ),
                            child: SingleChildScrollView(
                              child: Column(
                                  children: _getListDate().map((e) => e).toList()),
                            ),

                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _loadMoreWidget() {
    Future.delayed(const Duration(milliseconds:2000),(){
      // if(i<(total-total%10)/10+1) {
      //   i++;
      //   kg=false;
      //   data(i);
      // }
    });
    //还有更多数据可以加载
    return Center(
      child: Padding(
        padding: const EdgeInsetsDirectional.only(top: 10,bottom: 60),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: const <Widget>[
            Text("内容即将呈现......"),
            SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
              ),
            )
          ],
        ),
      ),
    );
  }
}



class Category {
  final String name;

  final List items;

  Category({required this.name, required this.items});
}

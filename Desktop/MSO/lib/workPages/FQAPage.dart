
import 'package:amkor/Notice_xq.dart';
import 'package:amkor/components/tabbar_v2.dart';
import 'package:amkor/services/employeeService.dart';
import 'package:amkor/utils/Adapt.dart';
import 'package:amkor/utils/enums.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';


class FQAPage extends StatefulWidget {
  const FQAPage({Key? key }) : super(key: key);

  @override
  State<FQAPage> createState() => _FQAPageState();
}

class _FQAPageState extends State<FQAPage> with SingleTickerProviderStateMixin{

  final TextEditingController typeCtrl = TextEditingController();
  late final TabController _tabController;

  var dataList;
  var dataListcx;
  ///用来替换中间值
  var dataLists;
  var dataListcxs;

  var epidemic= QuestionApp();

  int total1=0;
  int total1cx=0;
  bool cx=false;
  int total=0;

  final ScrollController _scrollController = ScrollController();
  @override
  void initState() {
    // TODO: implement activate

    super.initState();
    _tabController =  TabController(
      vsync: this,
      length: 1,
    );
    dataa();
    if(!kIsWeb) {
      Adapt.setAndroid();
    }
  }

  Future<void> dataa() async {
    var r=await epidemic.GetAppQuestionList();

    //是否已经被释放
    if (mounted) {
      setState(() {
        dataList=r?.data;
        total1=dataList.length;
      });
    }
  }



  final RefreshController _refreshController = RefreshController(initialRefresh: false);
  final RefreshController _refreshController1 = RefreshController(initialRefresh: false);
  void _onRefresh() async{
    // monitor network fetch
    await Future.delayed(const Duration(milliseconds: 300));
    // if failed,use refreshFailed()
    if(!cx){
      dataList=null;
      total=0;
      total1=0;
      dataa();
    }


    _refreshController.refreshCompleted();
    ///待接入数据删除
    _refreshController1.refreshCompleted();
  }

  @override
  void dispose() {
    _refreshController.dispose();
    _refreshController1.dispose();
    _scrollController.dispose();
    typeCtrl.dispose();
    super.dispose();
  }



  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
          appBar: AppBarSearch(
            onTap:(){
            },
            onCancel: (){
              dataList=null;
              total=0;
              total1=0;
              dataa();
              cx=false;
            },
            onClear:(){
              dataList=null;
              total=0;
              total1=0;
              dataa();
              cx=false;
            },
            onChanged:(text){
              print('----=-=--=1331--=$text-');
              if(text!=''){
                total1cx=0;
                dataListcx=null;
                ///用来替换中间值
               if(dataLists!=null) dataList=dataLists;
               if(dataListcxs!=null) total1=dataListcxs;

                for(var i=0;i<total1;i++){
                  if(dataList[i]['title'].lastIndexOf(text)!=-1) {
                    dataListcx != null
                        ? dataListcx.add(dataList[i])
                        : dataListcx = [dataList[i]];
                    total1cx += 1;
                  }

                }
                setState(() {
                  ///用来替换中间值
                  dataLists=dataList;
                  dataListcxs=total1;

                  dataList=dataListcx;
                  total1=total1cx;
                  cx=true;
                });
              }else{
                dataList=null;
                total=0;
                total1=0;
                dataa();
                cx=false;
              }
          },
          ),
          body:Column(
            children: [
               Container(
                color: Colors.white,
                alignment: Alignment.topLeft,
                height: 50,
                child:TabBar(
                    isScrollable: true,
                    labelColor: Colors.blue,   // 选中的Widget颜色
                    indicatorColor:Colors.blue, // 选中的指示器颜色
                    labelStyle:  const TextStyle(fontSize: 18.0),// 必须设置，设置 color 没用的，因为 labelColor 已经设置了
                    unselectedLabelColor: Colors.black,
                    unselectedLabelStyle:  const TextStyle(
                        fontSize: 15.0), // 设置 color 没用的，因为unselectedLabelColor已经设置了
                    controller: _tabController,
                    indicatorSize: TabBarIndicatorSize.label,
                  tabs: const <Widget>[
                    Tab(text:"热门问题"),
                    // Tab(text:"我的问题")
                  ],
                ),
              ),
              const Padding(padding:EdgeInsetsDirectional.only(bottom: 10),),
              Expanded(
                  child:TabBarView(
                    controller: _tabController,
                    children: <Widget>[
                      SmartRefresher(
                          enablePullDown: true,
                          enablePullUp: false,
                          header: const WaterDropHeader(
                            waterDropColor:Colors.transparent,
                            complete:Text('加载成功'),
                            failed:Text('刷新失败'),
                            ///默认动画
                            // refresh:
                            idleIcon: Icon(
                              Icons.autorenew,
                              size: 20,
                              color: Colors.black,
                            ),
                          ),
                          footer: CustomFooter(
                            builder: ( context, mode){
                              Widget body ;
                              if(mode==LoadStatus.idle){
                                body =  const Text("pull up load");
                              }
                              else if(mode==LoadStatus.loading){
                                body =  const CupertinoActivityIndicator();
                              }
                              else if(mode == LoadStatus.failed){
                                body = const Text("Load Failed!Click retry!");
                              }
                              else if(mode == LoadStatus.canLoading){
                                body = const Text("刷新成功");
                              }
                              else{
                                body = const Text("No more Data");
                              }
                              return SizedBox(
                                height: 55.0,
                                child: Center(child:body),
                              );
                            },
                          ),
                          controller: _refreshController,
                          onRefresh: _onRefresh,
                          onLoading: null,
                          child: ListView.builder(
                              itemCount: total1,
                              controller: _scrollController,
                              itemBuilder: (context, index) {
                                return Column(
                                  children: [
                                    const Padding(padding: EdgeInsetsDirectional.only(top: 10,bottom: 10),
                                    ),
                                    GestureDetector(
                                      child:Container(
                                        clipBehavior: Clip.hardEdge,
                                        decoration: const BoxDecoration(
                                          borderRadius: BorderRadius.all(Radius.circular(4)),
                                          color:  Colors.white,
                                        ),
                                        width: MediaQuery.of(context).size.width*0.9,
                                        child:Column(
                                          children: [
                                            Stack(
                                              children: [
                                                Image.network(dataList[index]["cover"].replaceAll("\\","/"),
                                                  width: MediaQuery.of(context).size.width *0.9,
                                                  height: MediaQuery.of(context).size.height *0.2,
                                                  fit: BoxFit.cover,
                                                  errorBuilder: (a,b,c){
                                                    return Image(
                                                      image: const AssetImage('assets/images/error_pic_long.png'),
                                                      width: MediaQuery.of(context).size.width *0.92,
                                                      height: MediaQuery.of(context).size.height *0.2,
                                                      fit: BoxFit.cover,);
                                                  },
                                                ),
                                                Positioned(
                                                    bottom: 0,
                                                    left: 0,
                                                    child: Container(
                                                      padding: const EdgeInsetsDirectional.all(10),
                                                      width:  MediaQuery.of(context).size.width*0.9,
                                                      decoration: const BoxDecoration(
                                                        color: Color.fromRGBO(0, 0, 0, 0.3),
                                                      ),
                                                      child:Text("${dataList[index]["title"]}",
                                                        style: const TextStyle(fontSize: 16,color: Colors.white),),
                                                    )),

                                              ],
                                            ),

                                          ],
                                        ),
                                      ),
                                      onTap: () {
                                        Navigator.of(context).push(MaterialPageRoute(
                                          builder: (context) => NoticeXqPage(id: "${dataList[index]["id"]}",direction: Direction.problem),
                                        ));
                                      },
                                    ),

                                    if (index==total1-1)
                                      Row(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children:  [
                                          Container(
                                            color: const Color(0xF8F8F1FF),
                                            width: 50,
                                            height: 1,
                                          ),
                                          const Padding(padding: EdgeInsetsDirectional.all(10),
                                            child:  Text("已无更多公告消息",style: TextStyle(fontSize: 12,color: Colors.grey),) ,),
                                          Container(
                                            color: const Color(0xF8F8F1FF),
                                            width: 50,
                                            height: 1,
                                          )
                                        ],
                                      )

                                  ],
                                );

                              })
                      ),
                      // Text('000000')
                    ],)
              )
            ],
          ),
      )
    )

      ;
  }
}

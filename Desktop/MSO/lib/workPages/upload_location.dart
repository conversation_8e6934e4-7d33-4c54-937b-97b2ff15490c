import 'dart:io';
import 'package:amkor/components/tabbar.dart';
import 'package:amkor/services/dto/getPositionDto.dart';
import 'package:amkor/services/employeeService.dart';
import 'package:amkor/utils/adapt.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
// import 'package:rounded_loading_button/rounded_loading_button.dart';  // 暂时禁用

import '../components/routes.dart';
import '../utils/colors.dart';

class UploadLocationPage extends StatefulWidget {
  const UploadLocationPage({Key? key}) : super(key: key);

  @override
  _UploadLocationPageState createState() => _UploadLocationPageState();
}

class _UploadLocationPageState extends State<UploadLocationPage> {
  GetPositionOutputDto? _position;
  MobileScannerController? _controller;
  // final _btnCtrl = RoundedLoadingButtonController();  // 暂时禁用

  final _employeeService = EmployeeService();

  @override
  void initState() {
    super.initState();
    if (!kIsWeb) Adapt.setAndroid(isDark: false);
    _controller = MobileScannerController();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: kIsWeb
          ? _buildweb()
          : Stack(
              children: <Widget>[
                Positioned(
                  child: SizedBox(
                    height: MediaQuery.of(context).size.height +
                        MediaQuery.of(context).padding.bottom,
                    width: MediaQuery.of(context).size.width,
                    child: _buildQrView(context),
                  ),
                ),
                Positioned(
                  child: Tabbar(showBack: true),
                ),
                Positioned(
                  bottom: 48,
                  left: 15,
                  child: GestureDetector(
                    child: const ImageIcon(
                      AssetImage("assets/icons/light.png"),
                      color: Colors.white,
                    ),
                    onTap: () {
                      _controller?.toggleTorch();
                    },
                  ),
                )
              ],
            ),
    );
  }

  ///移动端扫码支持二维码和条形码
  Widget _buildQrView(BuildContext context) {
    return MobileScanner(
      controller: _controller,
      onDetect: (BarcodeCapture capture) {
        final List<Barcode> barcodes = capture.barcodes;
        if (barcodes.isNotEmpty) {
          final String code = barcodes.first.displayValue ?? '';
          if (code.isNotEmpty) {
            _onScanResult(code);
          }
        }
      },
    );
  }

  ///web扫码
  Widget _buildweb() {
    MobileScannerController cameraController = MobileScannerController();
    // cameraController.switchCamera();

    return Stack(
      children: <Widget>[
        MobileScanner(
            controller: kIsWeb ? cameraController : null,
            onDetect: (BarcodeCapture capture) {
              final List<Barcode> barcodes = capture.barcodes;
              if (barcodes.isEmpty) {
                debugPrint('Failed to scan Barcode');
              } else {
                final String code = barcodes.first.displayValue ??
                    barcodes.first.rawValue ??
                    '';
                if (code.isNotEmpty) {
                  _onScanResult(code);
                  debugPrint('Barcode found! $code');
                }
              }
            }),
        Positioned(
          child: Tabbar(showBack: true),
        ),
        Positioned(
          bottom: 48,
          right: 15,
          child: IconButton(
            color: Colors.white,
            icon: Icon(cameraController.facing == CameraFacing.front
                ? Icons.camera_front
                : Icons.camera_rear),
            iconSize: 32.0,
            onPressed: () => cameraController.switchCamera(),
          ),
        )
      ],
    );
  }

  Widget _buildQrResDialog(BuildContext buildContext) {
    return AlertDialog(
      shape: Theme.of(context).dialogTheme.shape,
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Image(
                image: const AssetImage("assets/icons/location.png"),
                height: Adapt.widthPt(20),
                width: Adapt.widthPt(20),
              ),
              Text(
                "当前所在位置",
                style: TextStyle(
                    fontSize: Adapt.widthPt(18), fontWeight: FontWeight.w500),
              )
            ],
          ),
          const Padding(padding: EdgeInsets.only(top: 20)),
          Container(
            padding: const EdgeInsets.all(20),
            alignment: Alignment.center,
            decoration: const BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(5)),
              color: Color(0xffebf1fd),
            ),
            child: Text(_position?.locationCode ?? ""),
          ),
          const Padding(padding: EdgeInsets.only(top: 20)),
          SizedBox(
            width: MediaQuery.of(context).size.width,
            child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: CustomColors.ThemeColor,
                  minimumSize: Size(MediaQuery.of(context).size.width, 48),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
                onPressed: () {
                  _uploadLocation().then((value) {
                    if (value == true) {
                      Navigator.of(context)
                          .popUntil(ModalRoute.withName(RouteNames.indexPage));
                    }
                  });
                },
                child: const Text("上报", style: TextStyle(color: Colors.white))),
          )
        ],
      ),
    );
  }

  ///调用上报位置api
  Future<bool> _uploadLocation() async {
    var reportRes =
        await _employeeService.PositionReport(_position!.locationCode!);
    if (reportRes == null) {
      Fluttertoast.showToast(msg: "上报失败，请稍后再试");
      return false;
    }

    if (!reportRes.success) {
      Fluttertoast.showToast(msg: reportRes.message);
      return false;
    }
    await Fluttertoast.showToast(msg: reportRes.message);
    return true;
  }

  /// 扫码结果事件
  Future _onScanResult(String scanData) async {
    _controller?.stop();
    final u = Uri.tryParse(scanData);
    if (u == null) {
      Fluttertoast.showToast(msg: "读取码失败，请重试");
      _controller?.start();
      return;
    }
    var positionCode = u.queryParameters["code"];
    print('----------------------------$u');
    print('----------------------------$positionCode');
    if (positionCode == null || positionCode.isEmpty) {
      Fluttertoast.showToast(msg: "读取码失败，请重试");
      _controller?.start();
      return;
    }

    EasyLoading.show();
    var positionRes = await _employeeService.GetPosition(positionCode);
    if (positionRes == null) {
      EasyLoading.dismiss();
      Fluttertoast.showToast(msg: "网络异常，请稍后再试");
      _controller?.start();
      return;
    }
    if (positionRes.data == null) {
      EasyLoading.dismiss();
      Fluttertoast.showToast(msg: positionRes.message);
      _controller?.start();
      return;
    }
    var position = GetPositionOutputDto.fromJson(positionRes.data!);
    setState(() {
      _position = position;
    });

    EasyLoading.dismiss();
    AwesomeDialog(
      context: context,
      dialogType: DialogType.info,
      animType: AnimType.bottomSlide,
      title: '位置信息',
      desc: '位置: ${_position?.locationName ?? "未知"}',
      btnOkOnPress: () {},
    ).show();
    _controller?.start();
  }

  void _back(BuildContext c) {
    Navigator.of(c).pop();
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }
}

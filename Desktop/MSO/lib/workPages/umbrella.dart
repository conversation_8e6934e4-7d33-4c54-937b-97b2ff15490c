import 'package:amkor/components/tabbar.dart';
import 'package:amkor/utils/adapt.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:pinput/pinput.dart';
import 'umbrellaCode.dart';

class UmbrellaPage extends StatefulWidget {
  const UmbrellaPage({Key? key }) : super(key: key);
  @override
  State<UmbrellaPage> createState() => _UmbrellaPageState();
}

class _UmbrellaPageState extends State<UmbrellaPage> {
  var _Code;
  @override
  void initState() {
    // TODO: implement activate
    Adapt.setAndroid();
    super.initState();
  }

  Widget buildPinPut() {
    final defaultPinTheme = PinTheme(
      width: 36,
      height: 36,
      textStyle:const TextStyle(fontSize: 20, color:  Color.fromRGBO(30, 60, 87, 1), fontWeight: FontWeight.w600),
      decoration: BoxDecoration(
        border: Border.all(color:const Color.fromRGBO(234, 239, 243, 1)),
        borderRadius: BorderRadius.circular(20),
      ),
    );

    final focusedPinTheme = defaultPinTheme.copyDecorationWith(
      border: Border.all(color:const Color.fromRGBO(114, 178, 238, 1)),
      borderRadius: BorderRadius.circular(8),
    );

    final submittedPinTheme = defaultPinTheme.copyWith(
      decoration: defaultPinTheme.decoration?.copyWith(
        color:const Color.fromRGBO(234, 239, 243, 1),
      ),
    );

    return Pinput(
        length:6,
        defaultPinTheme: defaultPinTheme,
        focusedPinTheme: focusedPinTheme,
        submittedPinTheme: submittedPinTheme,
        inputFormatters: [FilteringTextInputFormatter.allow(RegExp("[0-9.]")),],
        // validator: (s) {
        //   return s == '2222' ? null : 'Pin is incorrect';
        // },
        pinputAutovalidateMode: PinputAutovalidateMode.onSubmit,
        showCursor: true,
        onChanged:(value){
          _Code=value;

        },
        onCompleted: (pin) =>(){
          _Code=pin;
          print('-------------$pin');
        }

    );
  }


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset:false,
      body:Stack(
        children:[
           Image(
            image:const AssetImage('assets/images/axs_bg.png'),
             height:MediaQuery.of(context).size.height ,
          ),
          Column(
            children: [
              Tabbar(
                title: "爱心伞",
                showBack: true,
                fontColor: Colors.black,
                backgroundColor:Colors.white
              ),
              Container(
                height:MediaQuery.of(context).size.height-Adapt.widthPt(46)-MediaQuery.of(context).padding.top ,
                padding: const EdgeInsets.only(bottom: 200),
                child:Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children:  [
                    const Image(
                      image: AssetImage('assets/images/axs_icon.png'),
                      width: 160,
                      height: 115,
                    ),
                   const Padding(
                      padding: EdgeInsets.all(10),
                      child: Text("还未有取伞记录～"),
                    ),
                    buildPinPut(),
                  ],
                )
              )
            ],
          ),
          Positioned(
            bottom: 10,
            left: 60,
            child:GestureDetector(
              onTap: (){
                Navigator.of(context).push(MaterialPageRoute(
                  builder: (context) => const UmbrellaCodePage(),
                ));},
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: const [
                  Image(
                    image: AssetImage('assets/icons/axs_au1.png'),
                    width: 40,
                    height: 40,
                  ),
                  Padding(
                    padding: EdgeInsets.all(8),
                    child: Text("取伞"),
                  ),
                ],
              ),
            ) ,
          ),
          Positioned(
            bottom: 10,
            right: 60,
            child:GestureDetector(
              onTap: (){
                Navigator.of(context).push(MaterialPageRoute(
                builder: (context) => const UmbrellaCodePage(),
              ));},
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: const [
                  Image(
                    image: AssetImage('assets/icons/axs_au.png'),
                    width: 40,
                    height: 40,
                  ),
                  Padding(
                    padding: EdgeInsets.all(8),
                    child: Text("还伞"),
                  ),
                ],
              ),
            ) ,
          ),
        ],

      ),
    );
  }
}

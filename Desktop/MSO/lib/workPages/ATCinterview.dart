// import 'package:date_time_picker/date_time_picker.dart'; // 已移除，使用Flutter内置日期选择器
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../utils/adapt.dart';
import 'package:amkor/components/tabbar_v2.dart';
import 'package:amkor/utils/colors.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

class InterviewPage extends StatefulWidget {
  const InterviewPage({Key? key}) : super(key: key);

  @override
  State<InterviewPage> createState() => _InterviewPageState();
}

class _InterviewPageState extends State<InterviewPage> {
  //工号
  TextEditingController _JobController = TextEditingController();
  //手机号
  TextEditingController _Telephonetroller = TextEditingController();
  bool _isLoading = false;
  //姓名
  TextEditingController _nameController = TextEditingController();
  //部门
  TextEditingController _departmentController = TextEditingController();
  //反馈
  TextEditingController feedbackController = TextEditingController();
  //工号
  String _Job = "";
  //手机号
  String _Telephone = "";
  //姓名
  String _name = "";
  //部门
  String _department = "";
  //反馈
  String feedback = "";

  ///
  //FocusNode
  FocusNode jkNode = new FocusNode();

  GlobalKey _formKey = GlobalKey<FormState>();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: TabberV2(
          title: "预约面谈".tr,
          showBack: true,
        ),
        resizeToAvoidBottomInset: false,
        body: Column(
          children: [
            _buildBody(),
          ],
        ));
  }

  @override
  void reassemble() {
    // TODO: implement reassemble
    super.reassemble();
  }

  final d = DateTime.now();
  bool int1 = true;

  int int_ = 0;

  @override
  void initState() {
    super.initState();
    // WidgetsBinding.instance.addObserver(this);
  }

  Widget _buildBody() {
    FocusNode focusNode = FocusNode();
    return Container(
      alignment: Alignment.center,
      width: kIsWeb &&
              (defaultTargetPlatform == TargetPlatform.macOS ||
                  defaultTargetPlatform == TargetPlatform.windows)
          ? MediaQuery.of(context).size.width * 0.26
          : null,
      padding: const EdgeInsets.fromLTRB(25, 10, 25, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildForm(),
          const Padding(padding: EdgeInsets.only(top: 10)),
          const Text(
            "(公司承诺将保护您的相关个人隐私信息)",
            style: TextStyle(color: Colors.grey, fontSize: 14),
          ),
          const Padding(padding: EdgeInsets.only(top: 20)),
          Container(
            padding: EdgeInsets.only(top: Adapt.heightPt(30)),
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: CustomColors.ThemeColor,
                minimumSize: Size(double.infinity, 48),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
              onPressed: _isLoading
                  ? null
                  : () {
                      setState(() {
                        _isLoading = true;
                      });
                      _updatePwdClick();
                    },
              child: _isLoading
                  ? SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text("提交", style: TextStyle(color: Colors.white)),
            ),
          )
        ],
      ),
    );
  }

  Widget _buildForm() {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          const Padding(padding: EdgeInsets.only(top: 20)),
          _buildJob(),
          const Padding(padding: EdgeInsets.only(top: 20)),
          _buildName(),
          const Padding(padding: EdgeInsets.only(top: 20)),
          _buildDepartment(),
          const Padding(padding: EdgeInsets.only(top: 20)),
          _buildTelephone(),
          const Padding(padding: EdgeInsets.only(top: 20)),
          _buildFeedback(),
          const Padding(padding: EdgeInsets.only(top: 20)),
          _buildTestTime()
        ],
      ),
    );
  }

  Widget _buildJob() {
    return TextFormField(
      autofocus: true,
      controller: _JobController,
      // inputFormatters: [FilteringTextInputFormatter.allow(RegExp("[0-9.]")),],
      onChanged: (e) {
        setState(() {
          _Job = e;
        });
      },

      decoration: InputDecoration(
        border: const OutlineInputBorder(
            borderSide: BorderSide(
                color: Colors.black, width: 3.0, style: BorderStyle.solid)),
        // enabledBorder:const OutlineInputBorder(
        //     borderSide: BorderSide(color: Colors.black, width: 3.0, style: BorderStyle.solid)
        // ) ,
        // focusedBorder:const OutlineInputBorder(
        //     borderSide: BorderSide(color: Colors.black, width: 3.0, style: BorderStyle.solid)
        // ),
        isDense: true,
        contentPadding: EdgeInsets.fromLTRB(10, 10, 10, 0),
        hintText: "请输入工号".tr,
        floatingLabelBehavior: FloatingLabelBehavior.auto,
        label: Text(
          "工号:".tr,
          style: TextStyle(color: CustomColors.ThemeColor, fontSize: 16),
        ),
        focusColor: const Color.fromRGBO(29, 64, 133, 1),
        // focusedBorder: const UnderlineInputBorder(),
        suffixIcon: Offstage(
          offstage: _Job == "",
        ),
      ),
      autovalidateMode: AutovalidateMode.onUserInteraction,
      validator: (v) {
        if (!v!.trim().isNotEmpty) {
          return "工号不能为空".tr;
        }
        return null;
      },
    );
  }

  Widget _buildName() {
    return TextFormField(
      autofocus: true,
      controller: _nameController,
      // inputFormatters: [FilteringTextInputFormatter.allow(RegExp("[0-9.]")),],
      onChanged: (e) {
        setState(() {
          _name = e;
        });
      },

      decoration: InputDecoration(
        border: const OutlineInputBorder(
            borderSide: BorderSide(
                color: Colors.black, width: 3.0, style: BorderStyle.solid)),
        isDense: true,
        contentPadding: EdgeInsets.fromLTRB(10, 10, 10, 0),
        hintText: "".tr,
        floatingLabelBehavior: FloatingLabelBehavior.auto,
        label: Text(
          "中文姓名:".tr,
          style: TextStyle(color: CustomColors.ThemeColor, fontSize: 16),
        ),
        focusColor: const Color.fromRGBO(29, 64, 133, 1),
        // focusedBorder: const UnderlineInputBorder(),
        suffixIcon: Offstage(
          offstage: _name == "",
        ),
      ),
      autovalidateMode: AutovalidateMode.onUserInteraction,
      validator: (v) {
        if (!v!.trim().isNotEmpty) {
          return "姓名不能为空".tr;
        }
        return null;
      },
    );
  }

  Widget _buildDepartment() {
    return TextFormField(
      autofocus: true,
      controller: _departmentController,
      onChanged: (e) {
        setState(() {
          _department = e;
        });
      },
      decoration: InputDecoration(
        border: const OutlineInputBorder(
            borderSide: BorderSide(
                color: Colors.black, width: 3.0, style: BorderStyle.solid)),
        isDense: true,
        contentPadding: EdgeInsets.fromLTRB(10, 10, 10, 0),
        hintText: "请输入所属部门".tr,
        floatingLabelBehavior: FloatingLabelBehavior.auto,
        label: Text(
          "部门:".tr,
          style: TextStyle(color: CustomColors.ThemeColor, fontSize: 16),
        ),
        focusColor: const Color.fromRGBO(29, 64, 133, 1),
        // focusedBorder: const UnderlineInputBorder(),
        suffixIcon: Offstage(
          offstage: _department == "",
        ),
      ),
      autovalidateMode: AutovalidateMode.onUserInteraction,
      validator: (v) {
        if (!v!.trim().isNotEmpty) {
          return "部门不能为空".tr;
        }
        return null;
      },
    );
  }

  Widget _buildTelephone() {
    return TextFormField(
      autofocus: true,
      controller: _Telephonetroller,
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp("[0-9.()_]")),
      ],
      onChanged: (e) {
        setState(() {
          _Telephone = e;
        });
      },
      decoration: InputDecoration(
        border: const OutlineInputBorder(
            borderSide: BorderSide(
                color: Colors.black, width: 3.0, style: BorderStyle.solid)),
        isDense: true,
        contentPadding: EdgeInsets.fromLTRB(10, 10, 10, 0),
        hintText: "请输入手机号".tr,
        floatingLabelBehavior: FloatingLabelBehavior.auto,
        label: Text(
          "手机号".tr,
          style: TextStyle(color: CustomColors.ThemeColor, fontSize: 16),
        ),
        focusColor: const Color.fromRGBO(29, 64, 133, 1),
        // focusedBorder: const UnderlineInputBorder(),
        suffixIcon: Offstage(
          offstage: _Telephone == "",
        ),
      ),
      autovalidateMode: AutovalidateMode.onUserInteraction,
      validator: (v) {
        if (!v!.trim().isNotEmpty) {
          return "手机号不能为空".tr;
        }
        return null;
      },
    );
  }

  Widget _buildFeedback() {
    return TextFormField(
      autofocus: true,
      controller: feedbackController,
      onChanged: (e) {
        setState(() {
          feedback = e;
        });
      },
      decoration: InputDecoration(
        border: const OutlineInputBorder(
            borderSide: BorderSide(
                color: Colors.black, width: 3.0, style: BorderStyle.solid)),

        hintText: "请具体描述您的问题".tr,
        floatingLabelBehavior: FloatingLabelBehavior.auto,
        label: Text(
          "您的问题".tr,
          style: TextStyle(color: CustomColors.ThemeColor, fontSize: 16),
        ),

        focusColor: const Color.fromRGBO(29, 64, 133, 1),
        // focusedBorder: const UnderlineInputBorder(),
        suffixIcon: Offstage(
          offstage: feedback == "",
        ),
      ),
      maxLines: 3,
      minLines: 1,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      validator: (v) {
        if (!v!.trim().isNotEmpty) {
          return "问题不能为空".tr;
        }
        return null;
      },
    );
  }

  Widget _buildTestTime() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "预约日期".tr,
          style: const TextStyle(fontSize: 16),
        ),
        const Padding(padding: EdgeInsets.only(top: 10)),
        ConstrainedBox(
          constraints: const BoxConstraints(
            maxHeight: 40,
          ),
          child: InkWell(
            onTap: () async {
              final DateTime? pickedDate = await showDatePicker(
                context: context,
                initialDate: DateTime.now(),
                firstDate: DateTime(2000),
                lastDate: DateTime.now().add(const Duration(days: 30)),
                selectableDayPredicate: (date) {
                  if (date.isAfter(
                          DateTime.now().add(const Duration(days: -1))) &&
                      date.isBefore(
                          DateTime.now().add(const Duration(days: 30))) &&
                      (date.weekday == 2 || date.weekday == 5)) {
                    return true;
                  }
                  return false;
                },
              );

              if (pickedDate != null) {
                final TimeOfDay? pickedTime = await showTimePicker(
                  context: context,
                  initialTime: TimeOfDay.now(),
                );

                if (pickedTime != null) {
                  final DateTime selectedDateTime = DateTime(
                    pickedDate.year,
                    pickedDate.month,
                    pickedDate.day,
                    pickedTime.hour,
                    pickedTime.minute,
                  );
                  // 处理选中的日期时间
                  // 可以在这里添加状态更新逻辑
                }
              }
            },
            child: Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "${DateTime.now().toString().split(':')[0]}:${DateTime.now().toString().split(':')[1]}",
                    style: const TextStyle(color: Colors.grey),
                  ),
                  const Icon(
                    Icons.arrow_forward_ios,
                    size: 15,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _updatePwdClick() async {
    setState(() {
      _isLoading = false;
    });
    //   if ((_formKey.currentState as FormState).validate() == false) {
    //     setState(() {
    //       _isLoading = false;
    //     });
    //     return;
    //   }
    //
    //   var job = _JobController.text;
    //   var telephone = _Telephonetroller.text;
    //   var name = _nameController.text;
    //   var department = _departmentController.text;
    //
    //   ///忘记密码
    //   ///
    //   ///
    //   ///
    //   ///
    //   ///
    //   var resp = await AuthService().ResetPassword(job,telephone);
    //   print("===================================$resp");
    //
    //   _btnCtrl.stop();
    //
    //
    //   if (resp == null) {
    //     Fluttertoast.showToast(msg: "Tips6".tr);
    //     return;
    //   }
    //   if(resp.message=="") {
    //     Fluttertoast.showToast(msg: "text6_12".tr);
    //   }else
    //     Fluttertoast.showToast(msg: resp.message);
    //   if (resp.success) _back();
    // }
    //
    // void _back() {
    //   ///重制密码成功后回到登陆界面重新登陆
    //   AuthService().GetVerificationCode();
    //   Navigator.pop(context);
  }

  @override
  void dispose() {
    _JobController.dispose();
    _nameController.dispose();
    _Telephonetroller.dispose();
    _departmentController.dispose();
    feedbackController.dispose();
    // WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }
}

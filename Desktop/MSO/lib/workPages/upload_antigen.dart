// 抗原检测上报页面 - 简化版本
// 原始版本因 flutter_picker 和 rounded_loading_button 不兼容 Flutter 3.24.6 而暂时简化
// 原始文件已备份为 upload_antigen_backup.dart

import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';

import '../components/tabbar_v2.dart';
import '../utils/colors.dart';

class UploadAntigenPage extends StatefulWidget {
  UploadAntigenPage({Key? key, this.maxPicSize}) : super(key: key);
  final int? maxPicSize;

  @override
  _UploadAntigenPageState createState() => _UploadAntigenPageState();
}

class _UploadAntigenPageState extends State<UploadAntigenPage> {
  final TextEditingController _antigenResultCtrl = TextEditingController();
  final TextEditingController _antigenDateCtrl = TextEditingController();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _antigenResultCtrl.text = "功能暂时不可用，正在升级中...";
    _antigenDateCtrl.text = "功能暂时不可用，正在升级中...";
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("text3_1".tr),
        backgroundColor: CustomColors.ThemeColor,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "抗原检测上报",
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 20),
            TextField(
              controller: _antigenResultCtrl,
              decoration: InputDecoration(
                labelText: "检测结果",
                border: OutlineInputBorder(),
                enabled: false,
              ),
            ),
            SizedBox(height: 16),
            TextField(
              controller: _antigenDateCtrl,
              decoration: InputDecoration(
                labelText: "检测时间",
                border: OutlineInputBorder(),
                enabled: false,
              ),
            ),
            SizedBox(height: 20),
            Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                border: Border.all(color: Colors.orange),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info, color: Colors.orange),
                      SizedBox(width: 8),
                      Text(
                        "功能升级中",
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.orange.shade800,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),
                  Text(
                    "抗原检测上报功能正在升级以兼容新版本Flutter。\n"
                    "升级完成后将支持：\n"
                    "• 检测时间选择\n"
                    "• 检测结果选择\n"
                    "• 检测图片上传\n"
                    "• 检测信息保存",
                    style: TextStyle(color: Colors.orange.shade700),
                  ),
                ],
              ),
            ),
            Spacer(),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: CustomColors.ThemeColor,
                  padding: EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                onPressed: _isLoading ? null : _showUpgradeMessage,
                child: _isLoading
                    ? SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(
                        "text3_9".tr,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                        ),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showUpgradeMessage() {
    Fluttertoast.showToast(
      msg: "功能升级中，敬请期待！",
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.CENTER,
    );
  }

  @override
  void dispose() {
    _antigenResultCtrl.dispose();
    _antigenDateCtrl.dispose();
    super.dispose();
  }
}

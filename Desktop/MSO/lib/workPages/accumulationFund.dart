import 'package:amkor/components/tabbar.dart';
// import 'package:date_time_picker/date_time_picker.dart'; // 已移除，使用Flutter内置日期选择器
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../utils/adapt.dart';
import 'package:amkor/components/tabbar_v2.dart';
import 'package:amkor/services/authService.dart';
import 'package:amkor/utils/colors.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';

class accumulationFundPage extends StatefulWidget {
  const accumulationFundPage({Key? key}) : super(key: key);

  @override
  State<accumulationFundPage> createState() => _accumulationFundPageState();
}

class _accumulationFundPageState extends State<accumulationFundPage> {
  //手机号
  TextEditingController _Telephonetroller = TextEditingController();
  bool _isLoading = false;
  //反馈
  TextEditingController feedbackController = TextEditingController();
  //手机号
  String _Telephone = "";
  //反馈
  String feedback = "";

  ///

  GlobalKey _formKey = GlobalKey<FormState>();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: TabberV2(
          title: "公积金补缴原件审核预约登记".tr,
          titleSize: 18,
          showBack: true,
        ),
        resizeToAvoidBottomInset: false,
        body: SingleChildScrollView(
            child: Center(
                child: Column(
          children: [
            SizedBox(
              width: MediaQuery.of(context).size.width * 0.80,
              child: const Image(
                image: AssetImage("assets/images/gjjbj_bj.png"),
                fit: BoxFit.fitWidth,
              ),
            ),
            _buildBody(),
          ],
        ))));
  }

  @override
  void reassemble() {
    // TODO: implement reassemble
    super.reassemble();
  }

  final d = DateTime.now();
  bool int1 = true;

  int int_ = 0;

  @override
  void initState() {
    super.initState();
    // WidgetsBinding.instance.addObserver(this);
  }

  Widget _buildBody() {
    return Container(
      alignment: Alignment.center,
      width: kIsWeb &&
              (defaultTargetPlatform == TargetPlatform.macOS ||
                  defaultTargetPlatform == TargetPlatform.windows)
          ? MediaQuery.of(context).size.width * 0.26
          : MediaQuery.of(context).size.width * 0.92,
      padding: const EdgeInsets.fromLTRB(8, 10, 8, 10),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildForm(),
          const Padding(padding: EdgeInsets.only(top: 10)),
          const Text(
            "(公司承诺将保护您的相关个人隐私信息)",
            style: TextStyle(color: Colors.grey, fontSize: 14),
          ),
          const Padding(padding: EdgeInsets.only(top: 20)),
          Container(
            padding: EdgeInsets.only(top: Adapt.heightPt(30)),
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: CustomColors.ThemeColor,
                minimumSize: Size(double.infinity, 48),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
              onPressed: _isLoading
                  ? null
                  : () {
                      setState(() {
                        _isLoading = true;
                      });
                      _updatePwdClick();
                    },
              child: _isLoading
                  ? SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text("提交", style: TextStyle(color: Colors.white)),
            ),
          )
        ],
      ),
    );
  }

  Widget _buildForm() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '为了让员工与公司之间的沟通更便捷、更畅通有效，公司增设了与HR预约面谈的交流方式',
            style: TextStyle(color: Colors.grey, fontSize: 14),
          ),
          const Padding(padding: EdgeInsets.only(top: 20)),
          _buildTelephone(),
          const Padding(padding: EdgeInsets.only(top: 20)),
          _buildTestTime()
        ],
      ),
    );
  }

  Widget _buildTelephone() {
    return TextFormField(
      autofocus: false,
      controller: _Telephonetroller,
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp("[0-9.()_]")),
      ],
      onChanged: (e) {
        setState(() {
          _Telephone = e;
        });
      },
      decoration: InputDecoration(
        border: const OutlineInputBorder(
            borderSide: BorderSide(
                color: Colors.black, width: 3.0, style: BorderStyle.solid)),
        isDense: true,
        contentPadding: EdgeInsets.fromLTRB(10, 10, 10, 0),
        hintText: "请输入手机号".tr,
        floatingLabelBehavior: FloatingLabelBehavior.auto,
        label: Text(
          "手机号".tr,
          style: TextStyle(color: CustomColors.ThemeColor, fontSize: 16),
        ),
        focusColor: const Color.fromRGBO(29, 64, 133, 1),
        // focusedBorder: const UnderlineInputBorder(),
        suffixIcon: Offstage(
          offstage: _Telephone == "",
        ),
      ),
      autovalidateMode: AutovalidateMode.onUserInteraction,
      validator: (v) {
        if (!v!.trim().isNotEmpty) {
          return "手机号不能为空".tr;
        }
        return null;
      },
    );
  }

  Widget _buildTestTime() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "预约日期".tr,
          style: const TextStyle(fontSize: 16),
        ),
        const Padding(padding: EdgeInsets.only(top: 10)),
        ConstrainedBox(
          constraints: const BoxConstraints(
            maxHeight: 40,
          ),
          child: InkWell(
            onTap: () async {
              final DateTime? pickedDate = await showDatePicker(
                context: context,
                initialDate: DateTime.now(),
                firstDate: DateTime(2000),
                lastDate: DateTime.now().add(const Duration(days: 30)),
                selectableDayPredicate: (date) {
                  if (date.weekday == 6 || date.weekday == 7) {
                    return false;
                  }
                  if (date.isAfter(
                          DateTime.now().add(const Duration(days: -1))) &&
                      date.isBefore(
                          DateTime.now().add(const Duration(days: 30)))) {
                    return true;
                  }
                  return false;
                },
              );

              if (pickedDate != null && mounted) {
                final TimeOfDay? pickedTime = await showTimePicker(
                  context: context,
                  initialTime: TimeOfDay.now(),
                );

                if (pickedTime != null) {
                  final DateTime selectedDateTime = DateTime(
                    pickedDate.year,
                    pickedDate.month,
                    pickedDate.day,
                    pickedTime.hour,
                    pickedTime.minute,
                  );
                  // 处理选中的日期时间
                  // 可以在这里添加状态更新逻辑
                }
              }
            },
            child: Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "${DateTime.now().toString().split(':')[0]}:${DateTime.now().toString().split(':')[1]}",
                    style: const TextStyle(color: Colors.grey),
                  ),
                  const Icon(
                    Icons.arrow_forward_ios,
                    size: 15,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _updatePwdClick() async {
    setState(() {
      _isLoading = false;
    });
    //   if ((_formKey.currentState as FormState).validate() == false) {
    //     _btnCtrl.stop();
    //     return;
    //   }
    //
    //   var job = _JobController.text;
    //   var telephone = _Telephonetroller.text;
    //   var name = _nameController.text;
    //   var department = _departmentController.text;
    //
    //   ///忘记密码
    //   ///
    //   ///
    //   ///
    //   ///
    //   ///
    //   var resp = await AuthService().ResetPassword(job,telephone);
    //   print("===================================$resp");
    //
    //   _btnCtrl.stop();
    //
    //
    //   if (resp == null) {
    //     Fluttertoast.showToast(msg: "Tips6".tr);
    //     return;
    //   }
    //   if(resp.message=="") {
    //     Fluttertoast.showToast(msg: "text6_12".tr);
    //   }else
    //     Fluttertoast.showToast(msg: resp.message);
    //   if (resp.success) _back();
    // }
    //
    // void _back() {
    //   ///重制密码成功后回到登陆界面重新登陆
    //   AuthService().GetVerificationCode();
    //   Navigator.pop(context);
  }

  @override
  void dispose() {
    _Telephonetroller.dispose();
    // WidgetsBinding.instance.removeObserver(this);
    feedbackController.dispose();
    super.dispose();
  }
}

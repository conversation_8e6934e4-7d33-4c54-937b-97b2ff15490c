import 'dart:async';
import 'dart:io';
import 'dart:ui';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:qr_flutter/qr_flutter.dart';

import '../model/apiResponse.dart';
import '../model/employee.dart';
import '../services/dto/admittanceCodeDto.dart';
import '../services/employeeService.dart';
import '../utils/adapt.dart';
import '../utils/enums.dart';
import 'package:amkor/utils/debounceAndThrottling.dart' as fd;

class CodeCardComponent extends StatefulWidget {
  final CodeDirection codeDirection;
  const CodeCardComponent({Key? key, this.codeDirection = CodeDirection.In})
      : super(key: key);

  @override
  _CodeCardComponentState createState() => _CodeCardComponentState();
}

class _CodeCardComponentState extends State<CodeCardComponent>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  ApiResponse? r;
  String _d1 = "";
  String _d2 = "";
  int fresh_time = 600;
  Timer? _timer;
  Timer? _timerfd;
  Timer? _autoloadingicon;
  AdmittanceCodeOutputDto? _data;
  Employee? _employee;
  static String? wzpic = 'bus';
  static String? wzus = '  ';
  static String? wzcn = '  ';
  //显示弹窗
  String? showRedCodeMessage = '';
  //刷新页面
  bool RefreshCode = false;
  bool refresh = false;
  late AnimationController animationController;
  Animation? animation;
  var Gh = GetStorage().read('employee')['employeeId'];
  var Neme = GetStorage().read('employee')['employeeName'];
  var rmessage = "";
  // 过期倒记时
  var a = 0;
  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    super.initState();
    if (!kIsWeb && Platform.isAndroid) {
      SystemUiOverlayStyle systemUiOverlayStyle = const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        systemNavigationBarColor: Colors.black,
        statusBarIconBrightness: Brightness.dark,
      );
      SystemChrome.setSystemUIOverlayStyle(systemUiOverlayStyle);
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    }
    animationController = AnimationController(
        duration: const Duration(milliseconds: 600), vsync: this);
    //加载码的内容
    _initCode();
    var e = GetStorage().read("userinfo");
    //动态时间
    final d = DateTime.now();

    // setState(() {
    ///
    _employee = e;

    _d1 =
        "${d.year}-${d.month.toString().padLeft(2, '0')}-${d.day.toString().padLeft(2, '0')} ${d.hour.toString().padLeft(2, '0')}:${d.minute.toString().padLeft(2, '0')}:";
    _d2 = d.second.toString().padLeft(2, '0');
    // });

    _timer = Timer.periodic(const Duration(milliseconds: 1000), (timer) {
      final d = DateTime.now();
      setState(() {
        _d1 =
            "${d.year}-${d.month.toString().padLeft(2, '0')}-${d.day.toString().padLeft(2, '0')} ${d.hour.toString().padLeft(2, '0')}:${d.minute.toString().padLeft(2, '0')}:";
        _d2 = d.second.toString().padLeft(2, '0');
      });
      //print('_timer');
    });
  }

  ///加载码的内容
  Future<void> _initCode() async {
    if (widget.codeDirection == CodeDirection.In) {
      r = await EmployeeService().GetAdmittanceCode();
    } else if (widget.codeDirection == CodeDirection.Out) {
      r = await EmployeeService().GetDepartureCode();
    } else if (widget.codeDirection == CodeDirection.Dormitory) {
      ///宿舍码
      r = await EmployeeService().GetDormitoryCode();
    } else if (widget.codeDirection == CodeDirection.Position) {
      ///位置码
      r = await EmployeeService().GetLocationCode();
      wzpic = r?.data['type'];
      wzus = r?.data['locationCode'];
      wzcn = r?.data['locationName'];
    } else if (widget.codeDirection == CodeDirection.Passport) {
      print('time 0');

      ///员工码
      r = await EmployeeService().GetEmployeeCode(); // 调用接口，获取数据

      if (r == null) {
        Fluttertoast.showToast(msg: "网络异常，请稍后再试");
        return;
      }

      if (!r!.success) {
        Fluttertoast.showToast(msg: r!.message);
        return;
      }

      rmessage = r!.message;
      rmessage = ""; // leo 特殊人员提示（比如：长病假、停薪人员）

      RefreshCode = false;

      setState(() {});

      refresh = r!.success;

      _autoloadingicon =
          Timer.periodic(Duration(milliseconds: fresh_time), (timer) {
        // 延时任务
        _data = AdmittanceCodeOutputDto.fromJson(r!.data);
        refresh = false;

        Timer.periodic(Duration(milliseconds: 10000), (timer) {
          // 延时任务 过期时间Leo 20250417
          RefreshCode = true;
          timer.cancel();
        });

        print('firestloadingicon');
        timer.cancel();
      });

      print('on click 2');
    }

    var codeData = AdmittanceCodeOutputDto.fromJson(r!.data);
    animation = ColorTween(
      begin: Colors.white,
      end: Color.fromRGBO(
        codeData.barcodeColor?[0] ?? 255,
        codeData.barcodeColor?[1] ?? 255,
        codeData.barcodeColor?[2] ?? 255,
        1,
      ),
    ).animate(animationController);
    animation = ColorTween(
      begin: Colors.white,
      end: Color.fromRGBO(
        codeData.barcodeColor?[0] ?? 255,
        codeData.barcodeColor?[1] ?? 255,
        codeData.barcodeColor?[2] ?? 255,
        1,
      ),
    ).animate(animationController);

    animationController.forward();
    animationController.addListener(() {
      setState(() {
        _data = codeData;
      });
    });

    if (codeData.redCodeMessage != null &&
        codeData.redCodeMessage!.isNotEmpty) {
      showRedCodeMessage = codeData.redCodeMessage;
      if (widget.codeDirection != CodeDirection.Passport) {
        _showRedCodeMessage(codeData.redCodeMessage!);
      }
    }
  }

  void _showRedCodeMessage(String message) async {
    AwesomeDialog(
      context: context,
      dialogType: DialogType.warning,
      animType: AnimType.bottomSlide,
      title: '提示',
      desc: message,
      btnOkOnPress: () {},
    ).show();
  }

  @override
  void dispose() {
    if (_timer != null) {
      // 页面销毁时触发定widthPt销毁
      if (_timer?.isActive ?? false) {
        // 判断定时器是否是激活状态
        _timer?.cancel();
      }
    }
    if (_autoloadingicon != null) {
      // 页面销毁时触发定widthPt销毁
      if (_autoloadingicon?.isActive ?? false) {
        // 判断定时器是否是激活状态
        _autoloadingicon?.cancel();
      }
    }

    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  Widget build(BuildContext context) {
    // var h = MediaQuery.of(context).size.height;
    // print("h $h");
    return Center(
      child: Container(
          alignment: Alignment.topCenter,

          ///高度适配web
          padding: EdgeInsets.only(
              top: Adapt.widthPt(46) +
                  MediaQuery.of(context).padding.top +
                  MediaQuery.of(context).size.height * 0.04),
          width: kIsWeb &&
                  (defaultTargetPlatform == TargetPlatform.macOS ||
                      defaultTargetPlatform == TargetPlatform.windows)
              ? MediaQuery.of(context).size.width * 0.24
              : MediaQuery.of(context).size.width *
                  (widget.codeDirection == CodeDirection.Passport ? 0.9 : 1),
          child: _buildCard()),
    );
  }

  ///卡片背景
  Widget _buildCard() {
    var imgPath = widget.codeDirection == CodeDirection.Passport
        ? "assets/images/pic_bg_t1.png"
        : "assets/images/qrcode_bg.png";

    if (Adapt.ratio >= 2) {
      imgPath = widget.codeDirection == CodeDirection.Passport
          ? "assets/images/pic_bg_t1.png"
          : "assets/images/qrcode_bg_h1.png";
    }
    return Container(
      height: (widget.codeDirection == CodeDirection.Passport)
          ? MediaQuery.of(context).size.height *
              (Adapt.ratio >= 2 ? 0.72 : 0.82)
          : MediaQuery.of(context).size.height,
      alignment: Alignment.bottomCenter,
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.all(Radius.circular(6)),
        image: DecorationImage(
          alignment: Alignment.bottomCenter,
          image: AssetImage(imgPath),
          // fit: BoxFit.fitWidth,
          fit: BoxFit.fitHeight,
        ),
      ),
      child: _buildCardContent(),
    );
  }

  /// 卡片内容
  Widget _buildCardContent() {
    final q = MediaQuery.of(context);
    var radio = 1.514;
    // var radio = 1.83;
    if (Adapt.ratio >= 2) {
      radio = 1.83;
    }
    final cordHight = widget.codeDirection == CodeDirection.Passport
        ? q.size.width / 696 * 1154 * 0.9 * 0.19 + 20
        : q.size.height - radio * (q.size.width + 60) + Adapt.heightPt(12);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        kIsWeb &&
                (defaultTargetPlatform == TargetPlatform.macOS ||
                    defaultTargetPlatform == TargetPlatform.windows)
            ? const Padding(padding: EdgeInsets.only(top: 30))
            : Padding(padding: EdgeInsets.only(top: cordHight)),

        if (widget.codeDirection != CodeDirection.Passport)
          Text(
            widget.codeDirection == CodeDirection.Position
                ? "位置码".tr
                : widget.codeDirection == CodeDirection.In
                    ? "进厂码".tr
                    : widget.codeDirection == CodeDirection.Out
                        ? "离厂码".tr
                        : "宿舍码".tr,
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 20,
            ),
          ),
        if (widget.codeDirection != CodeDirection.Passport)
          const Padding(padding: EdgeInsets.only(top: 20)),
        if (widget.codeDirection == CodeDirection.Position)
          Column(
            children: [
              Padding(padding: EdgeInsets.only(top: Adapt.heightPt(10))),
              Text(
                "$wzcn",
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),

        ///用户信息
        if (widget.codeDirection == CodeDirection.Passport) _buildsyxx(),

        AnimatedContainer(
            duration: const Duration(seconds: 1),
            // Provide an optional curve to make the animation feel smoother.
            curve: Curves.fastOutSlowIn,
            child: GestureDetector(
              onTap: RefreshCode
                  ? () {
                      if (_timerfd != null) _timerfd?.cancel();
                      _timerfd = Timer(Duration(seconds: 1), () {
                        RefreshCode = false;
                        _initCode();
                      });
                    }
                  : () {},
              child: SizedBox(
                width: kIsWeb &&
                        (defaultTargetPlatform == TargetPlatform.macOS ||
                            defaultTargetPlatform == TargetPlatform.windows)
                    ? (widget.codeDirection == CodeDirection.Position
                        ? MediaQuery.of(context).size.width * 0.24
                        : MediaQuery.of(context).size.width * 0.3)
                    : (widget.codeDirection == CodeDirection.Position
                        ? MediaQuery.of(context).size.width * 0.46
                        : MediaQuery.of(context).size.width * 0.58),
                // :(widget.codeDirection == CodeDirection.Position?MediaQuery.of(context).size.width * 0.46: MediaQuery.of(context).size.width * 0.58),
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    QrImageView(
                      // padding: const EdgeInsets.all(10),
                      // backgroundColor: Colors.transparent,
                      // backgroundColor: RefreshCode?const Color.fromRGBO(96, 96, 96, 0.6):Colors.transparent,  // Leo 20250421 默认蒙板
                      backgroundColor: RefreshCode
                          ? const Color.fromRGBO(221, 152, 8, 0.7)
                          : Colors.transparent,

                      data: _data?.codeContent ?? "",
                      version: QrVersions.auto,
                      foregroundColor: animation?.value ?? Colors.white,
                      embeddedImage: AssetImage(
                        widget.codeDirection != CodeDirection.Position
                            ? "assets/icons/qr_embedded.png"
                            : "assets/icons/wz_$wzpic.png",
                      ),
                      embeddedImageStyle:
                          const QrEmbeddedImageStyle(size: Size(42, 42)),
                    ),
                    if (refresh) const CircularProgressIndicator()
                  ],
                ),
              ),
            )),
        Padding(padding: EdgeInsets.only(top: Adapt.heightPt(12))),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
              child: Text(
                "  $_d1",
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.w400,
                  fontFeatures: [FontFeature.tabularFigures()],
                ),
              ),
            ),
            SizedBox(
              width: 52,
              child: Text(
                _d2,
                style: const TextStyle(
                  fontSize: 30,
                  fontWeight: FontWeight.w600,
                  color: Color.fromRGBO(202, 31, 31, 1),
                  fontFeatures: [FontFeature.tabularFigures()],
                ),
              ),
            )
          ],
        ),
        if (widget.codeDirection != CodeDirection.Passport)
          Column(
            children: [
              Text(
                _employee?.employeeId ?? "",
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Padding(padding: EdgeInsets.only(top: Adapt.heightPt(10))),
              Text(
                _employee?.employeeName ?? "",
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFFBBBBBB),
                ),
              ),
            ],
          )
        else
          GestureDetector(
            onTap: () {
              if (showRedCodeMessage != '') {
                _showRedCodeMessage(showRedCodeMessage!);
              }
            },
            child: Container(
                alignment: Alignment.centerLeft,
                padding: EdgeInsets.only(
                    top: Adapt.heightPt(6), bottom: Adapt.heightPt(6)),
                width: MediaQuery.of(context).size.width * 0.46,
                child: Column(
                  children: [
                    RefreshCode
                        ? const Text(
                            // "*离线",
                            "*二维码已过期，请点击刷新",
                            style: TextStyle(
                                fontSize: 18,
                                //color: Colors.grey
                                color: Colors.green),
                          )
                        : const Text(
                            // "*在线",
                            "*点击二维码刷新",
                            style: TextStyle(fontSize: 18, color: Colors.grey),
                          ),
                    Text(
                      // "离职员工提示",
                      rmessage,
                      style: TextStyle(fontSize: 18, color: Colors.grey),
                    )
                  ],
                )),
          ),
      ],
    );
  }

  Widget _buildsyxx() {
    return Container(
      padding: EdgeInsets.only(
          right: Adapt.widthPt(24),
          left: Adapt.widthPt(24),
          bottom: Adapt.widthPt(18)),
      child: Row(
        children: [
          GestureDetector(
            child: Container(
              width: 50,
              height: 50,
              alignment: Alignment.topLeft,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(30),
                image: const DecorationImage(
                  image: AssetImage("assets/images/tx_t.png"),
                  fit: BoxFit.fill, // 完全填充
                ),
              ),
            ),
          ),
          const Padding(padding: EdgeInsets.only(right: 10)),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(Neme,
                  style: TextStyle(
                      color: Colors.black,
                      fontSize: Adapt.widthPt(22),
                      fontWeight: FontWeight.w600)),
              const Padding(
                padding: EdgeInsets.all(4.0),
              ),
              Text('工号: $Gh',
                  style: TextStyle(
                      color: Colors.black, fontSize: Adapt.widthPt(14))),
            ],
          ),
        ],
      ),
    );
  }

  // @override
  Future<void> didChangeAppLifecycleState(AppLifecycleState state) async {
    switch (state) {
      case AppLifecycleState.resumed:
        if (_timer != null) {
          // 页面销毁时触发定widthPt销毁
          if (_timer?.isActive ?? false) {
            // 判断定时器是否是激活状态
            _timer?.cancel();
          }
        }
        if (_autoloadingicon != null) {
          // 页面销毁时触发定widthPt销毁
          if (_autoloadingicon?.isActive ?? false) {
            // 判断定时器是否是激活状态
            _autoloadingicon?.cancel();
          }
        }

        _timer = Timer.periodic(const Duration(milliseconds: 1000), (timer) {
          final d = DateTime.now();
          setState(() {
            _d1 =
                "${d.year}-${d.month.toString().padLeft(2, '0')}-${d.day.toString().padLeft(2, '0')} ${d.hour.toString().padLeft(2, '0')}:${d.minute.toString().padLeft(2, '0')}:";
            _d2 = d.second.toString().padLeft(2, '0');
          });
        });

        setState(() {});

        print('重新建立定时器循环');
        // 从后台返回到前台 的事件监听
        print('后台切换前台。。。');

        break;
      case AppLifecycleState.inactive:
        if (_timer != null) {
          // 页面销毁时触发定widthPt销毁
          if (_timer?.isActive ?? false) {
            // 判断定时器是否是激活状态
            _timer?.cancel();
          }
        }
        if (_autoloadingicon != null) {
          // 页面销毁时触发定widthPt销毁
          if (_autoloadingicon?.isActive ?? false) {
            // 判断定时器是否是激活状态
            _autoloadingicon?.cancel();
          }
        }
        print('前台切换后台。。。');

        break;
      case AppLifecycleState.paused:
      case AppLifecycleState.detached:
      case AppLifecycleState.hidden:
        // _autoloadcode?.cancel();

        break;
    }
  }
}

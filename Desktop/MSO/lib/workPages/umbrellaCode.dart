import 'package:amkor/components/tabbar.dart';
import 'package:amkor/services/dto/getPositionDto.dart';
import 'package:amkor/services/employeeService.dart';
import 'package:amkor/utils/adapt.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:mobile_scanner/mobile_scanner.dart';

class UmbrellaCodePage extends StatefulWidget {
  const UmbrellaCodePage({Key? key}) : super(key: key);

  @override
  State<UmbrellaCodePage> createState() => _UmbrellaCodePageState();
}

class _UmbrellaCodePageState extends State<UmbrellaCodePage> {
  GetPositionOutputDto? _position;
  MobileScannerController? _controller;

  final _employeeService = EmployeeService();

  @override
  void initState() {
    super.initState();
    if (!kIsWeb) Adapt.setAndroid(isDark: false);
    _controller = MobileScannerController();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: <Widget>[
          Positioned(
            child: SizedBox(
              height: MediaQuery.of(context).size.height +
                  MediaQuery.of(context).padding.bottom,
              width: MediaQuery.of(context).size.width,
              child: _buildQrView(context),
            ),
          ),
          Positioned(
            child: Tabbar(showBack: true),
          ),
          Positioned(
            bottom: 148,
            left: 90,
            child: GestureDetector(
              child: const ImageIcon(
                AssetImage("assets/icons/light.png"),
                color: Colors.white,
              ),
              onTap: () {},
            ),
          ),
          Positioned(
            bottom: 148,
            right: 90,
            child: GestureDetector(
              child: const ImageIcon(
                AssetImage("assets/icons/light.png"),
                color: Colors.white,
              ),
              onTap: () {
                _controller?.toggleTorch();
              },
            ),
          )
        ],
      ),
    );
  }

  ///移动端扫码支持二维码和条形码
  Widget _buildQrView(BuildContext context) {
    return MobileScanner(
      controller: _controller,
      onDetect: (BarcodeCapture capture) {
        final List<Barcode> barcodes = capture.barcodes;
        if (barcodes.isNotEmpty) {
          final String code = barcodes.first.displayValue ?? '';
          if (code.isNotEmpty) {
            _onScanResult(code);
          }
        }
      },
    );
  }

  /// 扫码结果事件
  Future _onScanResult(String scanData) async {
    _controller?.stop();

    final u = Uri.tryParse(scanData);
    if (u == null) {
      Fluttertoast.showToast(msg: "读取码失败，请重试");
      _controller?.start();
      return;
    }
    var positionCode = u.queryParameters["code"];
    print('----------------------------$u');
    print('----------------------------$positionCode');
    if (positionCode == null || positionCode.isEmpty) {
      Fluttertoast.showToast(msg: "读取码失败，请重试");
      _controller?.start();
      return;
    }

    EasyLoading.show();
    var positionRes = await _employeeService.GetPosition(positionCode);
    if (positionRes == null) {
      EasyLoading.dismiss();
      Fluttertoast.showToast(msg: "网络异常，请稍后再试");
      _controller?.start();
      return;
    }
    if (positionRes.data == null) {
      EasyLoading.dismiss();
      Fluttertoast.showToast(msg: positionRes.message);
      _controller?.start();
      return;
    }
    var position = GetPositionOutputDto.fromJson(positionRes.data!);
    setState(() {
      _position = position;
    });

    EasyLoading.dismiss();
    if (mounted) {
      AwesomeDialog(
        context: context,
        dialogType: DialogType.info,
        animType: AnimType.bottomSlide,
        title: '当前所在位置',
        desc:
            '位置: ${_position?.locationName ?? "未知"}\n位置码: ${_position?.locationCode ?? "未知"}',
        btnOkOnPress: () {
          _controller?.start();
        },
      ).show();
    }
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }
}

import 'package:amkor/components/codeCard.dart';
import 'package:amkor/components/tabbar.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:screen_brightness/screen_brightness.dart';

import '../utils/Adapt.dart';
import '../utils/enums.dart';

class ShowCodePage extends StatefulWidget {
  final CodeDirection codeDirection;
  const ShowCodePage({Key? key, this.codeDirection = CodeDirection.In})
      : super(key: key);

  @override
  _ShowCodePageState createState() => _ShowCodePageState();
}

class _ShowCodePageState extends State<ShowCodePage> {
  @override
  void initState() {
    // 屏幕亮度调节
    // setBrightness(8);
    // resetBrightness();
    getSystemBrightness1();
    if (cur_bri < 0.96){
      setBrightness(0.96);
    }

    if(!kIsWeb) {
      Adapt.setAndroid();
    }
    super.initState();
  }

  @override
  void dispose() {  // 结束屏幕时，调用这个
    super.dispose();
    resetBrightness();
  }

  double cur_bri = 0 ;
  Future<void> getSystemBrightness1() async {
    cur_bri = await systemBrightness;
  }

  @override
  Widget build(BuildContext context) {
    final q = MediaQuery.of(context);
    // print("fh , ${q.size.height}");
    return Scaffold(
      body: Stack(
        children: [

          Container(
            height: q.size.height,
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage("assets/images/pic_bg.png"),
                fit: BoxFit.cover,
                repeat: ImageRepeat.repeatY,
              ),
            ),
          ),
          // SlideBanner(list: const [
          //   'assets/images/show_code_bg2.png',
          //   'assets/images/show_code_bg2.png',
          //   'assets/images/show_code_bg2.png',
          // ],),
          CodeCardComponent(codeDirection: widget.codeDirection),
          Tabbar(
            title: widget.codeDirection==CodeDirection.Passport? "Passport" : "",
            showBack: true,
            fontColor: Colors.white,
          ),
        ],
      ),
    );
  }
  Future<double> get systemBrightness async { // 系统亮度
    try {
      return await ScreenBrightness().system;
    } catch (e) {
      print(e);
      throw 'Failed to get system brightness';
    }
  }

  Future<void> setBrightness(double brightness) async {  //
    try {
      await ScreenBrightness().setScreenBrightness(brightness);
    } catch (e) {
      print(e);
      throw 'Failed to set brightness';
    }
  }

  Future<double> get currentBrightness async {
    try {
      print(ScreenBrightness().current);
      return await ScreenBrightness().current;
    } catch (e) {
      print(e);
      throw 'Failed to get current brightness';
    }
  }

  Future<void> resetBrightness() async {
    try {
      await ScreenBrightness().resetScreenBrightness();
    } catch (e) {
      debugPrint(e.toString());
      throw 'Failed to reset brightness';
    }
  }
}

import 'dart:io';

import 'package:amkor/components/tabbar.dart';
import 'package:amkor/services/dto/uploadCovidDto.dart';
import 'package:amkor/services/employeeService.dart';
import 'package:amkor/utils/Adapt.dart';
import 'package:amkor/utils/addj.dart';
import 'package:amkor/utils/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

class CustomReportPage extends StatefulWidget {
  const CustomReportPage({Key? key}) : super(key: key);

  @override
  State<CustomReportPage> createState() => _CustomReportPageState();
}

class _CustomReportPageState extends State<CustomReportPage> {
  final List<List<String>> _sportValueData = [];
  final ImagePicker _imagePicker = ImagePicker();
  bool _isLoading = false;

  ///传路径
  final formdata = UploadCovidInputDto();

  ///
  ///
  ///
  ///
  String filePath = "assets/icons/add_pic.png";
  @override
  void initState() {
    // TODO: implement activate
    super.initState();
    for (int i = 0; i < Global().homeData1.length; i++) {
      _sportValueData.add([
        '',
        '',
        '',
        '',
        '',
        '',
      ]);
    }
  }

  ///上报按钮
  Widget _buildBtn() {
    return Padding(
      padding: EdgeInsets.only(bottom: 30),
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: CustomColors.ThemeColor,
          minimumSize: Size(double.infinity, 48),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
          ),
        ),
        onPressed: _isLoading
            ? null
            : () {
                setState(() {
                  _isLoading = true;
                });
                upload();
              },
        child: _isLoading
            ? SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Text("text2_13".tr, style: TextStyle(color: Colors.white)),
      ),
    );
  }

  void upload() async {
    if (formdata.testTime == null || formdata.testTime!.isEmpty) {
      Fluttertoast.showToast(msg: "Tips2".tr);
      setState(() {
        _isLoading = false;
      });
      return;
    }
    var time = DateTime.parse(formdata.testTime!);
    if (time.isAfter(DateTime.now())) {
      Fluttertoast.showToast(msg: "Tips3".tr);
      setState(() {
        _isLoading = false;
      });
      return;
    }

    if (formdata.testResult == null || formdata.testResult!.isEmpty) {
      Fluttertoast.showToast(msg: "Tips4".tr);
      setState(() {
        _isLoading = false;
      });
      return;
    }

    if ((formdata.filePath == null || formdata.filePath!.isEmpty)) {
      Fluttertoast.showToast(msg: "Tips5".tr);
      setState(() {
        _isLoading = false;
      });
      return;
    }

    var r = await EmployeeService().UploadCovid(formdata.clone());

    ///web

    print('-=-=-=--=-=-=--=-=-=-=-=-=-$r');
    print(r);
    setState(() {
      _isLoading = false;
    });
    if (r == null) {
      Fluttertoast.showToast(msg: "Tips6".tr);
      return;
    }

    Fluttertoast.showToast(msg: r.message);
    if (!r.success) {
      return;
    }
    if (mounted) Navigator.of(context).pop();
  }

  Widget _reportForm(List<Map<String, dynamic>> reportData) {
    return Column(
      children: [
        for (int i = 0; i < reportData.length; i++)
          Padding(
              padding: const EdgeInsets.all(30),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.only(bottom: 10),
                    child: Text(reportData[i]['题目']),
                  ),
                  Container(
                    decoration: const BoxDecoration(
                      border: Border(
                          top: BorderSide(color: Colors.black12, width: 1),
                          left: BorderSide(color: Colors.black12, width: 1),
                          right: BorderSide(
                            color: Colors.black12,
                            width: 1,
                          )),
                    ),
                    child: Column(
                      children: [
                        ///单选
                        if (reportData[i]['类型'] == '单选')
                          for (int j = 0; j < reportData[i]['选项'].length; j++)
                            Container(
                              decoration: const BoxDecoration(
                                border: Border(
                                    bottom: BorderSide(
                                  color: Colors.black12,
                                  width: 1,
                                )),
                              ),
                              child: Row(
                                children: [
                                  Radio<String>(
                                    value: reportData[i]['选项'][j],
                                    groupValue: _sportValueData[i][0],
                                    onChanged: (value) {
                                      setState(() {
                                        _sportValueData[i][0] = value!;
                                      });
                                    },
                                  ),
                                  Expanded(child: Text(reportData[i]['选项'][j])),
                                ],
                              ),
                            ),

                        ///多选
                        if (reportData[i]['类型'] == '多选')
                          for (int j = 0; j < reportData[i]['选项'].length; j++)
                            Container(
                              decoration: const BoxDecoration(
                                border: Border(
                                    bottom: BorderSide(
                                  color: Colors.black12,
                                  width: 1,
                                )),
                              ),
                              child: Row(
                                children: [
                                  Checkbox(
                                    value: _sportValueData[i][j] ==
                                        reportData[i]['选项'][j],
                                    onChanged: (value) {
                                      setState(() {
                                        _sportValueData[i][j] =
                                            _sportValueData[i][j] ==
                                                    reportData[i]['选项'][j]
                                                ? ''
                                                : reportData[i]['选项'][j];
                                      });
                                    },
                                  ),
                                  Expanded(child: Text(reportData[i]['选项'][j])),
                                ],
                              ),
                            ),

                        ///图片
                        if (reportData[i]['类型'] == '图片')
                          Container(
                            decoration: const BoxDecoration(
                              border: Border(
                                  bottom: BorderSide(
                                color: Colors.black12,
                                width: 1,
                              )),
                            ),
                            child: Row(
                              children: [
                                InkWell(
                                  onTap: () async {
                                    final XFile? img =
                                        await _imagePicker.pickImage(
                                            source: ImageSource.gallery,
                                            maxHeight: 2400,
                                            maxWidth: 1080,
                                            imageQuality: 50);
                                    if (img == null) {
                                      return;
                                    }
                                    var imgSize = await img.length();

                                    File? compressedFile;
                                    final compressedXFile =
                                        await FlutterImageCompress
                                            .compressAndGetFile(
                                      img.path,
                                      img.path
                                          .replaceAll('.jpg', '_compressed.jpg')
                                          .replaceAll(
                                              '.png', '_compressed.png'),
                                      quality: 50,
                                      minWidth: 1080,
                                      minHeight: 1080,
                                    );
                                    if (compressedXFile != null) {
                                      compressedFile =
                                          File(compressedXFile.path);
                                      imgSize = compressedFile.lengthSync();
                                    }

                                    ///压缩后的图片限制200kb
                                    if (imgSize >= 2400000) {
                                      Fluttertoast.showToast(msg: "Tips1".tr);
                                      return;
                                    }
                                    formdata.filePath =
                                        compressedFile?.path ?? img.path;

                                    print('-=-=-=${img.path}=====${img.path}');

                                    setState(() {
                                      // filePath = compressedFile!.path;
                                      _sportValueData[i][0] =
                                          compressedFile!.path;
                                      _sportValueData[i][1] = "true";
                                    });
                                  },
                                  child: Container(
                                    width: 60,
                                    height: 60,
                                    alignment: Alignment.center,
                                    decoration: const BoxDecoration(
                                      image: DecorationImage(
                                        image: AssetImage(
                                            "assets/icons/add_pic.png"),
                                        fit: BoxFit.fill, // 完全填充
                                      ),
                                    ),
                                    child: (_sportValueData[i][1] == ''
                                            ? false
                                            : _sportValueData[i][1] == "true")
                                        ? Image.file(
                                            File(_sportValueData[i][0] == ''
                                                ? "assets/icons/add_pic.png"
                                                : _sportValueData[i][0]),
                                            height: 60,
                                            width: 60,
                                          )
                                        : Image(
                                            image: AssetImage(
                                                _sportValueData[i][0] == ''
                                                    ? "assets/icons/add_pic.png"
                                                    : _sportValueData[i][0]),
                                            height: 60,
                                            width: 60,
                                          ),
                                  ),
                                ),
                                const Padding(
                                  padding: EdgeInsets.only(left: 20),
                                ),
                                Expanded(child: Text(reportData[i]['选项'])),
                              ],
                            ),
                          )
                      ],
                    ),
                  )
                ],
              ))
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
            title: const Text(
              '自定义报表',
              style:
                  TextStyle(color: Colors.black, fontWeight: FontWeight.w600),
            ),
            centerTitle: true,
            iconTheme: const IconThemeData(
              color: Colors.black, //修改颜色
            ),
            backgroundColor: Colors.white,
            elevation: 0),
        body: SingleChildScrollView(
          child: Column(
            children: [
              // Tabbar(
              //   title: "自定义报表",
              //   showBack: true,
              //   fontColor: Colors.black,
              // ),
              _reportForm(Global().homeData1),
              _buildBtn()
            ],
          ),
        ));
  }
}

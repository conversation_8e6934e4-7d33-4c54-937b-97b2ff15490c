
import 'package:amkor/Notice_xq_pv.dart';
import 'package:amkor/components/routes.dart';
import 'package:amkor/services/employeeService.dart';
import 'package:amkor/utils/Adapt.dart';
import 'package:amkor/utils/verticalText.dart';

import 'package:flutter/material.dart';


class ShuttleBusDetailsPage extends StatefulWidget {
   ShuttleBusDetailsPage({Key? key ,required this.id,this.line=false}) : super(key: key);
  int id;
  bool line;
  @override
  State<ShuttleBusDetailsPage> createState() => _ShuttleBusDetailsState();
}

class _ShuttleBusDetailsState extends State<ShuttleBusDetailsPage>   {
  late PageController _pageController; // 缓存页面
  //记录当前圆点下标
  int yd=0;
  int dq=0;
  int _selectedIndex=0;
  List<bool> _status=[];
  dynamic lineData;
  bool line=true;
  bool status=true;
  var indexData;
  var indexDataLength;
  Future<void> _initData() async{
    var r=await BusApp().GetBusLineDetail(widget.id);
    print('-->-->${r!.data}');
    lineData=r!.data;
    indexData=line?lineData["upLine"]:lineData["downLine"];
    indexDataLength=indexData.length;
    print("===========================${lineData}");
    setState(() {
    });
  }
  @override
  void initState() {
    // TODO: implement activate
    super.initState();
    line=widget.line;
    _initData();
    _pageController = PageController(initialPage: _selectedIndex);
    for(int i=0;i<20;i++) {
      _status.add(false);
    }
    _status[0]=true;
  }

  @override
  void dispose() {
    super.dispose();
  }


  ///班车详情模块聚合
  Widget shuttleRoute(){
    return Padding(
      padding:const EdgeInsetsDirectional.only(top:18,bottom: 20),
      child:Card(
        color: Colors.white, // 背景色
        shadowColor: Colors.grey, // 阴影颜色
        elevation: 2, // 阴影高度
        borderOnForeground: false, // 是否在 child 前绘制 border，默认为 true
        margin: const EdgeInsets.fromLTRB(0, 0, 0, 0), // 外边距
        // 边框
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          // side: const BorderSide(
          //   color: Colors.grey,
          //   width: 1,
          // ),
        ),
        child: Padding(
            padding:const EdgeInsetsDirectional.all(14),
            child:Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                        height: 30,
                        width: 30,
                        margin:const EdgeInsetsDirectional.only(start: 4,end: 4),
                        alignment: Alignment.center,
                        decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                colors: [
                                  Color.fromRGBO(66, 173, 245, 1),
                                  Color.fromRGBO(75, 197, 250, 1),
                                ]
                            )
                        ),
                        child: Text(lineData["busNo"],style:const TextStyle(color: Colors.white,fontSize: 14,),)
                    ),
                    Container(
                      alignment: Alignment.center,
                      child: Text(lineData["lineName"],
                        style: const TextStyle(color: Color.fromRGBO(20, 178, 181, 1),fontSize: 14,),
                        maxLines: 2, // 最大行数为 2
                        overflow: TextOverflow.ellipsis, // 超出部分以省略号形式显示
                      ),
                    ),
                  ],
                ),
                Padding(
                  padding:const EdgeInsetsDirectional.only(start: 34),
                  child:Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: Container(
                          padding:const EdgeInsetsDirectional.only(start: 2,end: 2),
                          alignment: Alignment.center,
                          child: Text(indexData[0]["stationName"],
                            style: const TextStyle(color: Colors.black,fontSize: 12,),
                            maxLines: 2, // 最大行数为 2
                            overflow: TextOverflow.ellipsis, // 超出部分以省略号形式显示
                          ),
                        ),
                      ),
                      GestureDetector(
                        onTap: (){
                          line=!line;
                          indexData=line?lineData["upLine"]:lineData["downLine"];
                          indexDataLength=indexData.length;
                          for(int i=0;i<indexDataLength;i++) {
                            _status[i]=false;
                          }
                          _status[0]=true;
                          dq=0;
                          setState((){});
                        },
                        child: Padding(
                          padding:const EdgeInsetsDirectional.only(start: 4,end: 4),
                          child: Image(
                            image:const AssetImage("assets/icons/exchange.png"),
                            height: Adapt.widthPt(24),
                            width: Adapt.widthPt(24),
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Container(
                          padding:const EdgeInsetsDirectional.only(start: 2,end: 2),
                          alignment: Alignment.center,
                          child: Text(indexData[indexDataLength-1]["stationName"],
                            style:const TextStyle(color: Colors.black,fontSize: 12,),
                            maxLines: 2, // 最大行数为 2
                            overflow: TextOverflow.ellipsis, // 超出部分以省略号形式显示
                          ),
                        ),
                      ),
                      const Padding(padding:EdgeInsetsDirectional.only(start: 60)),
                    ],
                  ) ,
                ),
                Container(
                  padding:const EdgeInsetsDirectional.only(start: 36,top: 10,bottom: 10),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text.rich(TextSpan(
                          children: [
                            const TextSpan(
                                text: "班次:  ",
                                style: TextStyle(color: Colors.grey,fontSize: 12,)),
                            TextSpan(
                                text: lineData["busTypeName"],
                                style: const TextStyle(color: Colors.black,fontSize: 12,)),
                          ]
                      )),
                      const Padding(padding: EdgeInsetsDirectional.all(6)),
                      GestureDetector(
                        onTap: (){
                          status=!status;
                          setState((){});
                        },
                        child:Text.rich(
                            overflow:status?TextOverflow.ellipsis:null,
                            maxLines:status?3:null,
                            TextSpan(
                                children: [
                                  const TextSpan(
                                      text: "途径:  ",
                                      style: TextStyle(color: Colors.grey,fontSize: 12,)),
                                  TextSpan(
                                      text: lineData["way"]!=null?"${lineData["way"]}".trim().replaceAll(RegExp("\\s+"), " "):"",
                                      style: const TextStyle(color: Colors.black,fontSize: 12,)),
                                ]
                            )
                        ),
                      ),
                      const Padding(padding: EdgeInsetsDirectional.all(6)),
                      Text.rich(TextSpan(
                          children: [
                            const TextSpan(
                                text: "备注:  ",
                                style: TextStyle(color: Colors.grey,fontSize: 12,)),
                            TextSpan(
                                text: lineData["remark"]??"",
                                style: const TextStyle(color: Colors.black,fontSize: 12,)),
                          ]
                      )),
                    ],
                  ) ,
                ),
                const Divider(
                  height: 2,
                  color: Color.fromRGBO(223, 229, 230, 1),
                ),
                const Padding(padding: EdgeInsetsDirectional.all(4)),
                SizedBox(
                    height: 190,
                    child: ListView(
                      scrollDirection: Axis.horizontal,
                      children: [
                        Stack(
                          children: [
                            Container(
                              height: 4,
                              width: (indexDataLength-1)*64.0,
                              margin: EdgeInsetsDirectional.only(start: 32,top:lineData["busType"]=="other"?60:44,end: 0),
                              decoration: BoxDecoration(
                                border: Border.all(color:const Color.fromRGBO(180, 194, 194, 1),width: 2),

                              ),
                            ),
                            ListView.builder(
                                itemCount: indexDataLength,
                                scrollDirection: Axis.horizontal,
                                shrinkWrap: true,
                                physics:const NeverScrollableScrollPhysics(),
                                itemBuilder: (context, index) {
                                  return GestureDetector(
                                    onTap:(){
                                      setState((){
                                        for(int i=0;i<indexDataLength;i++) {
                                          _status[i]=false;
                                        }
                                        _status[index]=true;
                                        dq=index;
                                        _selectedIndex=0;
                                        print("object========$_status");
                                      });
                                    },
                                    child: SizedBox(
                                      width: 64,
                                      child:Column(
                                        crossAxisAlignment: CrossAxisAlignment.center,
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        children: [
                                          if(index==0)
                                            Container(
                                              width: 18,
                                              height: 18,
                                              alignment: Alignment.center,
                                              decoration: BoxDecoration(
                                                color:const Color.fromRGBO(255, 141, 26, 0.2),
                                                borderRadius:const BorderRadiusDirectional.all(Radius.circular(2)),
                                                border: Border.all(color:const Color.fromRGBO(255, 141, 26, 1),width: 1),

                                              ),
                                              child:const Text('始',style: TextStyle(color: Color.fromRGBO(255, 141, 26, 1),fontSize: 12,),),
                                            ),
                                          if(index==indexDataLength-1)
                                            Container(
                                              width: 18,
                                              height: 18,
                                              alignment: Alignment.center,
                                              decoration: BoxDecoration(
                                                color:const Color.fromRGBO(20, 178, 181, 0.2),
                                                borderRadius:const BorderRadiusDirectional.all(Radius.circular(2)),
                                                border: Border.all(color:const Color.fromRGBO(20, 178, 181, 1),width: 1),

                                              ),
                                              child:const Text('终',style: TextStyle(color: Color.fromRGBO(20, 178, 181, 1),fontSize: 12,),),
                                            ),
                                          if(index!=0&&index!=indexDataLength-1)
                                            const SizedBox(
                                              width: 18,
                                              height: 18,),
                                          Text(indexData[index]["arrivedTime"]??"",style: TextStyle(color:_status[index]?const Color.fromRGBO(20, 178, 181, 1):Colors.black,fontSize: 14,),),
                                          if(lineData["busType"]=="other")Text("${indexData[index]["arrivedTime2"]}",style: TextStyle(color:_status[index]?const Color.fromRGBO(20, 178, 181, 1):Colors.black,fontSize: 14,),),
                                          Container(
                                            width: 18,
                                            height: 18,
                                            margin:const EdgeInsetsDirectional.only(start:16,top: 3,end: 16,bottom: 6),
                                            decoration: BoxDecoration(
                                              color:const Color.fromRGBO(242, 245, 245, 1),
                                              borderRadius:const BorderRadiusDirectional.all(Radius.circular(20)),
                                              border: Border.all(color:const Color.fromRGBO(180, 194, 194, 1),width: 3),

                                            ),
                                          ),
                                          VerticalText(
                                            key: UniqueKey(),
                                            text:indexData[index]["stationName"].replaceAllMapped(RegExp(r"([()（）])"), (match) => match.group(0) == '(' || match.group(0) == '（' ? '︵' : '︶'),
                                            wrapLength:7,color:_status[index]?const Color.fromRGBO(20, 178, 181, 1):Colors.black,
                                            fontSize: 14,
                                            height: 1.1,)

                                        ],
                                      ),
                                    )
                                  );

                                }),

                          ],
                        ),
                      ],
                    )
                ),


              ],
            )
        ),
      ),
    );

  }
  ///站点详情模块
  Widget siteDetails(int index){
    return Padding(
      padding:const EdgeInsetsDirectional.only(bottom: 20),
      child:Card(
        color: Colors.white, // 背景色
        shadowColor: Colors.grey, // 阴影颜色
        elevation: 2, // 阴影高度
        borderOnForeground: false, // 是否在 child 前绘制 border，默认为 true
        margin: const EdgeInsets.fromLTRB(0, 0, 0, 0), // 外边距
        // 边框
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          // side: const BorderSide(
          //   color: Colors.grey,
          //   width: 1,
          // ),
        ),

        child: Padding(
            padding:const EdgeInsetsDirectional.all(14),
            child:Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding:const EdgeInsetsDirectional.only(bottom: 20,start: 4,end: 4),
                  child: Image(
                    image:const AssetImage("assets/icons/wxts.png"),
                    height: Adapt.widthPt(24),
                  ),
                ),
                Text.rich(TextSpan(
                    children: [
                      const TextSpan(
                          text: "站点名称:  ",
                          style: TextStyle(color: Colors.grey,fontSize: 12,)),
                      TextSpan(
                          text: indexData[index]["stationName"]??'',
                          style: const TextStyle(color: Color.fromRGBO(20, 178, 181, 1),fontSize: 12,)),
                    ]
                )),
                const Padding(padding: EdgeInsetsDirectional.all(6)),
                Text.rich(TextSpan(
                    children: [
                      const TextSpan(
                          text: "到站时间:  ",
                          style: TextStyle(color: Colors.grey,fontSize: 12,)),
                      TextSpan(
                          text:lineData["busType"]!="other"? "${indexData[index]["arrivedTime"]}":
                          indexData[index]["arrivedTime"] != ""||indexData[index]["arrivedTime2"] != ""?"${indexData[index]["arrivedTime"]}/${indexData[index]["arrivedTime2"]}":"",
                          style: const TextStyle(color: Colors.black,fontSize: 12,)),
                    ]
                )),
                const Padding(padding: EdgeInsetsDirectional.all(6)),
                Text.rich(TextSpan(
                    children: [
                      const TextSpan(
                          text: "站点指南:  ",
                          style: TextStyle(color: Colors.grey,fontSize: 12,)),
                      TextSpan(
                          text: indexData[index]["remark"],
                          style: const TextStyle(color: Colors.black,fontSize: 12,)),
                    ]
                )),
                const Padding(padding: EdgeInsetsDirectional.all(4)),
                const Divider(
                  height: 2,
                  color: Color.fromRGBO(223, 229, 230, 1),
                ),
                if(indexData[index]["images"].length>0) Padding(
                   padding:const EdgeInsetsDirectional.only(start: 0,top: 28,end: 0,bottom: 8),
                   child: Container(
                     clipBehavior: Clip.hardEdge,
                     decoration: const BoxDecoration(
                       borderRadius: BorderRadius.all(Radius.circular(3)),
                       color:  Colors.white,
                     ),
                     height: MediaQuery.of(context).size.height*0.14,
                     child: Row(
                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
                     children: [
                       // Container(
                       //   width: 33,
                       //   height: 33,
                       //   margin:const EdgeInsetsDirectional.only(end: 10),
                       //   decoration: BoxDecoration(
                       //     border: Border.all(width: 1, color: Colors.grey),
                       //     color: Colors.white, // 底色
                       //     boxShadow: const [
                       //       BoxShadow(
                       //         blurRadius: 4, //阴影范围
                       //         spreadRadius: 0.1, //阴影浓度
                       //         color: Colors.grey, //阴影颜色
                       //       ),
                       //     ],
                       //   ),
                       //   child:IconButton(
                       //     padding:const EdgeInsetsDirectional.all(0),
                       //     onPressed: () {
                       //       if(_selectedIndex>0) {
                       //         _selectedIndex--;
                       //         _pageController.jumpToPage(_selectedIndex);
                       //       }
                       //       setState(() {
                       //       });
                       //     },
                       //     //arrow_forward_ios
                       //     icon:  Icon(
                       //       Icons.arrow_back_ios_sharp,
                       //       color:_selectedIndex>0?const Color.fromRGBO(20, 178, 181, 1):Colors.grey,
                       //       size: 22,
                       //     ),
                       //   ),
                       //
                       // ),

                       Expanded(
                         child:PageView(
                             onPageChanged: (int index) {
                                 _selectedIndex = index;
                                     setState(() {});
                             },
                             controller: _pageController,
                             children:[
                               for(int i=0;i<indexData[index]["images"].length;i++)
                                 GestureDetector(
                                     onTap: (){
                                       var r=[];
                                       indexData[index]["images"].map((e)=>r.add(e["url"])).toList();
                                       Navigator.push(context,
                                           PopRoute(
                                               child: PhotoPreview(galleryItems:r,defaultImage:i)
                                           )
                                       ).then((value) {
                                         if (value != _selectedIndex) {
                                           _selectedIndex=value;
                                           _pageController.jumpToPage(_selectedIndex);
                                           setState(() {
                                           });
                                         }
                                       });
                                     },
                                     child:Image.network(indexData[index]["images"][i]["url"].replaceAll("\\","/"),
                                       width: MediaQuery.of(context).size.width *0.9,
                                       height: MediaQuery.of(context).size.height *0.2,
                                       fit: BoxFit.cover,
                                       errorBuilder: (a,b,c){
                                         return Image(
                                           image: const AssetImage('assets/images/error_pic_long.png'),
                                           width: MediaQuery.of(context).size.width *0.92,
                                           height: MediaQuery.of(context).size.height *0.2,
                                           fit: BoxFit.cover,);
                                       },
                                     )
                                 ),
                             ]

                         ),

                       ),
                       // Container(
                       //   width: 33,
                       //   height: 33,
                       //   margin:const EdgeInsetsDirectional.only(start: 10),
                       //   decoration: BoxDecoration(
                       //     border: Border.all(width: 1, color: Colors.grey),
                       //     color: Colors.white, // 底色
                       //     boxShadow: const [
                       //       BoxShadow(
                       //         blurRadius: 4, //阴影范围
                       //         spreadRadius: 0.1, //阴影浓度
                       //         color: Colors.grey, //阴影颜色
                       //       ),
                       //     ],
                       //   ),
                       //   child:IconButton(
                       //     padding:const EdgeInsetsDirectional.all(0),
                       //     onPressed: (){
                       //       if(_selectedIndex<indexData[index]["images"].length-1) {
                       //         _selectedIndex++;
                       //         _pageController.jumpToPage(_selectedIndex);
                       //       }
                       //       setState(() {
                       //       });
                       //     },
                       //     icon: Icon(
                       //       Icons.arrow_forward_ios,
                       //       color:_selectedIndex<indexData[index]["images"].length-1?const Color.fromRGBO(20, 178, 181, 1):Colors.grey,
                       //       size: 22,
                       //     ),
                       //   ) ,
                       //
                       // ),

                     ],
                   ),
                 ),),
                Center(
                  child: SizedBox(
                    height: 18,
                    width: 20.0*indexData[index]["images"].length,
                    child: ListView.builder(
                        itemCount: indexData[index]["images"].length,
                        scrollDirection: Axis.horizontal,
                        itemBuilder: (context, index) {
                          return Container(
                            width: 8,
                            margin:const EdgeInsetsDirectional.all(5),
                            decoration: BoxDecoration(
                              color:_selectedIndex==index?const Color.fromRGBO(20, 178, 181, 1):const Color.fromRGBO(180, 194, 194, 1),
                              borderRadius:const BorderRadiusDirectional.all(Radius.circular(20)),
                              // border: Border.all(color:(_selectedIndex%3)==index?Colors.red:const Color.fromRGBO(180, 194, 194, 1),width: 3),

                            ),
                          );
                        }),
                  ),
                ),
                // if(_selectedIndex<indexData[index]["images"].length-indexData[index]["images"].length%3)
                //   Center(
                //     child: SizedBox(
                //       height: 18,
                //       width: 60,
                //       child: ListView.builder(
                //           itemCount: 3,
                //           scrollDirection: Axis.horizontal,
                //           itemBuilder: (context, index) {
                //             return Container(
                //               width: 8,
                //               margin:const EdgeInsetsDirectional.all(5),
                //               decoration: BoxDecoration(
                //                 color:(_selectedIndex%3)==index?const Color.fromRGBO(20, 178, 181, 1):const Color.fromRGBO(180, 194, 194, 1),
                //                 borderRadius:const BorderRadiusDirectional.all(Radius.circular(20)),
                //                 // border: Border.all(color:(_selectedIndex%3)==index?Colors.red:const Color.fromRGBO(180, 194, 194, 1),width: 3),
                //
                //               ),
                //             );
                //
                //           }),
                //     ),
                //   ),
                // if(_selectedIndex>=indexData[index]["images"].length-indexData[index]["images"].length%3)
                //   Center(
                //     child: SizedBox(
                //       height: 18,
                //       width: 60,
                //       child: ListView.builder(
                //           itemCount: indexData[index]["images"].length%3,
                //           scrollDirection: Axis.horizontal,
                //           itemBuilder: (context, index) {
                //             return Container(
                //               width: 8,
                //               margin:const EdgeInsetsDirectional.all(5),
                //               decoration: BoxDecoration(
                //                 color:(_selectedIndex%3)==index?const Color.fromRGBO(20, 178, 181, 1):const Color.fromRGBO(180, 194, 194, 1),
                //                 borderRadius:const BorderRadiusDirectional.all(Radius.circular(20)),
                //                 // border: Border.all(color:(_selectedIndex%3)==index?Colors.red:const Color.fromRGBO(180, 194, 194, 1),width: 3),
                //
                //               ),
                //             );
                //
                //           }),
                //     ),
                //   ),


              ],
            )
        ),
      ),
    );
  }


  @override
  Widget build(BuildContext context) {
    return  Scaffold(
      resizeToAvoidBottomInset:false,
      appBar: AppBar(
        backgroundColor: Colors.white,
        title:const Text('线路详情',style: TextStyle(color: Colors.black),),
        centerTitle:true,
        leading:  IconButton(
          color: Colors.black,
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
        elevation: 0,
      ),
      body:SingleChildScrollView(
        child:  Center(
            child: SizedBox(
              width: MediaQuery.of(context).size.width*0.92,
              child: Column(
                children:[
                  if(lineData!=null)shuttleRoute(),
                  if(lineData!=null)siteDetails(dq),
                ],
              ),
            )
        ),
      ),





    );
  }
}



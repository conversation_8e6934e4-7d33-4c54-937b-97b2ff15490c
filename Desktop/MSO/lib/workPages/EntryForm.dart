import 'dart:io';
import 'package:amkor/services/dto/uploadCovidDto.dart';
import 'package:amkor/utils/addj.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:image_picker/image_picker.dart';
import 'package:amkor/components/tabbar_v2.dart';
import 'package:amkor/utils/colors.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';

class EntryFormPage extends StatefulWidget {
  const EntryFormPage({Key? key}) : super(key: key);

  @override
  State<EntryFormPage> createState() => _EntryFormPagePageState();
}

class _EntryFormPagePageState extends State<EntryFormPage> {
  //身份证号
  TextEditingController idNumber = TextEditingController();
  //户籍地址
  TextEditingController registeredResidence = TextEditingController();
  bool _isLoading = false;
  //工资卡账户
  TextEditingController payrollCardAccount = TextEditingController();
  //紧急联系人
  TextEditingController emergencyContact = TextEditingController();
  //与联系人关系
  TextEditingController relationshipContacts = TextEditingController();

  //身份证号
  String idNumbers = "";
  //户籍地址
  String registeredResidences = "";
  //工资卡账户
  String payrollCardAccounts = "";
  //紧急联系人
  String emergencyContacts = "";
  //与联系人关系
  String relationshipContactss = "";

  ///
  //FocusNode
  FocusNode jkNode = new FocusNode();

  GlobalKey _formKey = GlobalKey<FormState>();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: TabberV2(
          title: "内部推荐".tr,
          showBack: true,
        ),
        resizeToAvoidBottomInset: false,
        body: SingleChildScrollView(
            child: Center(
          child: _buildBody(),
        )));
  }

  @override
  void reassemble() {
    // TODO: implement reassemble
    super.reassemble();
  }

  final d = DateTime.now();
  bool int1 = true;

  int int_ = 0;
  final List<List<String>> _sportValueData = [];
  final ImagePicker _imagePicker = ImagePicker();

  ///传路径
  final formdata = UploadCovidInputDto();

  ///
  ///
  ///
  ///
  String filePath = "assets/icons/add_pic.png";
  @override
  void initState() {
    // TODO: implement activate
    super.initState();
    for (int i = 0; i < Global().homeData1.length; i++) {
      _sportValueData.add([
        '',
        '',
        '',
        '',
        '',
        '',
      ]);
    }
  }

  ///表按钮
  Widget _Radio(List<Map<String, dynamic>> reportData) {
    return Column(
      children: [
        for (int i = 0; i < reportData.length; i++)
          Padding(
              padding: const EdgeInsets.only(bottom: 30),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.only(bottom: 10),
                    child: Text(Global().homeData1[i]['题目']),
                  ),
                  Container(
                    decoration: const BoxDecoration(
                      border: Border(
                          top: BorderSide(color: Colors.black12, width: 1),
                          left: BorderSide(color: Colors.black12, width: 1),
                          right: BorderSide(
                            color: Colors.black12,
                            width: 1,
                          )),
                    ),
                    child: Column(
                      children: [
                        ///单选
                        if (reportData[i]['类型'] == '单选')
                          for (int j = 0; j < reportData[i]['选项'].length; j++)
                            Container(
                              decoration: const BoxDecoration(
                                border: Border(
                                    bottom: BorderSide(
                                  color: Colors.black12,
                                  width: 1,
                                )),
                              ),
                              child: Row(
                                children: [
                                  Radio<String>(
                                    value: reportData[i]['选项'][j],
                                    groupValue: _sportValueData[i][0],
                                    onChanged: (value) {
                                      setState(() {
                                        _sportValueData[i][0] = value!;
                                      });
                                    },
                                  ),
                                  Expanded(
                                    child: Text(reportData[i]['选项'][j]),
                                  )
                                ],
                              ),
                            ),

                        ///多选
                        if (reportData[i]['类型'] == '多选')
                          for (int j = 0; j < reportData[i]['选项'].length; j++)
                            Container(
                              decoration: const BoxDecoration(
                                border: Border(
                                    bottom: BorderSide(
                                  color: Colors.black12,
                                  width: 1,
                                )),
                              ),
                              child: Row(
                                children: [
                                  Checkbox(
                                    value: _sportValueData[i][j] ==
                                        reportData[i]['选项'][j],
                                    onChanged: (value) {
                                      setState(() {
                                        _sportValueData[i][j] =
                                            _sportValueData[i][j] ==
                                                    reportData[i]['选项'][j]
                                                ? ''
                                                : reportData[i]['选项'][j];
                                      });
                                    },
                                  ),
                                  Text(reportData[i]['选项'][j]),
                                ],
                              ),
                            ),

                        ///图片
                        if (reportData[i]['类型'] == '图片')
                          Container(
                            decoration: const BoxDecoration(
                              border: Border(
                                  bottom: BorderSide(
                                color: Colors.black12,
                                width: 1,
                              )),
                            ),
                            child: Row(
                              children: [
                                InkWell(
                                  onTap: () async {
                                    final XFile? img =
                                        await _imagePicker.pickImage(
                                            source: ImageSource.gallery,
                                            maxHeight: 2400,
                                            maxWidth: 1080,
                                            imageQuality: 50);
                                    if (img == null) {
                                      return;
                                    }
                                    var imgSize = await img.length();

                                    File? compressedFile;
                                    final compressedXFile =
                                        await FlutterImageCompress
                                            .compressAndGetFile(
                                      img.path,
                                      img.path
                                          .replaceAll('.jpg', '_compressed.jpg')
                                          .replaceAll(
                                              '.png', '_compressed.png'),
                                      quality: 50,
                                      minWidth: 1080,
                                      minHeight: 1080,
                                    );
                                    if (compressedXFile != null) {
                                      compressedFile =
                                          File(compressedXFile.path);
                                      imgSize = compressedFile.lengthSync();
                                    }

                                    ///压缩后的图片限制200kb
                                    if (imgSize >= 2400000) {
                                      Fluttertoast.showToast(msg: "Tips1".tr);
                                      return;
                                    }
                                    formdata.filePath =
                                        compressedFile?.path ?? img.path;

                                    print('-=-=-=${img.path}=====${img.path}');

                                    setState(() {
                                      // filePath = compressedFile!.path;
                                      _sportValueData[i][0] =
                                          compressedFile!.path;
                                      _sportValueData[i][1] = "true";
                                    });
                                  },
                                  child: Container(
                                    width: 60,
                                    height: 60,
                                    alignment: Alignment.center,
                                    decoration: const BoxDecoration(
                                      image: DecorationImage(
                                        image: AssetImage(
                                            "assets/icons/add_pic.png"),
                                        fit: BoxFit.fill, // 完全填充
                                      ),
                                    ),
                                    child: (_sportValueData[i][1] == ''
                                            ? false
                                            : _sportValueData[i][1] == "true")
                                        ? Image.file(
                                            File(_sportValueData[i][0] == ''
                                                ? "assets/icons/add_pic.png"
                                                : _sportValueData[i][0]),
                                            height: 60,
                                            width: 60,
                                          )
                                        : Image(
                                            image: AssetImage(
                                                _sportValueData[i][0] == ''
                                                    ? "assets/icons/add_pic.png"
                                                    : _sportValueData[i][0]),
                                            height: 60,
                                            width: 60,
                                          ),
                                  ),
                                ),
                                const Padding(
                                  padding: EdgeInsets.only(left: 20),
                                ),
                                Text(reportData[i]['选项']),
                              ],
                            ),
                          )
                      ],
                    ),
                  )
                ],
              ))
      ],
    );
  }

  Widget _buildBody() {
    return Container(
      alignment: Alignment.center,
      width: kIsWeb &&
              (defaultTargetPlatform == TargetPlatform.macOS ||
                  defaultTargetPlatform == TargetPlatform.windows)
          ? MediaQuery.of(context).size.width * 0.26
          : MediaQuery.of(context).size.width * 0.92,
      child: Column(
        children: [
          Container(
            width: MediaQuery.of(context).size.width * 0.80,
            child: const Image(
              image: AssetImage("assets/images/xygrz_bj.png"),
              fit: BoxFit.fitWidth,
            ),
          ),
          const Padding(padding: EdgeInsets.only(top: 10)),
          _buildForm(),
        ],
      ),
    );
  }

  Widget _buildForm() {
    return Form(
        key: _formKey,
        child: Container(
          padding: const EdgeInsets.all(8),
          color: Colors.white,
          child: Column(
            children: [
              const Padding(padding: EdgeInsets.only(top: 20)),
              _buildIdNumber(),
              const Padding(padding: EdgeInsets.only(top: 20)),
              _buildRegisteredResidence(),
              const Padding(padding: EdgeInsets.only(top: 20)),
              _buildPayrollCardAccount(),
              const Padding(padding: EdgeInsets.only(top: 20)),
              _buildEmergencyContact(),
              const Padding(padding: EdgeInsets.only(top: 20)),
              _buildRelationshipContacts(),
              const Padding(padding: EdgeInsets.only(top: 20)),
              _Radio(Global().homeData1),
              const Text(
                "(公司承诺将保护您的相关个人隐私信息)",
                style: TextStyle(color: Colors.grey, fontSize: 14),
              ),
              const Padding(padding: EdgeInsets.only(top: 20)),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: CustomColors.ThemeColor,
                  minimumSize: Size(double.infinity, 48),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
                onPressed: _isLoading
                    ? null
                    : () {
                        setState(() {
                          _isLoading = true;
                        });
                        _updatePwdClick();
                      },
                child: _isLoading
                    ? SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Text("提交", style: TextStyle(color: Colors.white)),
              )
            ],
          ),
        ));
  }

  Widget _buildIdNumber() {
    return TextFormField(
      autofocus: true,
      controller: idNumber,
      // inputFormatters: [FilteringTextInputFormatter.allow(RegExp("[0-9.]")),],
      onChanged: (e) {
        setState(() {
          idNumbers = e;
        });
      },

      decoration: InputDecoration(
        border: const OutlineInputBorder(
            borderSide: BorderSide(
                color: Colors.black, width: 3.0, style: BorderStyle.solid)),
        isDense: true,
        contentPadding: EdgeInsets.fromLTRB(10, 10, 10, 0),
        hintText: "请输入身份证号".tr,
        floatingLabelBehavior: FloatingLabelBehavior.auto,
        label: Text(
          "身份证号:".tr,
          style: TextStyle(color: CustomColors.ThemeColor, fontSize: 16),
        ),
        focusColor: const Color.fromRGBO(29, 64, 133, 1),
        // focusedBorder: const UnderlineInputBorder(),
        suffixIcon: Offstage(
          offstage: idNumbers == "",
        ),
      ),
      autovalidateMode: AutovalidateMode.onUserInteraction,
      validator: (v) {
        if (!v!.trim().isNotEmpty) {
          return "身份证号不能为空".tr;
        }
        return null;
      },
    );
  }

  Widget _buildRegisteredResidence() {
    return TextFormField(
      autofocus: true,
      controller: registeredResidence,
      onChanged: (e) {
        setState(() {
          registeredResidences = e;
        });
      },
      decoration: InputDecoration(
        border: const OutlineInputBorder(
            borderSide: BorderSide(
                color: Colors.black, width: 3.0, style: BorderStyle.solid)),
        isDense: true,
        contentPadding: EdgeInsets.fromLTRB(10, 10, 10, 0),
        hintText: "请填写户籍地址".tr,
        floatingLabelBehavior: FloatingLabelBehavior.auto,
        label: Text(
          "户籍地址:".tr,
          style: TextStyle(color: CustomColors.ThemeColor, fontSize: 16),
        ),
        focusColor: const Color.fromRGBO(29, 64, 133, 1),
        // focusedBorder: const UnderlineInputBorder(),
        suffixIcon: Offstage(
          offstage: registeredResidences == "",
        ),
      ),
      autovalidateMode: AutovalidateMode.onUserInteraction,
      validator: (v) {
        if (!v!.trim().isNotEmpty) {
          return "姓名不能为空".tr;
        }
        return null;
      },
    );
  }

  Widget _buildPayrollCardAccount() {
    return TextFormField(
      autofocus: true,
      controller: payrollCardAccount,
      onChanged: (e) {
        setState(() {
          payrollCardAccounts = e;
        });
      },
      decoration: InputDecoration(
        border: const OutlineInputBorder(
            borderSide: BorderSide(
                color: Colors.black, width: 3.0, style: BorderStyle.solid)),
        isDense: true,
        contentPadding: EdgeInsets.fromLTRB(10, 10, 10, 0),
        hintText: "请填写工资卡账户".tr,
        floatingLabelBehavior: FloatingLabelBehavior.auto,
        label: Text(
          "工资卡账户:".tr,
          style: TextStyle(color: CustomColors.ThemeColor, fontSize: 16),
        ),
        focusColor: const Color.fromRGBO(29, 64, 133, 1),
        // focusedBorder: const UnderlineInputBorder(),
        suffixIcon: Offstage(
          offstage: payrollCardAccounts == "",
        ),
      ),
      autovalidateMode: AutovalidateMode.onUserInteraction,
      validator: (v) {
        if (!v!.trim().isNotEmpty) {
          return "工资卡账户不能为空".tr;
        }
        return null;
      },
    );
  }

  Widget _buildEmergencyContact() {
    return TextFormField(
      autofocus: true,
      controller: emergencyContact,
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp("[0-9.()_]")),
      ],
      onChanged: (e) {
        setState(() {
          emergencyContacts = e;
        });
      },
      decoration: InputDecoration(
        border: const OutlineInputBorder(
            borderSide: BorderSide(
                color: Colors.black, width: 3.0, style: BorderStyle.solid)),
        isDense: true,
        contentPadding: EdgeInsets.fromLTRB(10, 10, 10, 0),
        hintText: "请填写紧急联系方式".tr,
        floatingLabelBehavior: FloatingLabelBehavior.auto,
        label: Text(
          "紧急联系人手机号".tr,
          style: TextStyle(color: CustomColors.ThemeColor, fontSize: 16),
        ),
        focusColor: const Color.fromRGBO(29, 64, 133, 1),
        // focusedBorder: const UnderlineInputBorder(),
        suffixIcon: Offstage(
          offstage: emergencyContacts == "",
        ),
      ),
      autovalidateMode: AutovalidateMode.onUserInteraction,
      validator: (v) {
        if (!v!.trim().isNotEmpty) {
          return "手机号不能为空".tr;
        }
        return null;
      },
    );
  }

  Widget _buildRelationshipContacts() {
    return TextFormField(
      autofocus: true,
      controller: relationshipContacts,
      onChanged: (e) {
        setState(() {
          relationshipContactss = e;
        });
      },
      decoration: InputDecoration(
        border: const OutlineInputBorder(
            borderSide: BorderSide(
                color: Colors.black, width: 3.0, style: BorderStyle.solid)),
        isDense: true,
        contentPadding: EdgeInsets.fromLTRB(10, 10, 10, 0),
        hintText: "请填写紧急联系人以及关系".tr,
        floatingLabelBehavior: FloatingLabelBehavior.auto,
        label: Text(
          "紧急联系人".tr,
          style: TextStyle(color: CustomColors.ThemeColor, fontSize: 16),
        ),
        focusColor: const Color.fromRGBO(29, 64, 133, 1),
        // focusedBorder: const UnderlineInputBorder(),
        suffixIcon: Offstage(
          offstage: relationshipContactss == "",
        ),
      ),
      autovalidateMode: AutovalidateMode.onUserInteraction,
      validator: (v) {
        if (!v!.trim().isNotEmpty) {
          return "联系人不能为空".tr;
        }
        return null;
      },
    );
  }

  void _updatePwdClick() async {
    setState(() {
      _isLoading = false;
    });
    //   if ((_formKey.currentState as FormState).validate() == false) {
    //     setState(() {
    //       _isLoading = false;
    //     });
    //     return;
    //   }
    //
    //   var job = _JobController.text;
    //   var telephone = _Telephonetroller.text;
    //   var name = _nameController.text;
    //   var department = _departmentController.text;
    //
    //   ///忘记密码
    //   ///
    //   ///
    //   ///
    //   ///
    //   ///
    //   var resp = await AuthService().ResetPassword(job,telephone);
    //   print("===================================$resp");
    //
    //   _btnCtrl.stop();
    //
    //
    //   if (resp == null) {
    //     Fluttertoast.showToast(msg: "Tips6".tr);
    //     return;
    //   }
    //   if(resp.message=="") {
    //     Fluttertoast.showToast(msg: "text6_12".tr);
    //   }else
    //     Fluttertoast.showToast(msg: resp.message);
    //   if (resp.success) _back();
    // }
    //
    // void _back() {
    //   ///重制密码成功后回到登陆界面重新登陆
    //   AuthService().GetVerificationCode();
    //   Navigator.pop(context);
  }

  @override
  void dispose() {
    idNumber.dispose();
    registeredResidence.dispose();
    payrollCardAccount.dispose();
    emergencyContact.dispose();
    relationshipContacts.dispose();
    // WidgetsBinding.instance.removeObserver(this);

    super.dispose();
  }
}



import 'package:amkor/components/tabbar_v2.dart';
import 'package:amkor/model/MyBinding.dart';
import 'package:amkor/services/employeeService.dart';
import 'package:amkor/utils/Adapt.dart';
import 'package:amkor/utils/TreeView.dart';
import 'package:amkor/utils/verticalText.dart';
import 'package:amkor/workPages/ShuttleBusDetails.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';



class ShuttleBus extends StatelessWidget {
   ShuttleBus({Key? key}) : super(key: key);

   final TextEditingController search =TextEditingController();
   final ShuttleBusData controller =Get.put(ShuttleBusData());
   final _controller=ScrollController();
  @override
  Widget build(BuildContext context) {
    double w=MediaQuery.of(context).size.width;
    double h=MediaQuery.of(context).size.height;
    return  WillPopScope(
        onWillPop: () async {
          Get.delete<ShuttleBusData>();
      // 在这里处理用户点击返回按键的操作，如果返回true则关闭当前页面，返回false则不关闭页面
      return true;
    },
    child:Scaffold(
        resizeToAvoidBottomInset:false,
        appBar:AppBar(
          backgroundColor: Colors.white,
          title:const Text('班车线路',style: TextStyle(color: Colors.black),),
          centerTitle:true,
          leading:  IconButton(
            color: Colors.black,
            icon: const Icon(Icons.arrow_back_ios),
            onPressed: (){
              Get.delete<ShuttleBusData>();
              Navigator.of(context).pop();
            },
          ),
          elevation: 0,
        ),
        body:Padding(
          padding: EdgeInsetsDirectional.only(start:w*0.04,end: w*0.04 ),
          child: Column(
            children: [
              AppBarSearch(
                controller: search,
                hintText: "请输入线路号/线路名称/站点名称",
                backgroundColor:Colors.white,
                // focusNode: ,
                onClear: (){
                  search.clear();
                  controller.initData(search.text,controller.busType.value);
                },
                onRightTap: (){
                  controller.initData(search.text,controller.busType.value);
                  FocusScope.of(context).unfocus();
                },
                onSearch: (value){
                  controller.initData(value,controller.busType.value);
                  FocusScope.of(context).unfocus();
                },
                leadings:true,
              ),
              Obx(() => Row(
                children: [
                  GestureDetector(
                    onTap: (){
                      controller.busType.value="";
                      _controller.jumpTo(0);
                      controller.initData(search.text,controller.busType.value);
                    },
                    child: Container(
                      margin:const EdgeInsetsDirectional.only(end: 10),
                      decoration:controller.busType.value==""?const BoxDecoration(
                        border: Border(
                          bottom: BorderSide(width: 2, color: Colors.black),
                        ),
                      ):null,
                      child: Text(
                        "  全部  ",
                        style: TextStyle(fontWeight:controller.busType.value==""?FontWeight.w600:FontWeight.w300),
                      ),
                    ) ,
                  ),
                  GestureDetector(
                    onTap: (){
                      controller.busType.value="dayShift";
                      // 滚动到初始位置
                      _controller.jumpTo(0);
                      controller.initData(search.text,controller.busType.value);
                    },
                    child: Container(
                      margin:const EdgeInsetsDirectional.only(end: 10),
                      decoration:controller.busType.value=="dayShift"?const BoxDecoration(
                        border: Border(
                          bottom: BorderSide(width: 2, color: Colors.black),
                        ),
                      ):null,
                      child:Text(
                        "  常日班  ",
                        style: TextStyle(fontWeight:controller.busType.value=="dayShift"?FontWeight.w600:FontWeight.w300),
                      ),
                    ) ,
                  ),
                  GestureDetector(
                    onTap: (){
                      controller.busType.value="other";
                      _controller.jumpTo(0);
                      controller.initData(search.text,controller.busType.value);
                    },
                    child: Container(
                      margin:const EdgeInsetsDirectional.only(end: 10),
                      decoration:controller.busType.value=="other"?const BoxDecoration(
                        border: Border(
                          bottom: BorderSide(width: 2, color: Colors.black),
                        ),
                      ):null,
                      child: Text(
                        "  翻班  ",
                        style: TextStyle(fontWeight:controller.busType.value=="other"?FontWeight.w600:FontWeight.w300),
                      ),
                    ) ,
                  )

                ],
              )),
              const Padding(padding: EdgeInsetsDirectional.all(2)),
              Obx(() => controller.loading.value?const LoadingWidget():const SizedBox()),
              const Padding(padding: EdgeInsetsDirectional.all(4)),
              RefreshIndicator(
                onRefresh: (){
                  return controller.initData(search.text,controller.busType.value);
                },
                child: SizedBox(
                    height: h*0.74,
                    child:GetBuilder<ShuttleBusData>(
                      // init: ShuttleBusData(),
                      builder: (controller){
                        return CustomScrollView(
                          controller:_controller,
                          slivers: [
                            SliverList(
                              delegate: SliverChildBuilderDelegate(
                                    (context, index) {
                                  return Padding(
                                    padding:const EdgeInsetsDirectional.only(bottom: 20),
                                    child: Card(
                                      color: Colors.white, // 背景色
                                      shadowColor: Colors.grey, // 阴影颜色
                                      elevation: 2, // 阴影高度
                                      borderOnForeground: false, // 是否在 child 前绘制 border，默认为 true
                                      margin: const EdgeInsets.fromLTRB(0, 0, 0, 0), // 外边距
                                      // 边框
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                        // side: const BorderSide(
                                        //   color: Colors.grey,
                                        //   width: 1,
                                        // ),
                                      ),

                                      child: Padding(
                                          padding:const EdgeInsetsDirectional.all(14),
                                          child:Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Row(
                                                children: [
                                                  Container(
                                                      height: 30,
                                                      width: 30,
                                                      margin:const EdgeInsetsDirectional.only(start: 4,end: 4),
                                                      alignment: Alignment.center,
                                                      decoration: const BoxDecoration(
                                                          shape: BoxShape.circle,
                                                          gradient: LinearGradient(
                                                              begin: Alignment.topCenter,
                                                              end: Alignment.bottomCenter,
                                                              colors: [
                                                                Color.fromRGBO(66, 173, 245, 1),
                                                                Color.fromRGBO(75, 197, 250, 1),
                                                              ]
                                                          )
                                                      ),
                                                      child: Text(controller.data[index]["busNo"],style:const TextStyle(color: Colors.white,fontSize: 14,),)
                                                  ),
                                                  Container(
                                                    alignment: Alignment.center,
                                                    child: Text(controller.data[index]["lineName"],
                                                      style:const TextStyle(color: Color.fromRGBO(20, 178, 181, 1),fontSize: 14,),
                                                      maxLines: 2, // 最大行数为 2
                                                      overflow: TextOverflow.ellipsis, // 超出部分以省略号形式显示
                                                    ),
                                                  ),
                                                  Expanded(
                                                    child: GestureDetector(
                                                      onTap: (){
                                                        Navigator.of(context).push(MaterialPageRoute(
                                                          builder: (context) => ShuttleBusDetailsPage(id: controller.data[index]["lineId"],line: controller.line[index],),
                                                        ));
                                                      },
                                                      child:Container(
                                                        padding:const EdgeInsetsDirectional.all(2),
                                                        alignment: Alignment.centerRight,
                                                        child:const Text("线路详情 >",style: TextStyle(fontSize:10,color: Colors.grey),),
                                                      ),
                                                    ),
                                                  )
                                                ],
                                              ),
                                              Padding(
                                                padding:const EdgeInsetsDirectional.only(start: 34),
                                                child: Row(
                                                  children: [
                                                    Expanded(
                                                      flex: 2,
                                                      child: Container(
                                                        padding:const EdgeInsetsDirectional.only(start: 2,end: 2),
                                                        alignment: Alignment.center,
                                                        child: Text(controller.line[index]?controller.data[index]["upLine"][0]["stationName"]:controller.data[index]["downLine"][0]["stationName"],
                                                          style:const TextStyle(color: Colors.black,fontSize: 12,),
                                                          maxLines: 2, // 最大行数为 2
                                                          overflow: TextOverflow.ellipsis, // 超出部分以省略号形式显示
                                                        ),
                                                      ),
                                                    ),
                                                    GestureDetector(
                                                      onTap: (){
                                                        controller.line[index]=!controller.line[index];
                                                        controller.update();
                                                        print("--$index------${controller.line[index]}-----------${controller.indexData}-----------");
                                                      },
                                                      child: Padding(
                                                        padding:const EdgeInsetsDirectional.only(start: 4,end: 4),
                                                        child: Image(
                                                          image:const AssetImage("assets/icons/exchange.png"),
                                                          height: Adapt.widthPt(24),
                                                          width: Adapt.widthPt(24),
                                                        ),
                                                      ),
                                                    ),
                                                    Expanded(
                                                      flex: 2,
                                                      child: Container(
                                                        padding:const EdgeInsetsDirectional.only(start: 2,end: 2),
                                                        alignment: Alignment.center,
                                                        child: Text(controller.line[index]?controller.data[index]["upLine"][controller.data[index]["upLine"].length-1]["stationName"]: controller.data[index]["downLine"][controller.data[index]["downLine"].length-1]["stationName"],
                                                          style:const TextStyle(color: Colors.black,fontSize: 12,),
                                                          maxLines: 2, // 最大行数为 2
                                                          overflow: TextOverflow.ellipsis, // 超出部分以省略号形式显示
                                                        ),
                                                      ),
                                                    ),
                                                    const Padding(padding:EdgeInsetsDirectional.only(start: 12)),
                                                    if(controller.data[index]["isUpdate"]) SizedBox(
                                                      width:60 ,
                                                      child:Container(
                                                          padding:const EdgeInsetsDirectional.all(2),
                                                          alignment: Alignment.center,
                                                          decoration: BoxDecoration(
                                                              color: Colors.amber[50],
                                                              borderRadius:const BorderRadiusDirectional.all(Radius.circular(2)),
                                                              border: Border.all(color: const Color.fromRGBO(255, 169, 106, 1),)
                                                          ),
                                                          child:const Text("线路已更新",style: TextStyle(fontSize:10,color: Color.fromRGBO(255, 169, 106, 1),),)
                                                      ),

                                                    ),
                                                  ],
                                                ),
                                              ),
                                              Container(
                                                padding:const EdgeInsetsDirectional.only(start: 36,top: 10,bottom: 10),
                                                child: Column(
                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                  children: [
                                                    Text.rich(TextSpan(
                                                        children: [
                                                          const TextSpan(
                                                              text: "班次:  ",
                                                              style: TextStyle(color: Colors.grey,fontSize: 12,)),
                                                          TextSpan(
                                                              text: controller.data[index]["busTypeName"],
                                                              style: const TextStyle(color: Colors.black,fontSize: 12,)),
                                                        ]
                                                    )),
                                                    const Padding(padding: EdgeInsetsDirectional.all(6)),
                                                    // GestureDetector(
                                                    //   onTap: (){
                                                    //     controller.tj[index]=!controller.tj[index];
                                                    //     controller.update();
                                                    //   },
                                                    //   child:Text.rich(
                                                    //       overflow:controller.tj[index]?TextOverflow.ellipsis:null,
                                                    //       maxLines:controller.tj[index]?3:null,
                                                    //       TextSpan(
                                                    //           children: [
                                                    //             const TextSpan(
                                                    //                 text: "途径:  ",
                                                    //                 style: TextStyle(color: Colors.grey,fontSize: 12,)),
                                                    //             TextSpan(
                                                    //                 text: controller.data[index]["way"] ??"",
                                                    //                 style: const TextStyle(color: Colors.black,fontSize: 12,)),
                                                    //           ]
                                                    //       )
                                                    //   ),
                                                    // ),
                                                    // const Padding(padding: EdgeInsetsDirectional.all(6)),
                                                    // Text.rich(
                                                    //     TextSpan(
                                                    //         children: [
                                                    //           const TextSpan(
                                                    //               text: "备注:  ",
                                                    //               style: TextStyle(color: Colors.grey,fontSize: 12,)),
                                                    //           TextSpan(
                                                    //               text: controller.data[index]["remark"]??'',
                                                    //               style: const TextStyle(color: Colors.black,fontSize: 12,)),
                                                    //
                                                    //         ]
                                                    //     )),
                                                  ],
                                                ) ,
                                              ),
                                              const Divider(
                                                height: 2,
                                                color: Color.fromRGBO(223, 229, 230, 1),
                                              ),
                                              const Padding(padding: EdgeInsetsDirectional.all(4)),
                                              SizedBox(
                                                  height: 200,
                                                  child:ListView(
                                                    scrollDirection: Axis.horizontal,
                                                    children: [
                                                      Stack(
                                                        children: [
                                                          Container(
                                                            height: 4,
                                                            width: (controller.line[index]?controller.data[index]["upLine"].length-1:controller.data[index]["downLine"].length-1)*64.0,
                                                            margin:  EdgeInsetsDirectional.only(start: 32,top: controller.data[index]["busType"]=="other"?60:44,end: 0),
                                                            decoration: BoxDecoration(
                                                              border: Border.all(color:const Color.fromRGBO(180, 194, 194, 1),width: 2),

                                                            ),
                                                          ),
                                                          ListView.builder(
                                                              itemCount:controller.line[index]?controller.data[index]["upLine"].length:controller.data[index]["downLine"].length,
                                                              scrollDirection: Axis.horizontal,
                                                              shrinkWrap: true,
                                                              physics:const NeverScrollableScrollPhysics(),
                                                              // controller: controller,
                                                              itemBuilder: (context, _index) {
                                                                return GestureDetector(
                                                                    onTap: (){
                                                                      Navigator.of(context).push(MaterialPageRoute(
                                                                        builder: (context) => ShuttleBusDetailsPage(id: controller.data[index]["lineId"],line: controller.line[index],),
                                                                      ));
                                                                    },
                                                                    child:
                                                                    SizedBox(
                                                                      width: 64,
                                                                      child: Column(
                                                                        crossAxisAlignment: CrossAxisAlignment.center,
                                                                        mainAxisAlignment: MainAxisAlignment.start,
                                                                        children: [
                                                                          if(_index==0)
                                                                            Container(
                                                                              width: 18,
                                                                              height: 18,
                                                                              alignment: Alignment.center,
                                                                              decoration: BoxDecoration(
                                                                                color:const Color.fromRGBO(255, 141, 26, 0.2),
                                                                                borderRadius:const BorderRadiusDirectional.all(Radius.circular(2)),
                                                                                border: Border.all(color:const Color.fromRGBO(255, 141, 26, 1),width: 1),

                                                                              ),
                                                                              child:const Text('始',style: TextStyle(color: Color.fromRGBO(255, 141, 26, 1),fontSize: 12,),),
                                                                            ),
                                                                          if(_index==(controller.line[index]?controller.data[index]["upLine"].length:controller.data[index]["downLine"].length)-1)
                                                                            Container(
                                                                              width: 18,
                                                                              height: 18,
                                                                              alignment: Alignment.center,
                                                                              decoration: BoxDecoration(
                                                                                color:const Color.fromRGBO(20, 178, 181, 0.2),
                                                                                borderRadius:const BorderRadiusDirectional.all(Radius.circular(2)),
                                                                                border: Border.all(color:const Color.fromRGBO(20, 178, 181, 1),width: 1),

                                                                              ),
                                                                              child:const Text('终',style: TextStyle(color: Color.fromRGBO(20, 178, 181, 1),fontSize: 12,),),
                                                                            ),
                                                                          if(_index!=0&&_index!=(controller.line[index]?controller.data[index]["upLine"].length:controller.data[index]["downLine"].length)-1)
                                                                            const SizedBox(
                                                                              width: 18,
                                                                              height: 18,),
                                                                          Text(controller.line[index]?controller.data[index]["upLine"][_index]["arrivedTime"]??"":controller.data[index]["downLine"][_index]["arrivedTime"]??"",
                                                                            style: TextStyle(color:_index!=0&&_index!=(controller.line[index]?controller.data[index]["upLine"].length:controller.data[index]["downLine"].length)-1?Colors.black:const Color.fromRGBO(20, 178, 181, 1),fontSize: 14,),),
                                                                          if(controller.data[index]["busType"]=="other")Text(controller.line[index]?controller.data[index]["upLine"][_index]["arrivedTime2"]??"":controller.data[index]["downLine"][_index]["arrivedTime2"]??"",
                                                                            style: TextStyle(color:_index!=0&&_index!=(controller.line[index]?controller.data[index]["upLine"].length:controller.data[index]["downLine"].length)-1?Colors.black:const Color.fromRGBO(20, 178, 181, 1),fontSize: 14,),),
                                                                          Container(
                                                                            width: 18,
                                                                            height: 18,
                                                                            margin:const EdgeInsetsDirectional.only(start:16,top: 3,end: 16,bottom: 6),
                                                                            decoration: BoxDecoration(
                                                                              color:const Color.fromRGBO(242, 245, 245, 1),
                                                                              borderRadius:const BorderRadiusDirectional.all(Radius.circular(20)),
                                                                              border: Border.all(color:const Color.fromRGBO(180, 194, 194, 1),width: 3),

                                                                            ),
                                                                          ),
                                                                          VerticalText(
                                                                            key: UniqueKey(),
                                                                            text:(controller.line[index]?controller.data[index]["upLine"][_index]["stationName"]:controller.data[index]["downLine"][_index]["stationName"]).replaceAllMapped(RegExp(r"([()（）])"), (match) => match.group(0) == '(' || match.group(0) == '（' ? '︵' : '︶'),
                                                                            wrapLength:7,
                                                                            color:_index!=0&&_index!=(controller.line[index]?controller.data[index]["upLine"].length:controller.data[index]["downLine"].length)-1?Colors.black:const Color.fromRGBO(20, 178, 181, 1),
                                                                            fontSize: 14,
                                                                            height: 1.1,)
                                                                        ],
                                                                      ),
                                                                    )


                                                                );

                                                              })
                                                        ],
                                                      ),
                                                    ],
                                                  )
                                              ),

                                            ],
                                          )
                                      ),
                                    ),
                                  );
                                },
                                childCount: controller.data.length,
                              ),
                            ),

                          ],
                        );



                      },
                    )
                ),
              )
            ],
          ),
        )



    ) ,

    );

  }

}



///web报表

import 'dart:io' as io;
import 'dart:html' if (dart.library.io) 'dart:io';
// import "package:universal_html/html.dart";
import 'package:amkor/components/tabbar.dart';
import 'package:amkor/utils/adapt.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
// import 'package:flutter_webview_pro/webview_flutter.dart';
import 'dart:ui' as ui;

import 'package:webview_flutter/webview_flutter.dart';

// import 'package:universal_ui/universal_ui.dart' as ui;
class WebviewPage extends StatefulWidget {
  final String title;
  final String url;
  const WebviewPage(
      {Key? key, this.title = "", this.url = "https://www.baidu.com"})
      : super(key: key);

  @override
  State<WebviewPage> createState() => _WebviewPageState();
}

class _WebviewPageState extends State<WebviewPage> {
  late final WebViewController _controller;
  bool _isLoading = false;
  double _progress = 0.0;

  @override
  void initState() {
    super.initState();
    if (!kIsWeb) {
      Adapt.setAndroid();
      // 初始化WebViewController
      _controller = WebViewController()
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..setNavigationDelegate(
          NavigationDelegate(
            onProgress: (int progress) {
              setState(() {
                _progress = progress.toDouble();
              });
            },
            onPageStarted: (String url) {
              setState(() {
                _isLoading = true;
                _progress = 0.0;
              });
            },
            onPageFinished: (String url) {
              // 延迟停止加载状态，确保页面完全渲染
              Future.delayed(const Duration(milliseconds: 500), () {
                if (mounted) {
                  setState(() {
                    _isLoading = false;
                    _progress = 100.0;
                  });
                }
              });
            },
          ),
        )
        ..loadRequest(Uri.parse(widget.url));
    }
  }

  ///web报表链接
  Widget getWebView() {
//     final IFrameElement _iframeElement = IFrameElement();
//     _iframeElement.src = url;
//     _iframeElement.style.border = 'none';
// // ignore: undefined_prefixed_name
//     ui.platformViewRegistry.registerViewFactory(
//       'iframeElement',
//           (int viewId) => _iframeElement,
//     );
    Widget _iframeWidget;
    _iframeWidget = HtmlElementView(
      key: UniqueKey(),
      viewType: 'iframeElement',
    );
    return Stack(
      children: <Widget>[
        IgnorePointer(
          ignoring: true,
          child: Center(
            child: _iframeWidget,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    print('----->');
    print(widget.url);
    print('----->');
    return PopScope(
        canPop: false,
        onPopInvokedWithResult: (bool didPop, dynamic result) async {
          if (!didPop && !kIsWeb) {
            if (await _controller.canGoBack()) {
              _controller.goBack();
            } else {
              Navigator.of(context).pop();
            }
          }
        },
        child: Scaffold(
          resizeToAvoidBottomInset: false,
          body: Column(
            children: [
              Tabbar(
                title: widget.title,
                showBack: false,
                fontColor: Colors.black,
                leftBtn: Align(
                  alignment: Alignment.centerLeft,
                  child: IconButton(
                    onPressed: () async {
                      if (!kIsWeb && await _controller.canGoBack()) {
                        _controller.goBack();
                      } else {
                        Navigator.pop(context);
                      }
                    },
                    icon: const Icon(
                      Icons.arrow_back_ios,
                      color: Colors.black,
                      size: 22,
                    ),
                  ),
                ),
              ),
              // 进度条
              if (_isLoading)
                LinearProgressIndicator(
                  value: _progress > 0 ? _progress / 100 : null,
                  backgroundColor: Colors.grey[300],
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                ),
              SizedBox(
                height: MediaQuery.of(context).size.height -
                    Adapt.widthPt(60) -
                    MediaQuery.of(context).padding.top -
                    (_isLoading ? 4 : 0), // 为进度条预留空间
                child: !kIsWeb
                    ? WebViewWidget(controller: _controller)
                    : getWebView(),
              ),
            ],
          ),
        ));
  }
}

import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:amkor/components/tabbar.dart';
import 'package:amkor/login.dart';
import 'package:amkor/services/authService.dart';
import 'package:amkor/services/employeeService.dart';
import 'package:amkor/utils/adapt.dart';
import 'package:amkor/utils/crypt.dart';
import 'package:amkor/utils/reg.dart';
// import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';

import '../components/tabbar_v2.dart';
import '../utils/colors.dart';

class ForgetPassword extends StatefulWidget {
  ForgetPassword({Key? key}) : super(key: key);

  @override
  _ForgetPasswordState createState() => _ForgetPasswordState();
}

class _ForgetPasswordState extends State<ForgetPassword> {
  //工号
  TextEditingController _JobController = TextEditingController();

  //手机号
  TextEditingController _Telephonetroller = TextEditingController();
  bool _isLoading = false;
  //工号
  String _Job = "";

  //手机号
  String _Telephone = "";

  ///
  //FocusNode
  FocusNode jkNode = new FocusNode();

  GlobalKey _formKey = GlobalKey<FormState>();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: TabberV2(
          title: "forget password".tr,
          // title: "忘记密码?".tr,
          showBack: true,
        ),
        resizeToAvoidBottomInset: false,
        body: Center(
          child: _buildBody(),
        ));
  }

  @override
  void reassemble() {
    // TODO: implement reassemble
    super.reassemble();
  }

  final d = DateTime.now();
  bool int1 = true;

  int int_ = 0;
  void initState() {
    // WidgetsBinding.instance.addObserver(this);
  }

  Widget _buildBody() {
    FocusNode focusNode = FocusNode();
    return Container(
      alignment: Alignment.center,
      width: kIsWeb &&
              (defaultTargetPlatform == TargetPlatform.macOS ||
                  defaultTargetPlatform == TargetPlatform.windows)
          ? MediaQuery.of(context).size.width * 0.26
          : null,
      padding: const EdgeInsets.fromLTRB(25, 10, 25, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ///旧密码，新密码修改,验证码
          _buildForm(),

          const Padding(padding: EdgeInsets.only(top: 30)),

          Container(
            width: MediaQuery.of(context).size.width,
            padding: EdgeInsets.only(top: Adapt.heightPt(30)),
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: CustomColors.ThemeColor,
                minimumSize: Size(MediaQuery.of(context).size.width, 48),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
              onPressed: _isLoading
                  ? null
                  : () {
                      setState(() {
                        _isLoading = true;
                      });
                      _updatePwdClick();
                    },
              child: _isLoading
                  ? SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text("Button3".tr, style: TextStyle(color: Colors.white)),
            ),
          )
        ],
      ),
    );
  }

  Widget _buildForm() {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          const Padding(padding: EdgeInsets.only(top: 20)),
          _buildJob(),
          const Padding(padding: EdgeInsets.only(top: 20)),
          _buildTelephone(),
          const Padding(padding: EdgeInsets.only(top: 20)),
        ],
      ),
    );
  }

  Widget _buildJob() {
    return TextFormField(
      autofocus: true,
      controller: _JobController,
      // inputFormatters: [FilteringTextInputFormatter.allow(RegExp("[0-9.]")),],
      onChanged: (e) {
        setState(() {
          _Job = e;
        });
      },

      decoration: InputDecoration(
        hintText: 'Job number Tips'.tr,
        // hintText: "请输入工号".tr,
        floatingLabelBehavior: FloatingLabelBehavior.auto,
        label: Text(
          'Job number'.tr,
          // "工号:".tr,
          style: TextStyle(color: CustomColors.ThemeColor),
        ),
        focusColor: const Color.fromRGBO(29, 64, 133, 1),
        focusedBorder: const UnderlineInputBorder(),
        suffixIcon: Offstage(
          offstage: _Job == "",
        ),
      ),
      autovalidateMode: AutovalidateMode.onUserInteraction,
      validator: (v) {
        if (!v!.trim().isNotEmpty) {
          return 'Job number Tips1'.tr;
          // return "工号不能为空".tr;
        }
        return null;
      },
    );
  }

  Widget _buildTelephone() {
    return TextFormField(
      autofocus: true,
      controller: _Telephonetroller,
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp("[0-9.()_]")),
      ],
      onChanged: (e) {
        setState(() {
          _Telephone = e;
        });
      },
      decoration: InputDecoration(
        hintText: 'phone number Tips'.tr,
        // hintText: "请输入手机号".tr,
        floatingLabelBehavior: FloatingLabelBehavior.auto,
        label: Text(
          "phone number".tr,
          // "手机号".tr,
          style: TextStyle(color: CustomColors.ThemeColor),
        ),
        focusColor: const Color.fromRGBO(29, 64, 133, 1),
        focusedBorder: const UnderlineInputBorder(),
        suffixIcon: Offstage(
          offstage: _Telephone == "",
        ),
      ),
      autovalidateMode: AutovalidateMode.onUserInteraction,
      validator: (v) {
        if (!v!.trim().isNotEmpty) {
          return "phone number Tips1".tr;
          // return "手机号不能为空".tr;
        }
        return null;
      },
    );
  }

  void _updatePwdClick() async {
    if ((_formKey.currentState as FormState).validate() == false) {
      setState(() {
        _isLoading = false;
      });
      return;
    }

    var Job = _JobController.text;
    var Telephone = _Telephonetroller.text;

    ///忘记密码
    var resp = await AuthService().ResetPassword(Job, Telephone);
    print("===================================$resp");

    setState(() {
      _isLoading = false;
    });

    if (resp == null) {
      Fluttertoast.showToast(msg: "Tips6".tr);
      return;
    }
    if (resp.message == "") {
      Fluttertoast.showToast(msg: "text6_12".tr);
    } else {
      Fluttertoast.showToast(msg: resp.message);
    }
    if (resp.success) _back();
  }

  void _back() {
    ///重制密码成功后回到登陆界面重新登陆
    // AuthService().GetVerificationCode();
    // Navigator.of(context).push(MaterialPageRoute(
    //   builder: (context) => const LoginPage(),
    // ));
    Navigator.pop(context);
  }

  @override
  void dispose() {
    _JobController.dispose();
    _Telephonetroller.dispose();
    // WidgetsBinding.instance.removeObserver(this);

    super.dispose();
  }
}

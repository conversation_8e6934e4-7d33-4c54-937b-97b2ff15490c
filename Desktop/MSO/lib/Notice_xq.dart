import 'package:amkor/Notice_xq_pv.dart';
import 'package:amkor/components/routes.dart';
import 'package:amkor/indexPages/index/index.dart';
import 'package:amkor/model/kvPair.dart';
import 'package:amkor/services/employeeService.dart';
import 'package:amkor/utils/enums.dart';
import 'package:amkor/watermark.dart';
import 'package:amkor/widget_gj/CommentInputBox.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:amkor/widgets/html_renderer.dart';
import 'package:get_storage/get_storage.dart';
import 'package:url_launcher/url_launcher.dart';

import '../utils/adapt.dart';

class NoticeXqPage extends StatefulWidget {
   NoticeXqPage({Key? key, this.id,this.direction=Direction.notice }) : super(key: key);
  String? id='';
  final Direction direction;
  @override
  State<NoticeXqPage> createState() => _NoticeXqPageState();
}

class _NoticeXqPageState extends State<NoticeXqPage> {
  OverlayEntry? overlayEntry;
  String data1='加载中...';
  dynamic r;
  String htmlData="""""";
  String html = """
   <!DOCTYPE html>
        <html>
        <head><meta name="viewport" content="width=device-width, initial-scale=1.0"></head>
          <body>
          
          
          </body>
        </html>
  
  """;

  Future<void> _data() async {
    if(widget.direction== Direction.problem) {
      r=await QuestionApp().GetAppQuestionDetail("${widget.id}");
    } else if(widget.direction==Direction.newestNotice){
      r=await AnnouncementApp().GetAppNewAnnouncementDetail("${widget.id}");
    }
    else{
      r=await AnnouncementApp().GetAnnouncementDetail("${widget.id}");
    }
    NoticeTitle.title=r!.data["title"];


    data1=r?.data["content"]??"";
    htmlData="""
   <!DOCTYPE html>
        <html>
        <head><meta name="viewport" content="width=device-width, initial-scale=1.0">
         <style>
         </style>       
        </head>
          <body>
            $data1           
          </body>
        </html>
  """;
    setState(() {
    });
  }
  GlobalKey noticeKey = GlobalKey(); // 可以获取到被截图组件状态的 GlobalKey
  @override
  void initState() {
    // TODO: implement activate
    if(!kIsWeb) {
      Adapt.setAndroid();
    }
    jtKey=noticeKey;
    //build构建的时候不能调setState
    var Gh="${GetStorage().read('employee')['employeeId']}${GetStorage().read('employee')['employeeName']}";
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      addWaterMarkter(context, Gh, 3, 12);
    });

    _data();
    super.initState();
  }
  @override
  void dispose() {
    removeWatermark();
    jtKey=null;
    NoticeTitle.title="";
    super.dispose();
  }



  ///默认水印
  void addWaterMarkter(BuildContext context, String string, int row, int column) async {
    OverlayState? overlayState = Overlay.of(context);
    overlayEntry = OverlayEntry(
        builder: (context) => WatermarkWidget(
          rowCount: row,
          columnCount: column,
          text: string,
          textStyle:
          const TextStyle(
              color: Color.fromRGBO(201, 205, 212, 0.06),
              fontSize: 14,
              overflow: TextOverflow.ellipsis,
              decoration: TextDecoration.none),
        ));
    overlayState?.insert(overlayEntry!);
  }

  /// 移除水印
  void removeWatermark() async {
    if (overlayEntry != null) {
      overlayEntry!.remove();
      overlayEntry = null;
    }
  }

  ///评论回复
  Widget questionAndAnswer(){
    return Padding(
      padding:EdgeInsets.only(right: Adapt.widthPt(12),left: Adapt.widthPt(12) ),
      child:Container(
        decoration:const BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(3)),
          color: Color.fromRGBO(242, 243, 245, 1),
        ),
        padding: EdgeInsets.only(right: Adapt.widthPt(8),left: Adapt.widthPt(8),top:Adapt.widthPt(8) ,bottom:Adapt.widthPt(0) ),
        child:ListView.builder(
            physics:const NeverScrollableScrollPhysics(),
            shrinkWrap:true,
            itemCount:r.data["questionAsk"].length,
            itemBuilder: (_, index) {
              return Column(
                children: [
                  GestureDetector(
                      onTap: (){
                        // Navigator.push(context,
                        //     PopRoute(
                        //         child: InputButtomWidget(
                        //           onEditingCompleteText: (text){
                        //             print('点击发送 ---$text');
                        //           },
                        //           id: GetStorage().read('employee')['employeeId'],
                        //         )
                        //     )
                        // );
                      },
                      child:Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                  child: Row(
                                    children: [
                                      Container(
                                        width: 36,
                                        height: 36,
                                        alignment: Alignment.topLeft,
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius: BorderRadius.circular(30),
                                          image: const DecorationImage(
                                            image: AssetImage("assets/images/tx_t.png"),
                                            fit: BoxFit.fill,
                                          ),
                                        ),
                                      ),
                                      const Padding(padding: EdgeInsets.only(right:10)),
                                      Text(r.data["questionAsk"][index]["employeeName"],
                                          style: TextStyle(color: Colors.black,fontSize:Adapt.widthPt(20),fontWeight: FontWeight.w600)),
                                    ],
                                  )
                              ),
                              Text(r.data["questionAsk"][index]["createTime"],
                                  style: TextStyle(color: Colors.grey,fontSize:Adapt.widthPt(12),)),
                            ],
                          ),
                          Padding(padding: const EdgeInsetsDirectional.only(start: 46),
                            child:Text(r.data["questionAsk"][index]["title"],
                                style: TextStyle(color: Colors.black,fontSize:Adapt.widthPt(14))),),
                          const Divider(
                            color: Color.fromRGBO(217, 217, 217, 1),
                          ),
                        ],
                      )
                  ),
                  if( r.data["questionAsk"][index]["content"]!=null)
                  ListView.builder(
                      physics:const NeverScrollableScrollPhysics(),
                      shrinkWrap:true,
                      itemCount:1,
                      itemBuilder: (_, indexs) {
                        return GestureDetector(
                            onTap: (){
                              // Navigator.push(context,
                              //     PopRoute(
                              //         child: InputButtomWidget(
                              //           onEditingCompleteText: (text){
                              //             print('点击发送 ---$text');
                              //           },
                              //           id: GetStorage().read('employee')['employeeId'],
                              //         )
                              //     )
                              // );
                            },
                            child: Padding(padding: const EdgeInsetsDirectional.only(start: 46),
                              child:Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Expanded(
                                          child: Row(
                                            children: [
                                              Container(
                                                width: 18,
                                                height: 18,
                                                alignment: Alignment.topLeft,
                                                decoration: BoxDecoration(
                                                  color: Colors.white,
                                                  borderRadius: BorderRadius.circular(30),
                                                  image: const DecorationImage(
                                                    image: AssetImage("assets/images/tx_t.png"),
                                                    fit: BoxFit.fill,
                                                  ),
                                                ),
                                              ),
                                              const Padding(padding: EdgeInsets.only(right:4)),
                                              Text('HR',
                                                  style: TextStyle(color: Colors.black,fontSize:Adapt.widthPt(12),fontWeight: FontWeight.w300)),
                                            ],
                                          )
                                      ),
                                      Text(r.data["questionAsk"][index]["replyCreateTime"],
                                          style: TextStyle(color: Colors.grey,fontSize:Adapt.widthPt(12),)),
                                    ],
                                  ),
                                  Padding(
                                    padding:const EdgeInsets.only(top:6),
                                    child:Text(r.data["questionAsk"][index]["content"]??'',
                                        style: TextStyle(color: Colors.black,fontSize:Adapt.widthPt(14))) ,
                                  ),
                                  const Divider(
                                    color: Color.fromRGBO(217, 217, 217, 1),
                                  ),
                                ],
                              ),),
                        );
                      }),
                ],
              );
            }),
      ),
    );
  }


  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
        key: noticeKey,
        child:WillPopScope(
          onWillPop: () async {
            removeWatermark();
            return Future.value(true);
          },
          child: Scaffold(
            appBar: AppBar(
              backgroundColor: Colors.white,
              leading:  IconButton(
                color: Colors.black,
                icon: const Icon(Icons.arrow_back_ios),
                onPressed: () {
                  removeWatermark();
                  Navigator.of(context).pop();
                },
              ),
              elevation: 0,
            ),
            body:r!=null?
            SingleChildScrollView(
                child:Container(
                  color: Colors.white,
                  child:Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(right: 12,left: 12),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.all(10),
                              child:Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.fromLTRB(0, 10, 0, 8),
                                    child:Text(r?.data["title"],style: const TextStyle(fontSize: 22,fontWeight: FontWeight.w600)),),
                                  if(widget.direction== Direction.problem)
                                    Row(
                                      children: [
                                        Padding(
                                          padding: const EdgeInsets.only(right: 50),
                                          child:Row(
                                            mainAxisAlignment: MainAxisAlignment.end,
                                            children: [
                                              const Icon(Icons.remove_red_eye_outlined,color: Colors.grey,size: 22,),
                                              Text(r.data["readCount"].toString(),style: const TextStyle(color: Colors.grey,fontSize: 14),),
                                            ],
                                          ) ,

                                        ),
                                        Text('${r?.data["publishTime"].split("-")[1]}-${r?.data["publishTime"].split("-")[2].replaceAll(':${r?.data["publishTime"].split(":")[2]}:','')}',
                                            style: const TextStyle(color: Colors.grey,fontSize: 12)),
                                      ],
                                    ),
                                  if(widget.direction== Direction.notice||widget.direction==Direction.newestNotice)
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children:  [
                                        Text.rich(TextSpan(
                                            children: [
                                              const TextSpan(
                                                  text: "作者:",
                                                  style: TextStyle(color: Colors.grey)),
                                              TextSpan(
                                                  text: r?.data["author"],
                                                  style: const TextStyle(color: Colors.black)),
                                            ]
                                        )),
                                        Text('${r?.data["publishTime"].split("-")[1]}-${r?.data["publishTime"].split("-")[2].replaceAll(':${r?.data["publishTime"].split(":")[2]}','')}',
                                            style: const TextStyle(color: Colors.grey,fontSize: 12)),
                                      ],
                                    )
                                ],
                              ),
                            ),
                            // Container(
                            //   clipBehavior: Clip.hardEdge,
                            //   width: MediaQuery.of(context).size.width *0.9,
                            //   height: MediaQuery.of(context).size.height *0.2,
                            //   decoration:BoxDecoration(
                            //     borderRadius: const BorderRadius.all(Radius.circular(4)),
                            //     image: DecorationImage(
                            //       image: NetworkImage(r?.data["cover"].replaceAll("\\","/"),),
                            //       fit: BoxFit.fill
                            //     )
                            //   ),
                            // ),
                            // Image.network(
                            //   r?.data["cover"].replaceAll("\\","/"),
                            //   fit: BoxFit.cover,
                            //   ///加载错误显示
                            //   errorBuilder: (a,b,c){
                            //     return const Image(
                            //       image: AssetImage('assets/images/error_pic_long.png'),
                            //       fit: BoxFit.cover,);
                            //   },
                            // ),
                          ],
                        ),
                      ),
                      SizedBox(
                        width: MediaQuery.of(context).size.width *0.9,
                        child: HtmlRenderer(
                          htmlContent: htmlData,
                          onLinkTap: (String url) {
                            launchUrl(Uri.parse(url));
                          },
                          onImageTap: (String url) {
                            ///图片放大缩写功能
                            Navigator.push(
                              context,
                              PopRoute(
                                child: PhotoPreview(
                                  galleryItems: [url.replaceAll("\\", "/")]
                                )
                              )
                            );
                          },
                          height: 600, // Adjust height as needed
                          customStyles: const {
                            'html': 'background-color: transparent;',
                            'body': 'margin: 0; padding: 12px; font-size: 14px; color: #333333; font-family: PingFang SC; font-weight: 400; line-height: 1.5; background-color: transparent;',
                            'h4': 'width: 50px; background-color: lightblue;',
                            'table': 'background-color: rgba(238, 238, 238, 0.31); border-top: 1px solid grey; border-left: 1px solid grey; border-collapse: collapse;',
                            'tr': 'border-bottom: 1px solid grey; border-right: 1px solid grey;',
                            'th': 'padding: 6px; background-color: grey; color: white;',
                            'td': 'padding: 6px; text-align: left; vertical-align: top;',
                            'img': 'max-width: 100%; height: auto; cursor: pointer;',
                            'iframe': 'width: 100%; height: 300px; border: none;',
                          },
                        ),
                      ),

                      // SizedBox(
                      //   height: MediaQuery.of(context).size.height,
                      //   child: WebView(
                      //       javascriptMode: JavascriptMode.unrestricted,
                      //       onWebViewCreated: (WebViewController controller){
                      //         setState(() {
                      //         });
                      //         print('===html==================$data1');
                      //         controller.loadHtmlString("""
                      //       <!DOCTYPE html>
                      //       <html>
                      //       <head><meta name="viewport" content="width=device-width, initial-scale=1.0"></head>
                      //         <body>
                      //           <p> Hello WebView</p>
                      //            $data1
                      //           <p> <a href="https://www.baidu.com/" target="_blank">百度</a> </p><p><img src="图片" alt="" data-href="https://alifei05.cfp.cn/creative/vcg/800/version23/VCG41175510742.jpg" style="width: 100%;"/></p>
                      //           </body>
                      //       </html>
                      //       <script>
                      //         const resizeObserver = new ResizeObserver(entries =>
                      //         Resize.postMessage(document.documentElement.scrollHeight.toString()) )
                      //         resizeObserver.observe(document.body)
                      //       </script>
                      //                    """);
                      //       },
                      //       javascriptChannels: {
                      //         JavascriptChannel(name: "Resize", onMessageReceived: (JavascriptMessage message) {
                      //           double height = double.parse(message.message);
                      //           setState(() {
                      //             webViewHeight = height;
                      //           });
                      //         })
                      //       }
                      //   ),
                      // ),
                      if(widget.direction== Direction.problem)
                        GestureDetector(
                            onTap: (){
                              Navigator.push(context,
                                  PopRoute(
                                      child: InputButtomWidget(
                                        onEditingCompleteText: (text){
                                          print('点击发送 ---$text');
                                        },
                                        id: r.data["id"],
                                      )
                                  )
                              );
                            },
                            child: Container(
                              padding: const EdgeInsets.all(20),
                              alignment: Alignment.centerRight,
                              child: const Text("提问",style: TextStyle(color: Colors.grey,fontSize: 14),),
                            )
                        ),
                      if(widget.direction== Direction.notice||widget.direction==Direction.newestNotice)
                        Container(
                          padding: const EdgeInsets.all(20),
                          child:Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              const Icon(Icons.remove_red_eye_outlined,color: Colors.grey,size: 22,),
                              Text(r.data["readCount"].toString(),style: const TextStyle(color: Colors.grey,fontSize: 14),),
                            ],
                          ) ,

                        ),
                      if(widget.direction== Direction.problem)
                        questionAndAnswer(),


                    ],
                  ) ,
                )

            ):
            const Center(
              child: Text('内容即将呈现...'),
            ),
            backgroundColor:  Colors.white,
          ) ,
        )
    );

  }
}

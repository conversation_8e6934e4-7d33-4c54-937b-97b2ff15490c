import 'package:amkor/Notice_xq_pv.dart';
import 'package:amkor/components/routes.dart';
import 'package:amkor/services/employeeService.dart';
import 'package:amkor/watermark.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:amkor/widgets/html_renderer.dart';
import 'package:get_storage/get_storage.dart';
import 'package:url_launcher/url_launcher.dart';

import '../utils/adapt.dart';

class NoticeCookbookPage extends StatefulWidget {
  const NoticeCookbookPage({Key? key}) : super(key: key);
  @override
  State<NoticeCookbookPage> createState() => _NoticeXqPageState();
}

class _NoticeXqPageState extends State<NoticeCookbookPage> {
  OverlayEntry? overlayEntry;
  String data1='加载中...';
  dynamic r;
  bool states=false;

  Future<void> data() async {
    r = await Dish().GetDishAnnouncementContent();
    data1 = r!.data;
    setState(() {
    });
  }
  @override
  void initState() {
    // TODO: implement activate
    if(!kIsWeb) {
      Adapt.setAndroid();
    }
    //build构建的时候不能调setState
    var Gh=GetStorage().read('employee')['employeeId'];
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      addWaterMarkter(context, Gh, 2, 10);
    });

    data();
    super.initState();
  }
  @override
  void dispose() {
    removeWatermark();
    super.dispose();
  }


  ///默认水印
  void addWaterMarkter(BuildContext context, String string, int row, int column) async {
    OverlayState? overlayState = Overlay.of(context);
    overlayEntry = OverlayEntry(
        builder: (context) => WatermarkWidget(
          rowCount: row,
          columnCount: column,
          text: string,
          textStyle:
          const TextStyle(
              color: Color(0x09000000),
              fontSize: 16,
              decoration: TextDecoration.none),
        ));
    overlayState?.insert(overlayEntry!);
  }

  /// 移除水印
  void removeWatermark() async {
    if (overlayEntry != null) {
      overlayEntry!.remove();
      overlayEntry = null;
    }
  }


  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        removeWatermark();
        return Future.value(true);
      },
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.white,
          leading:  IconButton(
            color: Colors.black,
            icon: const Icon(Icons.arrow_back_ios),
            onPressed: () {
              removeWatermark();
              Navigator.of(context).pop();
            },
          ),
          elevation: 0,
        ),
        body:r!=null?
        SingleChildScrollView(
            child:Container(
              // color: Colors.white,
              alignment: Alignment.topCenter,
              child:Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  // Padding(
                  //   padding: const EdgeInsets.only(right: 12,left: 12),
                  //   child: Column(
                  //     crossAxisAlignment: CrossAxisAlignment.center,
                  //     // crossAxisAlignment: CrossAxisAlignment.start,
                  //     children: [
                  //       Padding(
                  //         padding: const EdgeInsets.all(10),
                  //         child:Column(
                  //           crossAxisAlignment: CrossAxisAlignment.start,
                  //           children: [
                  //             Padding(
                  //               padding: const EdgeInsets.fromLTRB(0, 10, 0, 8),
                  //               child:Text(r?.data["title"],style: const TextStyle(fontSize: 22,fontWeight: FontWeight.w600)),),
                  //               Row(
                  //                 children: [
                  //                   Padding(
                  //                     padding: const EdgeInsets.only(right: 50),
                  //                     child:Row(
                  //                       mainAxisAlignment: MainAxisAlignment.end,
                  //                       children: [
                  //                         const Icon(Icons.remove_red_eye_outlined,color: Colors.grey,size: 22,),
                  //                         Text(r.data["readCount"].toString(),style: const TextStyle(color: Colors.grey,fontSize: 14),),
                  //                       ],
                  //                     ) ,
                  //
                  //                   ),
                  //                   Text('${r?.data["publishTime"].split("-")[1]}-${r?.data["publishTime"].split("-")[2].replaceAll(':${r?.data["publishTime"].split(":")[2]}:','')}',
                  //                       style: const TextStyle(color: Colors.grey,fontSize: 12)),
                  //                 ],
                  //               ),
                  //               Row(
                  //                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  //                 children:  [
                  //                   Text.rich(TextSpan(
                  //                       children: [
                  //                         const TextSpan(
                  //                             text: "作者:",
                  //                             style: TextStyle(color: Colors.grey)),
                  //                         TextSpan(
                  //                             text: r?.data["author"],
                  //                             style: const TextStyle(color: Colors.black)),
                  //                       ]
                  //                   )),
                  //                   Text('${r?.data["publishTime"].split("-")[1]}-${r?.data["publishTime"].split("-")[2].replaceAll(':${r?.data["publishTime"].split(":")[2]}','')}',
                  //                       style: const TextStyle(color: Colors.grey,fontSize: 12)),
                  //                 ],
                  //               )
                  //           ],
                  //         ),
                  //       ),
                  //       Container(
                  //         clipBehavior: Clip.hardEdge,
                  //         width: MediaQuery.of(context).size.width *0.9,
                  //         height: MediaQuery.of(context).size.height *0.2,
                  //         decoration:BoxDecoration(
                  //           borderRadius: const BorderRadius.all(Radius.circular(4)),
                  //           image: DecorationImage(
                  //             image: NetworkImage(r?.data["cover"].replaceAll("\\","/"),),
                  //             fit: BoxFit.fill
                  //           )
                  //         ),
                  //       ),
                  //       Image.network(
                  //         r?.data["cover"].replaceAll("\\","/"),
                  //         fit: BoxFit.cover,
                  //         ///加载错误显示
                  //         errorBuilder: (a,b,c){
                  //           return const Image(
                  //             image: AssetImage('assets/images/error_pic_long.png'),
                  //             fit: BoxFit.cover,);
                  //         },
                  //       ),
                  //     ],
                  //   ),
                  // ),
                  SizedBox(
                    width: MediaQuery.of(context).size.width *0.9,
                    child: HtmlRenderer(
                      htmlContent: data1,
                      onLinkTap: (String url) {
                        launchUrl(Uri.parse(url));
                      },
                      onImageTap: (String url) {
                        ///图片放大缩写功能
                        Navigator.push(
                          context,
                          PopRoute(
                            child: PhotoPreview(
                              galleryItems: [url.replaceAll("\\", "/")]
                            )
                          )
                        );
                      },
                      height: 600, // Adjust height as needed
                      customStyles: const {
                        'html': 'background-color: transparent;',
                        'body': 'margin: 0; padding: 12px; font-size: 14px; color: #333333; font-family: PingFang SC; font-weight: 400; line-height: 1.5; background-color: transparent;',
                        'h4': 'width: 50px; background-color: lightblue;',
                        'table': 'background-color: rgba(238, 238, 238, 0.31); border-top: 1px solid grey; border-left: 1px solid grey; border-collapse: collapse;',
                        'tr': 'border-bottom: 1px solid grey; border-right: 1px solid grey;',
                        'th': 'padding: 6px; background-color: grey; color: white;',
                        'td': 'padding: 6px; text-align: left; vertical-align: top;',
                        'img': 'max-width: 100%; height: auto; cursor: pointer;',
                        'iframe': 'width: 100%; height: 300px; border: none;',
                      },
                    ),
                  ),
                ],
              ) ,
            )

        )
        : const Center(
          child: Text('内容即将呈现...'),
        ),
        backgroundColor:  Colors.white,
      ) ,
    );
  }
}

import 'dart:async';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:screen_protector/screen_protector.dart';
// import 'package:flutter_windowmanager/flutter_windowmanager.dart'; // 暂时禁用
// import 'package:secure_application/secure_application.dart'; // 暂时禁用

/// 安全管理器 - 替代 screenshot_callback 插件
/// 提供截屏检测等功能，配合 SecureApplication 使用
class SecurityManager {
  static SecurityManager? _instance;
  static SecurityManager get instance => _instance ??= SecurityManager._();

  SecurityManager._();

  /// 截屏回调函数列表
  final List<VoidCallback> _screenshotCallbacks = <VoidCallback>[];

  /// 是否已初始化
  bool _isInitialized = false;

  /// 获取初始化状态
  bool get isInitialized => _isInitialized;

  /// SecureApplication 控制器 (暂时禁用)
  // SecureApplicationController? _controller;

  /// 初始化安全管理器
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // 启用防截屏功能
      await ScreenProtector.protectDataLeakageOn();

      _isInitialized = true;
      print('SecurityManager initialized successfully with screen protection');
    } catch (e) {
      print('Failed to initialize screen protection: $e');
      // 即使防截屏失败，也标记为已初始化，避免重复尝试
      _isInitialized = true;
    }
  }

  /// 添加截屏监听器
  void addScreenshotListener(VoidCallback callback) {
    _screenshotCallbacks.add(callback);
  }

  /// 移除截屏监听器
  void removeScreenshotListener(VoidCallback callback) {
    _screenshotCallbacks.remove(callback);
  }

  /// 清除所有截屏监听器
  void clearScreenshotListeners() {
    _screenshotCallbacks.clear();
  }

  /// 手动触发截屏回调（用于测试或特殊情况）
  void triggerScreenshotCallbacks() {
    for (final callback in _screenshotCallbacks) {
      try {
        callback();
      } catch (e) {
        print('Error in screenshot callback: $e');
      }
    }
  }

  /// 获取 SecureApplication 控制器 (暂时禁用)
  // SecureApplicationController? get controller => _controller;

  /// 销毁安全管理器，清理资源
  Future<void> dispose() async {
    if (!_isInitialized) return;

    try {
      // 关闭防截屏功能
      await ScreenProtector.protectDataLeakageOff();

      // 清空回调列表
      _screenshotCallbacks.clear();

      _isInitialized = false;
      print('SecurityManager disposed successfully');
    } catch (e) {
      print('Failed to dispose screen protection: $e');
    }
  }

  /// 获取截图数据（用于上传历史记录）
  Future<Uint8List?> captureWidget(GlobalKey repaintKey) async {
    try {
      BuildContext? buildContext = repaintKey.currentContext;
      if (buildContext != null) {
        RenderRepaintBoundary? boundary =
            buildContext.findRenderObject() as RenderRepaintBoundary?;

        if (boundary != null) {
          // 获取当前设备的像素比
          double pixelRatio = MediaQuery.of(buildContext).devicePixelRatio;
          ui.Image image = await boundary.toImage(pixelRatio: pixelRatio);
          ByteData? byteData =
              await image.toByteData(format: ui.ImageByteFormat.png);
          return byteData?.buffer.asUint8List();
        }
      }
    } catch (e) {
      print('Error capturing widget: $e');
    }
    return null;
  }

  /// 压缩图片数据
  Future<Uint8List?> compressImageData(Uint8List imageData) async {
    try {
      print("===原数据大小==${imageData.length}");

      // 压缩图片
      Uint8List compressedData = await FlutterImageCompress.compressWithList(
        imageData,
        quality: 70,
      );

      print("===输出压缩后的数据大小===${compressedData.length}");
      return compressedData;
    } catch (e) {
      print('Error compressing image: $e');
      return null;
    }
  }
}

/// 安全应用包装器 - 用于包装需要安全保护的页面
class SecureWrapper extends StatefulWidget {
  final Widget child;
  final bool preventScreenshot;
  final bool hideInBackground;

  const SecureWrapper({
    Key? key,
    required this.child,
    this.preventScreenshot = false,
    this.hideInBackground = true,
  }) : super(key: key);

  @override
  State<SecureWrapper> createState() => _SecureWrapperState();
}

class _SecureWrapperState extends State<SecureWrapper>
    with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _updateSecuritySettings();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didUpdateWidget(SecureWrapper oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.preventScreenshot != widget.preventScreenshot ||
        oldWidget.hideInBackground != widget.hideInBackground) {
      _updateSecuritySettings();
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // 当应用从后台恢复时，重新应用安全设置
    if (state == AppLifecycleState.resumed) {
      _updateSecuritySettings();
    }
  }

  void _updateSecuritySettings() {
    // 注意：实际的安全设置需要通过 SecureApplication 组件在应用级别配置
    // 这里只是一个占位符，实际功能由 SecureApplication 和 SecureGate 提供
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}

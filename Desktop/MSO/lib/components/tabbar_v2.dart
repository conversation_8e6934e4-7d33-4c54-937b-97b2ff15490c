import 'package:amkor/utils/gKey.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../utils/adapt.dart';

/// 这是一个可以指定SafeArea区域背景色的AppBar
/// PreferredSizeWidget提供指定高度的方法
/// 如果没有约束其高度，则会使用PreferredSizeWidget指定的高度

class TabberV2 extends StatefulWidget implements PreferredSizeWidget {
  final double contentHeight; //从外部指定高度
  bool themeIsDark;
  bool showBack;
  Widget? trailingWidget;
  String title;
  double titleSize;
  Color? bgColor;
  bool hasUnderline;
  TabberV2(
      {this.showBack = false,
      required this.title,
      this.titleSize=18,
      this.contentHeight = 60,
      this.themeIsDark = false,
      this.trailingWidget,
      this.bgColor,
      this.hasUnderline = true})
      : super();

  @override
  State<StatefulWidget> createState() {
    return _CustomAppbarState();
  }

  @override
  Size get preferredSize => Size.fromHeight(contentHeight);
}

/// 这里没有直接用SafeArea，而是用Container包装了一层
/// 因为直接用SafeArea，会把顶部的statusBar区域留出空白
/// 外层Container会填充SafeArea，指定外层Container背景色也会覆盖原来SafeArea的颜色
///     var statusheight = MediaQuery.of(context).padding.top;  获取状态栏高度

class _CustomAppbarState extends State<TabberV2> {
  @override
  void initState() {
    if(!kIsWeb) {
      Adapt.setAndroid(isDark: !widget.themeIsDark);
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Color? c;
    if (widget.bgColor != null) {
      c = widget.bgColor;
    } else if (widget.themeIsDark) {
      c = Colors.black;
    } else {
      c = Colors.white;
    }

    return Container(
      color: c!,
      // padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top),
      child: SafeArea(
        top: true,
        child: Container(
            decoration: widget.hasUnderline
                ? const UnderlineTabIndicator(
                    borderSide: BorderSide(
                      width: 1.0,
                      color: Color(0xFFeeeeee),
                    ),
                  )
                : null,
            height: widget.contentHeight,
            child: Stack(
              alignment: Alignment.center,
              children: <Widget>[
                Positioned(
                  left: 0,
                  child: Container(
                    padding: const EdgeInsets.only(left: 5),
                    child: widget.showBack
                        ? IconButton(
                            onPressed: () => Navigator.pop(context),
                            icon: Icon(
                              Icons.arrow_back_ios,
                              color: widget.themeIsDark
                                  ? Colors.white
                                  : Colors.black,
                              size: 22,
                            ),
                          )
                        : Container(),
                  ),
                ),
                Text(
                  widget.title,
                  style: TextStyle(
                    fontSize: widget.titleSize,
                    color: widget.themeIsDark ? Colors.white : Colors.black,
                  ),
                ),
                Positioned(
                  right: 0,
                  child: Container(
                    padding: const EdgeInsets.only(right: 5),
                    child: widget.trailingWidget,
                  ),
                ),
              ],
            )),
      ),
    );
  }
}


/// 搜索AppBar
class AppBarSearch extends StatefulWidget implements PreferredSizeWidget {
  const AppBarSearch({
    Key? key,
    this.borderRadius = 10,
    this.autoFocus = false,
    this.focusNode,
    this.controller,
    this.height = 40,
    this.value,
    this.leading,
    this.leadings=false,
    this.backgroundColor=Colors.transparent,
    this.suffix,
    this.actions = const [],
    this.hintText,
    this.onTap,
    this.onClear,
    this.onCancel,
    this.onChanged,
    this.onSearch,
    this.onRightTap,
  }) : super(key: key);
  final double? borderRadius;
  final bool? autoFocus;
  final FocusNode? focusNode;
  final TextEditingController? controller;

  // 输入框高度 默认40
  final double height;

  // 默认值
  final String? value;

  // 最前面的组件
  final Widget? leading;
  // 最前面的组件是否需求
  final bool? leadings;
  // 背景色
  final Color? backgroundColor;

  // 搜索框内部后缀组件
  final Widget? suffix;

  // 搜索框右侧组件
  final List<Widget> actions;

  // 输入框提示文字
  final String? hintText;

  // 输入框点击回调
  final VoidCallback? onTap;

  // 清除输入框内容回调
  final VoidCallback? onClear;

  // 清除输入框内容并取消输入
  final VoidCallback? onCancel;

  // 输入框内容改变
  final ValueChanged<String>? onChanged;

  // 点击键盘搜索
  final ValueChanged<String>? onSearch;

  // 点击右边widget
  final VoidCallback? onRightTap;

  @override
  _AppBarSearchState createState() => _AppBarSearchState();

  @override
  Size get preferredSize => Size.fromHeight(height);
}

class _AppBarSearchState extends State<AppBarSearch> {
  TextEditingController? _controller;
  FocusNode? _focusNode;

  bool get isFocus => _focusNode?.hasFocus ?? false; //是否获取焦点

  bool get isTextEmpty => _controller?.text.isEmpty ?? false; //输入框是否为空

  bool get isActionEmpty => widget.actions.isEmpty; // 右边布局是否为空

  bool isShowCancel = false;

  @override
  void initState() {
    _controller = widget.controller ?? TextEditingController();
    _focusNode = widget.focusNode ?? FocusNode();
    if (widget.value != null) _controller?.text = widget.value ?? "";
    // 焦点获取失去监听
    _focusNode?.addListener(() => setState(() {}));
    // 文本输入监听
    _controller?.addListener(() {
      setState(() {});
    });
    super.initState();
  }

  // 清除输入框内容
  void _onClearInput() {
    setState(() {
      _controller?.clear();
    });
    widget.onClear?.call();
  }

  // 取消输入框编辑失去焦点
  void _onCancelInput() {
    setState(() {
      _controller?.clear();
      _focusNode?.unfocus(); //失去焦点
    });
    // 执行onCancel
    widget.onCancel?.call();
  }

  Widget _suffix() {
    if (!isTextEmpty) {
      return InkWell(
        onTap:widget.onClear??_onClearInput,
        child: SizedBox(
          width: widget.height,
          height: widget.height,
          child: const Icon(Icons.cancel, size: 22, color: Color(0xFF999999)),
        ),
      );
    }
    return widget.suffix ?? const SizedBox();
  }

  List<Widget> _actions() {

    List<Widget> list = [];
    if (isFocus || !isTextEmpty) {
      list.add(InkWell(
        onTap: widget.onRightTap ?? _onCancelInput,
        child: Container(
          constraints: const BoxConstraints(minWidth: 48),
          alignment: Alignment.center,
          child: const Text(
            '搜索',
            // '取消',
            style: TextStyle(color: Color.fromRGBO(20, 178, 181, 1)),
          ),
        ),
      ));
    } else if (!isActionEmpty) {
      list.addAll(widget.actions);
    }
    return list;
  }

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.transparent,
      //阴影z轴
      elevation: 0,
      // 标题与其他控件的间隔
      titleSpacing: 0,
      leadingWidth:widget.leadings!?0: widget.leading ==null?40:70,
      leading: widget.leading ??
          InkWell(
            child: const Icon(
              Icons.arrow_back_ios,
              size: 18,
              color: Colors.black,
            ),
            onTap: () {
              Navigator.pop(context);
            },
          ),
      title: Container(
          margin: const EdgeInsetsDirectional.only(end: 10),
          height: widget.height*0.8,
          decoration: BoxDecoration(
            color: const Color(0xFFF2F2F2),
            borderRadius: BorderRadius.circular(widget.borderRadius ?? 0),
          ),
          child: Row(
            children: [
              Expanded(
                // 权重
                flex: 1,
                child: TextField(
                  autofocus: widget.autoFocus ?? false,
                  // 是否自动获取焦点
                  focusNode: _focusNode,
                  // 焦点控制
                  controller: _controller,
                  // 与输入框交互控制器
                  //装饰
                  decoration: InputDecoration(
                    fillColor: widget.backgroundColor,//背景颜色，
                    filled: true,
                    isDense: true,
                    border: InputBorder.none,
                    // contentPadding:const EdgeInsets.all(8),
                    // border: OutlineInputBorder(
                    //     borderRadius: BorderRadius.circular(15),
                    //     borderSide: BorderSide.none),
                    hintText: widget.hintText ?? '请输入关键字',
                    hintStyle: const TextStyle(fontSize: 14, ),
                    prefixIcon: SizedBox(
                      width: widget.height,
                      height: widget.height,
                      child: const Icon(Icons.search, size: 20, color: Color(0xFF999999)),
                    ),
                    suffixIcon:  _suffix(),
                  ),
                  style: const TextStyle(
                    fontSize: 14
                  ),
                  // 键盘动作右下角图标
                  textInputAction: TextInputAction.search,
                  onTap: widget.onTap,
                  // 输入框内容改变回调
                  onChanged: widget.onChanged,
                  onSubmitted: widget.onSearch, //输入框完成触发

                ),
              ),

            ],
          )),
      actions: _actions(),
    );
  }

  @override
  void dispose() {
    _controller?.dispose();
    _focusNode?.dispose();
    super.dispose();
  }
}

class FullScreenSearchModal extends ModalRoute {
  @override
  Duration get transitionDuration => const Duration(milliseconds: 500);

  @override
  bool get opaque => false;

  @override
  bool get barrierDismissible => false;

  @override
  Color get barrierColor => Colors.black.withOpacity(0.6);

  @override
  String? get barrierLabel => null;

  @override
  bool get maintainState => true;

  @override
  Widget buildPage(
      BuildContext context,
      Animation<double> animation,
      Animation<double> secondaryAnimation,
      ) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body:Column(
        children: [
          Container(
            color: Colors.white,
            child: Column(
              children: [
                const AppBarSearch(
                  leadings: true,
                ),
               const Divider(
                  height: 1,
                  color: Colors.grey,
                ),
                ListTile(
                  title:const Text('Flutter tutorials'),
                  leading:const Icon(Icons.search),
                  trailing:const Icon(Icons.close),
                  onTap: (){
                    Navigator.pop(context);
                  },
                ),
                const Divider(
                  height: 1,
                  color: Colors.grey,
                ),
                ListTile(
                  title:const Text('Flutter tutorials'),
                  leading:const Icon(Icons.search),
                  trailing:const Icon(Icons.close),
                  onTap: (){
                    Navigator.pop(context);
                  },
                ),
                const Divider(
                  height: 1,
                  color: Colors.grey,
                ),
                ListTile(
                  title:const Text('Flutter tutorials'),
                  leading:const Icon(Icons.search),
                  trailing:const Icon(Icons.close),
                  onTap: (){
                    Navigator.pop(context);
                  },
                ),
              ],
            ),
          ),
          Container(
            padding:const EdgeInsetsDirectional.all(12),
            color: Colors.white,
            alignment: Alignment.center,
            child:const Text("已展示所有搜索结果",style: TextStyle(fontSize: 12,color: Colors.grey,),),
          ),
          Expanded(
              child:GestureDetector(
                onTap: (){
                  // FocusScope.of(context).requestFocus(FocusNode());
                  Navigator.pop(context);
                },
                child:Container(
                  color: Colors.transparent,
                ),
              )
          ),
        ],
      ),



    );
  }

  // animations for the search modal
  @override
  Widget buildTransitions(BuildContext context, Animation<double> animation,
      Animation<double> secondaryAnimation, Widget child) {
    // add fade animation
    return FadeTransition(
      opacity: animation,
      // add slide animation
      child: SlideTransition(
        position: Tween<Offset>(
          begin: const Offset(0, -1),
          end: Offset.zero,
        ).animate(animation),
        child: child,
      ),
    );
  }
}
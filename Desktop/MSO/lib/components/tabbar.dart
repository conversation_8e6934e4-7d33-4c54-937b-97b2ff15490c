import 'package:flutter/material.dart';
import 'package:flutter_launcher_icons/xml_templates.dart';

import '../utils/adapt.dart';

class Tabbar extends StatelessWidget {
  final String title;
  final double barHeight = Adapt.widthPt(46); //标题栏高度
  final bool showBack;
  final Color fontColor;
  final Widget? leftBtn;
  final Widget? rightBtn;
  final Color backgroundColor;
  Tabbar(
      {this.title = "",
      this.showBack = false,
      this.fontColor = Colors.white,
      this.rightBtn,
      this.leftBtn,
      this.backgroundColor=Colors.transparent});
  @override
  Widget build(BuildContext context) {
    //获取系统状态栏高度
    final double statusBarHeight = MediaQuery.of(context).padding.top;

    return Container(
      padding: EdgeInsets.only(top: statusBarHeight),
      color: backgroundColor,
      height: barHeight + statusBarHeight,
      child: Padding(
        padding:
            EdgeInsets.only(left: Adapt.widthPt(15), right: Adapt.widthPt(15)),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              flex: 1,
              child:leftBtn==null?
              showBack ?
              Align(
                alignment: Alignment.centerLeft,
                child: IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: Icon(
                    Icons.arrow_back_ios,
                    color: fontColor,
                    size: 22,
                  ),
                ),
              ):
              const SizedBox(
                height: 20,
                width: 20,
              ):
              Align(
                alignment: Alignment.centerLeft,
                child: leftBtn,
              ),
            ),
            Expanded(
              flex: 3,
              child: title != ""
                  ? Align(
                      alignment: Alignment.center,
                      child: Text(
                        title,
                        style: TextStyle(
                          color: fontColor,
                          ///转换web的尺寸
                          fontSize: Adapt.widthPt(22),
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    )
                  : Container(
                      height: 20,
                      width: 20,
                    ),
            ),
            Expanded(
              flex: 1,
              child: rightBtn != null
                  ? Align(
                      alignment: Alignment.centerRight,
                      child: rightBtn,
                    )
                  : Container(
                      height: 20,
                      width: 20,
                    ),
            )
          ],
        ),
      ),
    );
  }
}

import 'package:amkor/Change_Password.dart';
import 'package:amkor/Notice_xq.dart';
import 'package:amkor/PersonPages/personAddress.dart';
import 'package:amkor/PersonPages/updatePwd.dart';
import 'package:amkor/about.dart';
import 'package:amkor/indexPages/index/index.dart';
import 'package:amkor/indexPages/person.dart';
import 'package:amkor/kong.dart';
import 'package:amkor/login.dart';
import 'package:amkor/workPages/ATCinterview.dart';
import 'package:amkor/workPages/AddFunction.dart';
import 'package:amkor/workPages/EntryForm.dart';
import 'package:amkor/workPages/FQAPage.dart';
import 'package:amkor/workPages/Paymentcode.dart';
import 'package:amkor/workPages/PushIN.dart';
import 'package:amkor/workPages/RiskArea.dart';
import 'package:amkor/workPages/accumulationFund.dart';
import 'package:amkor/workPages/caidan02.dart';
import 'package:amkor/workPages/trainingRecord.dart';
import 'package:amkor/workPages/customReport.dart';
import 'package:amkor/workPages/qa.dart';
import 'package:amkor/workPages/show_code.dart';
import 'package:amkor/workPages/shuttleBus.dart';
import 'package:amkor/workPages/umbrella.dart';
import 'package:amkor/workPages/uplaod_covid.dart';
import 'package:amkor/workPages/upload_antigen.dart';
import 'package:amkor/workPages/upload_location.dart';
import 'package:amkor/pages/security_test_page.dart';
import 'package:flutter/material.dart';

import '../utils/enums.dart';

class Routes {
  String Telephonenumber;
  int? maxPicSize;
  static String? SJH;
  static num? XZ;
  Routes(this.Telephonenumber, this.maxPicSize) {
    SJH = Telephonenumber;
    XZ = maxPicSize;
  }
  static Map<String, WidgetBuilder> getRoutes() {
    return {
      RouteNames.login: (context) => const LoginPage(),
      //忘记密码
      RouteNames.forgetPassword: (context) => ForgetPassword(),
      RouteNames.indexPage: (context) => const IndexPage(),
      RouteNames.updatePwd: (context) => UpdatePwdPage(Telephonenumber: SJH),
      //关于
      RouteNames.about: (context) => const AboutPage(),
      RouteNames.uploadLocation: (context) => const UploadLocationPage(),
      RouteNames.qa: (context) => const QaPage(),
      RouteNames.showInCode: (context) => const ShowCodePage(
            codeDirection: CodeDirection.In,
          ),
      RouteNames.showOutCode: (context) =>
          const ShowCodePage(codeDirection: CodeDirection.Out),
      //位置
      RouteNames.showPositionCode: (context) =>
          const ShowCodePage(codeDirection: CodeDirection.Position),
      //员工码
      RouteNames.showPassportCode: (context) =>
          const ShowCodePage(codeDirection: CodeDirection.Passport),
      //宿舍码
      RouteNames.showDormitoryCode: (context) =>
          const ShowCodePage(codeDirection: CodeDirection.Dormitory),
      RouteNames.person: (context) => PersonPage(),
      RouteNames.personAddress: (context) => const PersonAddressPage(),
      RouteNames.uploadCovid: (context) => UplaodCovidPage(maxPicSize: XZ),
      RouteNames.uploadAntigen: (context) =>
          UploadAntigenPage(maxPicSize: XZ?.toInt()),
      // RouteNames.uploadAntigen: (context) => UploadAntigenPage(),
      RouteNames.addFunction: (context) => const AddFunction(),
      //爱心伞
      RouteNames.umbrellaPage: (context) => const UmbrellaPage(),
      //自定义报表
      RouteNames.customReportPage: (context) => const CustomReportPage(),
      //预约登记
      RouteNames.interviewPage: (context) => const InterviewPage(),
      //公积金
      RouteNames.accumulationFundPage: (context) =>
          const accumulationFundPage(),
      //员工内推表
      RouteNames.pushInPage: (context) => const PushInPage(),
      //员工入职信息表
      RouteNames.entryFormPage: (context) => const EntryFormPage(),
      //公告详情
      RouteNames.noticeXqPage: (context) => NoticeXqPage(),
      //风险地区
      RouteNames.riskAreaPage: (context) => const RiskAreaPage(),
      //常见问题
      RouteNames.FQAPage: (context) => const FQAPage(),
      //付款码
      RouteNames.paymentCodePage: (context) => const PaymentCodePage(),
      //班车
      RouteNames.ShuttleBusPage: (context) => ShuttleBus(),
      //食谱
      // RouteNames.cookbook: (context) => NoticeXqPage(id:"104",direction:Direction.cookbook,),
      RouteNames.cookbook: (context) => const CaidanPage(),
      //消费记录
      RouteNames.trainingRecord: (context) => const TrainingRecord(),
      //安全功能测试
      RouteNames.securityTestPage: (context) => const SecurityTestPage(),

      RouteNames.kongPage: (context) => const KongPage(),
    };
  }
}

class RouteNames {
  static const String login = "/login";
  //忘记密码
  static const String forgetPassword = "/forgetPassword";
  static const String indexPage = "/indexPage";
  static const String updatePwd = "/updatePwd";
  //关于
  static const String about = "/about";
  static const String uploadLocation = "/uploadPosition";
  static const String qa = "/qa";
  static const String showInCode = "/showInCode";
  static const String showOutCode = "/showOutCode";
  //位置
  static const String showPositionCode = "/showPositionCode";
  //员工码
  static const String showPassportCode = "/showPassportCode";
  //宿舍码
  static const String showDormitoryCode = "/showDormitoryCode";
  static const String person = "/person";
  static const String personAddress = "/personAddress";
  static const String uploadCovid = "/uploadCovid";
  static const String uploadAntigen = "/uploadAntigen";
  static const String addFunction = "/addFunction";
  //爱心伞
  static const String umbrellaPage = "/umbrellaPage";
  //自定义报表
  static const String customReportPage = "/customReportPage";
  //预约登记
  static const String interviewPage = "/interviewPage";
  //公积金
  static const String accumulationFundPage = "/accumulationFundPage";
  //员工内推
  static const String pushInPage = "/pushInPage";
  //员工入职信息表
  static const String entryFormPage = "/entryFormPage";
  //公告详情
  static const String noticeXqPage = "/noticeXqPage";
  //风险地区
  static const String riskAreaPage = "/riskAreaPage";
  //常见问题
  static const String FQAPage = "/FQAPage";
  //付款码
  static const String paymentCodePage = "/paymentCodePage";
  //班车
  static const String ShuttleBusPage = "/ShuttleBusPage";
  //食谱
  static const String cookbook = "/cookbook";
  //培训记录
  static const String trainingRecord = "/TrainingRecord";
  //安全功能测试
  static const String securityTestPage = "/securityTestPage";

  static const String kongPage = "/KongPage";
}

//发送评论过渡路由
class PopRoute extends PopupRoute {
  final Duration _duration = const Duration(milliseconds: 300);
  Widget? child;

  PopRoute({@required this.child});

  @override
  Color? get barrierColor => null;

  @override
  bool get barrierDismissible => true;

  @override
  String? get barrierLabel => null;

  @override
  Widget buildPage(BuildContext context, Animation<double> animation,
      Animation<double> secondaryAnimation) {
    return child!;
  }

  @override
  Duration get transitionDuration => _duration;
}

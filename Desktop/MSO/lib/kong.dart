
import 'package:amkor/components/tabbar.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../utils/adapt.dart';
import 'components/tabbar_v2.dart';

class KongPage extends StatefulWidget {
  const KongPage({Key? key }) : super(key: key);

  @override
  State<KongPage> createState() => _WorkbenchState();
}

class _WorkbenchState extends State<KongPage> {
  @override
  void initState() {
    // TODO: implement activate
    super.initState();
    if(!kIsWeb) {
      Adapt.setAndroid();
    }
  }

  @override
  Widget build(BuildContext context) {
    return  Scaffold(
      backgroundColor: Colors.transparent,
      appBar: TabberV2(
        title: '',
        showBack: true,
        themeIsDark: false,
      ),
      body: const Center(
        child: Text("内容即将呈现..."),
      ),
    );
  }
}

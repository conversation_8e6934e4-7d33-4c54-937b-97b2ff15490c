import 'package:amkor/login.dart';
import 'package:amkor/model/MyBinding.dart';
import 'package:amkor/services/Pgy_update.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class StartPage extends StatefulWidget {
  const StartPage({Key? key}) : super(key: key);

  @override
  State<StartPage>  createState() => _StartPageState();
}

class _StartPageState extends State<StartPage> with TickerProviderStateMixin{

  late AnimationController _animationController;
  Animation<double>? _fadeAnimation;
  final UIMessage uiMessageController = Get.put(UIMessage());

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );
    _fadeAnimation = Tween(begin: 0.0, end: 1.0).animate(_animationController);

    // 初始化连接
    // uiMessageController.initWebSocket();

    _fadeAnimation?.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => const UpdaterPage(LoginPage())),
          (route) => false, // 清除所有路由栈
        );
      }
    });

    _animationController.forward(); // 播放动画
  }

  final DecorationTween decorationTween = DecorationTween(
    begin: BoxDecoration(
      color: const Color(0x66666666),
      border: Border.all(style: BorderStyle.none),
      borderRadius: BorderRadius.circular(10.0),
      shape: BoxShape.rectangle,
      boxShadow: const <BoxShadow>[
        BoxShadow(
          color: Color(0x66666666),
          blurRadius: 10.0,
          spreadRadius: 3.0,
          offset: Offset(0, 6.0),
        )
      ],
    ),
    end: BoxDecoration(
      color: const Color(0xFFFFFFFF),
      border: Border.all(
        style: BorderStyle.none,
      ),
      borderRadius: BorderRadius.circular(60.0),
      // No shadow.
    ),
  );

  late final AnimationController _controller = AnimationController(
    vsync: this,
    duration: const Duration(seconds: 3),
  )..repeat(reverse: true);



  @override
  void dispose() {
    _animationController.dispose();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
        alignment: Alignment.center,
        // fit: StackFit.expand,
        children: [
          FadeTransition(
            opacity: _fadeAnimation!,
            child: Image.asset("assets/images/main_bg.png",
              fit: BoxFit.cover,
              width:double.infinity,
              height: double.infinity,),
          ),


        ],
      )

    ;
  }


}


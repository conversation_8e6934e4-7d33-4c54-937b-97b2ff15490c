// import 'dart:io';

// import 'package:amkor/login.dart';
// import 'package:amkor/utils/gKey.dart';
// import 'package:flutter/foundation.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:jpush_flutter/jpush_flutter.dart';

// class PushUtil {
//   static JPush? jpush;

//   static initPush({dynamic localN}) async {
//     jpush ??= JPush();
//     try {

//       jpush!.addEventHandler(
//           onReceiveNotification: (Map<String, dynamic> message) async {

//             debugPrint("flutter onReceiveNotification: $message");
//           },
//           ///点击的回调事件
//           onOpenNotification: (Map<String, dynamic> message) async {
//             print('------------------------------------');
//             ///设置应用 badge 值，该方法还会同步 JPush 服务器的的 badge 值，JPush 服务器的 badge 值用于推送 badge 自动 +1 时会用到。
//             jpush!.setBadge(0).then((map) {});
//             ///清空通知栏上所有通知
//             jpush!.clearAllNotifications();
//             ///获取 iOS 点击推送启动应用的那条通知。
//             jpush!.getLaunchAppNotification().then((map) {});
//             debugPrint("flutter onOpenNotification: $message");
//             // GKey.navKek.currentState!.pushAndRemoveUntil(
//             //     MaterialPageRoute(builder: (BuildContext context) => LoginPage()),
//             //         (route) => route == null);
//             // Navigator.of(GKey.context!).removeRoute(GKey.modalRoute as Route);

//           },
//           onReceiveMessage: (Map<String, dynamic> message) async {

//         debugPrint("flutter onReceiveMessage: $message");
//       },
//           onReceiveNotificationAuthorization:
//           (Map<String, dynamic> message) async {

//         debugPrint("flutter onReceiveNotificationAuthorization: $message");
//       });
//     } on PlatformException {

//       // platformVersiononOpenNotification = 'Failed to get platform version.';
//     }

//     jpush!.setup(
//       appKey: "c878cea447ec83f459a97b7a", //自己应用的 AppKey
//       channel: "theChannel",
//       production: false,
//       debug: false,
//     );

//     ///声音，震动等设置
//     jpush!.applyPushAuthority(
//          const NotificationSettingsIOS(sound: true, alert: true, badge: true));
//     jpush!.isNotificationEnabled().then((bool value) {
//       // jpush!.stopPush();
//       // 返回是否开启通知
//       if(!value) {
//         ///跳转系统推送设置
//         jpush!.openSettingsForNotification();

//         jpush!.resumePush();
//       }
//     });



//    /// 申请推送权限，注意这个方法只会向用户弹出一次推送权限请求（如果用户不同意，之后只能用户到设置页面里面勾选相应权限），需要开发者选择合适的时机调用。
//    /// 注意： iOS10+ 可以通过该方法来设置推送是否前台展示，是否触发声音，是否设置应用角标 badge
//     jpush!.applyPushAuthority(
//         const NotificationSettingsIOS(
//         sound: true,
//         alert: true,
//         badge: true)
//     );

//     ///给后台的registration id
//     jpush!.getRegistrationID().then((rid) {
//       print("flutter get registration id : $rid");
//     });
//     if(localN!=null) {
//       jpush?.sendLocalNotification(localN)
//           .then((res) {

//       });
//     }
//   }
// }


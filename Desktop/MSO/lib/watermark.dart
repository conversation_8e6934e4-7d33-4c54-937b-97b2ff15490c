import 'dart:math';
import 'package:flutter/material.dart';

class WatermarkWidget extends StatelessWidget{
  final int rowCount;
  final int columnCount;
  final String text;
  final TextStyle textStyle;

  const WatermarkWidget({
    Key? key,
    required this.rowCount,
    required this.columnCount,
    required this.text,
    required this.textStyle,
  }) : super(key: key);



  @override
  Widget build(BuildContext context) {
    return IgnorePointer(
      child: Padding(padding: EdgeInsetsDirectional.only(top: AppBar().preferredSize.height),
        child: Column(
          children: creatColumnWidgets(),
        ),
      ),);
  }
  List<Widget> creatRowWdiges() {
    List<Widget> list = [];
    for (var i = 0; i < rowCount; i++) {
      final widget = Expanded(
          child: Center(
              child: Transform.rotate(
                  angle: pi / 10, child:Column(
                children: [
                  Text(text, style: textStyle),
                  const Padding(padding: EdgeInsetsDirectional.only(top: 10)),
                  Text(text, style:  const TextStyle(
                      color: Color.fromRGBO(201, 205, 212, 0.4),
                      fontSize: 14,
                      overflow: TextOverflow.ellipsis,
                      decoration: TextDecoration.none)),
                ],
              )

              )));
      list.add(widget);
    }
    return list;
  }

  List<Widget> creatColumnWidgets() {
    List<Widget> list = [];
    for (var i = 0; i < columnCount; i++) {
      final widget = Expanded(
          child: Row(
            children: creatRowWdiges(),
          ));
      list.add(widget);
    }
    return list;
  }
}


/// 应用常量定义
/// 集中管理应用中使用的各种常量值
class AppConstants {
  // 私有构造函数，防止实例化
  AppConstants._();

  // ==================== 设计尺寸 ====================
  /// 设计稿宽度
  static const double designWidth = 375.0;
  
  /// 设计稿高度
  static const double designHeight = 667.0;

  // ==================== 动画时长 ====================
  /// 启动页动画时长
  static const Duration splashAnimationDuration = Duration(milliseconds: 1000);
  
  /// 对话框更新间隔
  static const Duration dialogUpdateInterval = Duration(milliseconds: 100);
  
  /// 页面切换动画时长
  static const Duration pageTransitionDuration = Duration(milliseconds: 300);

  // ==================== API相关 ====================
  /// 测试环境标识
  static const String testEnvironmentLabel = "Test API";
  
  /// 生产环境标识
  static const String productionEnvironmentLabel = "Production API";
  
  /// 请求超时时间
  static const Duration requestTimeout = Duration(seconds: 30);
  
  /// 连接超时时间
  static const Duration connectTimeout = Duration(seconds: 10);

  // ==================== 存储键名 ====================
  /// Token存储键
  static const String tokenStorageKey = "token";
  
  /// 用户信息存储键
  static const String userInfoStorageKey = "user_info";
  
  /// 语言设置存储键
  static const String languageStorageKey = "language";

  // ==================== UI相关 ====================
  /// 默认边距
  static const double defaultPadding = 16.0;
  
  /// 小边距
  static const double smallPadding = 8.0;
  
  /// 大边距
  static const double largePadding = 24.0;
  
  /// 默认圆角半径
  static const double defaultBorderRadius = 8.0;
  
  /// 卡片圆角半径
  static const double cardBorderRadius = 12.0;

  // ==================== 文件相关 ====================
  /// APK文件扩展名
  static const String apkFileExtension = '.apk';
  
  /// 最大文件上传大小 (10MB)
  static const int maxFileUploadSize = 10 * 1024 * 1024;

  // ==================== 网络相关 ====================
  /// HTTP状态码 - 成功
  static const int httpStatusOk = 200;
  
  /// HTTP状态码 - 创建成功
  static const int httpStatusCreated = 201;
  
  /// HTTP状态码 - 未授权
  static const int httpStatusUnauthorized = 401;
  
  /// HTTP状态码 - 服务器错误
  static const int httpStatusInternalServerError = 500;

  // ==================== 权限相关 ====================
  /// 权限请求间隔时间
  static const Duration permissionRequestInterval = Duration(seconds: 1);

  // ==================== 调试相关 ====================
  /// 是否启用详细日志
  static const bool enableVerboseLogging = true;
  
  /// 日志标签前缀
  static const String logTagPrefix = 'MSO_';
}

/// API端点常量
class ApiEndpoints {
  ApiEndpoints._();

  // ==================== 基础URL ====================
  /// 测试环境基础URL
  static const String testBaseUrl = "https://mobileappt01.amkor.com.cn:4433/api";
  
  /// 生产环境基础URL
  static const String productionBaseUrl = "https://mobileappp01.amkor.com.cn/api";
  
  /// DMZ测试环境URL
  static const String dmzTestUrl = "https://msodmztest.amkor.com.cn:4433/api";

  // ==================== 认证相关 ====================
  /// 登录端点
  static const String login = "/auth/login";
  
  /// 刷新Token端点
  static const String refreshToken = "/auth/refresh";
  
  /// 登出端点
  static const String logout = "/auth/logout";

  // ==================== 用户相关 ====================
  /// 用户信息端点
  static const String userInfo = "/user/info";
  
  /// 更新用户信息端点
  static const String updateUserInfo = "/user/update";

  // ==================== 文件相关 ====================
  /// 文件上传端点
  static const String fileUpload = "/file/upload";
  
  /// 文件下载端点
  static const String fileDownload = "/file/download";
}

/// 应用配置常量
class AppConfig {
  AppConfig._();

  // ==================== 应用信息 ====================
  /// 应用名称
  static const String appName = "Amkor MSO";
  
  /// 应用包名
  static const String packageName = "com.amkor.mso";
  
  /// 应用版本检查间隔
  static const Duration versionCheckInterval = Duration(hours: 24);

  // ==================== 功能开关 ====================
  /// 是否启用水印
  static const bool enableWatermark = true;
  
  /// 是否启用安全模式
  static const bool enableSecureMode = true;
  
  /// 是否启用推送通知
  static const bool enablePushNotification = true;

  // ==================== 缓存配置 ====================
  /// 缓存过期时间
  static const Duration cacheExpiration = Duration(hours: 1);
  
  /// 最大缓存大小 (50MB)
  static const int maxCacheSize = 50 * 1024 * 1024;
}

/// 错误消息常量
class ErrorMessages {
  ErrorMessages._();

  // ==================== 网络错误 ====================
  static const String networkError = "网络连接失败，请检查网络设置";
  static const String timeoutError = "请求超时，请稍后重试";
  static const String serverError = "服务器错误，请稍后重试";

  // ==================== 权限错误 ====================
  static const String permissionDenied = "权限被拒绝";
  static const String permissionPermanentlyDenied = "权限被永久拒绝，请在设置中手动开启";

  // ==================== 文件错误 ====================
  static const String fileNotFound = "文件不存在";
  static const String fileTooLarge = "文件过大";
  static const String downloadFailed = "下载失败";

  // ==================== 通用错误 ====================
  static const String unknownError = "未知错误";
  static const String operationFailed = "操作失败";
  static const String invalidInput = "输入无效";
}

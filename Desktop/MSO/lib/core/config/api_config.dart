import 'package:flutter/foundation.dart';
import '../constants/app_constants.dart';

/// API配置管理类
/// 根据环境自动选择合适的API配置
class ApiConfig {
  // 私有构造函数
  ApiConfig._();

  /// 当前环境标识
  static String get environmentLabel {
    if (kDebugMode) {
      return AppConstants.testEnvironmentLabel;
    } else {
      // 可以根据需要切换到生产环境
      return AppConstants.testEnvironmentLabel; // 当前设置为测试环境
      // return AppConstants.productionEnvironmentLabel; // 切换到生产环境时取消注释
    }
  }

  /// 获取当前环境的基础URL
  static String get baseUrl {
    if (kDebugMode) {
      // 开发环境 - 可以根据需要选择不同的测试服务器
      return _getDebugUrl();
    } else {
      // 生产环境 - 当前仍使用测试环境
      return ApiEndpoints.testBaseUrl;
      // return ApiEndpoints.productionBaseUrl; // 切换到生产环境时取消注释
    }
  }

  /// 获取调试环境URL
  static String _getDebugUrl() {
    // 可以根据需要切换不同的开发服务器
    
    // 主要测试服务器
    return ApiEndpoints.testBaseUrl;
    
    // 其他可选的开发服务器（根据需要取消注释）
    // return "http://************:8080/api"; // Jim 设备绑定
    // return "http://************:5283/api"; // Justin 设备绑定
    // return "https://mobileappt01.amkor.com.cn:4433/api_web"; // 测试设备绑定
    // return ApiEndpoints.dmzTestUrl; // DMZ 测试
    // return "http://10.86.72.20/api"; // 迁移SQL test
    // return "https://atcmsoapi.amkor.com/api"; // 短信
    // return "http://10.86.81.238:8088/api"; // ATC员工和供应商区分权限
    // return "http://10.20.1.131:8088/api";
  }

  /// 是否为测试环境
  static bool get isTestEnvironment {
    return baseUrl.contains('test') || baseUrl.contains('t01') || kDebugMode;
  }

  /// 是否为生产环境
  static bool get isProductionEnvironment {
    return !isTestEnvironment;
  }

  /// 获取完整的API URL
  static String getApiUrl(String controller, String action) {
    return "$baseUrl/$controller/$action";
  }

  /// 获取请求头配置
  static Map<String, String> get defaultHeaders {
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
  }

  /// 获取带Token的请求头
  static Map<String, String> getAuthHeaders(String token) {
    return {
      ...defaultHeaders,
      'Authorization': 'Bearer $token',
    };
  }

  /// 超时配置
  static Duration get connectTimeout => AppConstants.connectTimeout;
  static Duration get receiveTimeout => AppConstants.requestTimeout;
  static Duration get sendTimeout => AppConstants.requestTimeout;

  /// 打印当前配置信息（仅在调试模式下）
  static void printConfig() {
    if (kDebugMode) {
      debugPrint('=== API Configuration ===');
      debugPrint('Environment: $environmentLabel');
      debugPrint('Base URL: $baseUrl');
      debugPrint('Is Test Environment: $isTestEnvironment');
      debugPrint('Connect Timeout: ${connectTimeout.inSeconds}s');
      debugPrint('Receive Timeout: ${receiveTimeout.inSeconds}s');
      debugPrint('========================');
    }
  }
}

/// 环境配置枚举
enum Environment {
  development,
  testing,
  staging,
  production,
}

/// 环境配置扩展
extension EnvironmentExtension on Environment {
  /// 获取环境名称
  String get name {
    switch (this) {
      case Environment.development:
        return 'Development';
      case Environment.testing:
        return 'Testing';
      case Environment.staging:
        return 'Staging';
      case Environment.production:
        return 'Production';
    }
  }

  /// 获取环境对应的基础URL
  String get baseUrl {
    switch (this) {
      case Environment.development:
        return "http://************:8080/api"; // 开发服务器
      case Environment.testing:
        return ApiEndpoints.testBaseUrl;
      case Environment.staging:
        return ApiEndpoints.dmzTestUrl;
      case Environment.production:
        return ApiEndpoints.productionBaseUrl;
    }
  }

  /// 是否启用调试功能
  bool get enableDebug {
    switch (this) {
      case Environment.development:
      case Environment.testing:
        return true;
      case Environment.staging:
      case Environment.production:
        return false;
    }
  }
}

/// 服务器配置类
class ServerConfig {
  final String name;
  final String url;
  final String description;
  final bool isActive;

  const ServerConfig({
    required this.name,
    required this.url,
    required this.description,
    this.isActive = false,
  });

  /// 预定义的服务器配置
  static const List<ServerConfig> availableServers = [
    ServerConfig(
      name: 'Production',
      url: ApiEndpoints.productionBaseUrl,
      description: '生产环境服务器',
    ),
    ServerConfig(
      name: 'Test',
      url: ApiEndpoints.testBaseUrl,
      description: '测试环境服务器',
      isActive: true,
    ),
    ServerConfig(
      name: 'DMZ Test',
      url: ApiEndpoints.dmzTestUrl,
      description: 'DMZ测试环境服务器',
    ),
    ServerConfig(
      name: 'Jim Dev',
      url: 'http://************:8080/api',
      description: 'Jim开发服务器 - 设备绑定',
    ),
    ServerConfig(
      name: 'Justin Dev',
      url: 'http://************:5283/api',
      description: 'Justin开发服务器 - 设备绑定',
    ),
  ];

  /// 获取当前激活的服务器配置
  static ServerConfig get activeServer {
    return availableServers.firstWhere(
      (server) => server.isActive,
      orElse: () => availableServers.first,
    );
  }
}

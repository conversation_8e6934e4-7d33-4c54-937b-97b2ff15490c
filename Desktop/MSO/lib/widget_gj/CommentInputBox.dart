import 'package:amkor/services/employeeService.dart';
import 'package:amkor/widget_gj/My_Icons.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class InputButtomWidget extends StatelessWidget {

  final ValueChanged? onEditingCompleteText;
  final TextEditingController controller = TextEditingController();
  int? id;
  InputButtomWidget({Key? key, this.onEditingCompleteText,this.id}) : super(key: key);


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Column(
        children: <Widget>[
          Expanded(
              child:  GestureDetector(
                child:  Container(
                  color: Colors.transparent,
                ),
                onTap: () {
                  Navigator.pop(context);
                },
              )
          ),
          Container(
              color: const Color(0xFFF4F4F4),
              padding: const EdgeInsets.only(left: 16,top: 8,bottom: 8,right: 16),
              child: Row(
                children: [
                  Expanded(
                    flex: 9,
                      child: Container(
                        decoration:  BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: TextField(
                          controller: controller,
                          autofocus: true,
                          style: const TextStyle(
                              fontSize: 16
                          ),
                          //设置键盘按钮为发送
                          textInputAction: TextInputAction.send,
                          keyboardType: TextInputType.multiline,
                          onEditingComplete: ()  {
                            //点击发送调用
                            print('onEditingComplete');
                            onEditingCompleteText!(controller.text);
                            QuestionApp().AskQuestion(controller.text,id!,);
                            Navigator.pop(context);

                          },
                          decoration: const InputDecoration(
                            hintText: '请输入评论的内容',
                            isDense: true,
                            icon:Icon(MyIcons.write,color: Colors.grey,size: 20,),
                            contentPadding:EdgeInsets.only(left: -10,top: 5,bottom: 5,right: 10),
                            border:OutlineInputBorder(
                              gapPadding: 0,
                              borderSide: BorderSide(
                                width: 0,
                                style: BorderStyle.none,
                              ),
                            ),

                          ),
                          minLines: 1,
                          maxLines: 5,
                        ),
                      )
                  ),
                  Expanded(
                    flex: 1,
                    child: GestureDetector(
                      onTap: () {
                        //点击发送调用
                        print('onEditingComplete');
                        onEditingCompleteText!(controller.text);
                        QuestionApp().AskQuestion(controller.text,id!,);
                        Navigator.pop(context);
                      },
                      child:const Icon(MyIcons.send_out,color: Colors.grey,size: 22,),
                    ),
                  ),

                ],
              )


          )

        ],
      ),
    );
  }
}

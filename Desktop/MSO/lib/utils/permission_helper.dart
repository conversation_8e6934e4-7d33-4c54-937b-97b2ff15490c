import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';

/// Result class for permission operations
class PermissionResult {
  final bool isGranted;
  final List<Permission> deniedPermissions;
  final List<Permission> permanentlyDeniedPermissions;
  final String? error;

  const PermissionResult({
    required this.isGranted,
    required this.deniedPermissions,
    required this.permanentlyDeniedPermissions,
    this.error,
  });

  /// Returns true if all permissions are granted
  bool get hasAllPermissions => isGranted && deniedPermissions.isEmpty && permanentlyDeniedPermissions.isEmpty;

  /// Returns true if any permissions are permanently denied
  bool get hasPermanentlyDeniedPermissions => permanentlyDeniedPermissions.isNotEmpty;

  /// Returns true if any permissions are denied but not permanently
  bool get hasDeniedPermissions => deniedPermissions.isNotEmpty;
}

/// Android 13兼容的权限管理工具类
/// 
/// 这个类处理Android 13引入的新权限模型，包括：
/// - Android 13+: 使用READ_MEDIA_IMAGES, READ_MEDIA_VIDEO, READ_MEDIA_AUDIO
/// - Android 11-12: 使用scoped storage和传统权限的混合
/// - Android 10及以下: 使用传统的READ_EXTERNAL_STORAGE权限
/// - iOS: 使用照片库权限
class PermissionHelper {
  
  /// 检查并请求存储相关权限
  /// 根据Android版本自动选择合适的权限策略
  /// [includeApkInstall] 是否包含APK安装权限（默认false，减少敏感权限请求）
  static Future<PermissionResult> checkAndRequestStoragePermissions({
    bool includeApkInstall = false,
  }) async {
    try {
      if (Platform.isAndroid) {
        final deviceInfo = await DeviceInfoPlugin().androidInfo;
        final androidVersion = deviceInfo.version.sdkInt ?? 0;

        if (androidVersion >= 33) {
          // Android 13+ (API 33+) - 使用新的媒体权限
          return await _checkAndroid13Permissions(includeApkInstall);
        } else if (androidVersion >= 30) {
          // Android 11-12 (API 30-32) - 使用scoped storage但仍需要一些传统权限
          return await _checkAndroid11Permissions(includeApkInstall);
        } else {
          // Android 10及以下 - 使用传统存储权限
          return await _checkLegacyStoragePermissions(includeApkInstall);
        }
      } else if (Platform.isIOS) {
        // iOS权限处理
        return await _checkIOSPermissions();
      }
    } catch (e) {
      return PermissionResult(
        isGranted: false,
        deniedPermissions: [],
        permanentlyDeniedPermissions: [],
        error: 'Failed to check permissions: $e',
      );
    }

    return PermissionResult(
      isGranted: false,
      deniedPermissions: [],
      permanentlyDeniedPermissions: [],
      error: 'Unsupported platform',
    );
  }

  /// Android 13+ 权限检查
  static Future<PermissionResult> _checkAndroid13Permissions(bool includeApkInstall) async {
    List<Permission> permissionsToRequest = [];
    List<Permission> deniedPermissions = [];
    List<Permission> permanentlyDeniedPermissions = [];

    // 检查媒体权限 - Android 13使用新的细分权限
    final photoStatus = await Permission.photos.status;
    final videoStatus = await Permission.videos.status;

    if (!photoStatus.isGranted) {
      permissionsToRequest.add(Permission.photos);
    }
    if (!videoStatus.isGranted) {
      permissionsToRequest.add(Permission.videos);
    }

    // APK安装功能需要的权限（仅在需要时请求）
    if (includeApkInstall) {
      final installPackagesStatus = await Permission.requestInstallPackages.status;
      if (!installPackagesStatus.isGranted) {
        permissionsToRequest.add(Permission.requestInstallPackages);
      }

      // 注意：MANAGE_EXTERNAL_STORAGE是敏感权限，仅在确实需要时使用
      // 对于大多数APK安装场景，可以使用应用私有目录或Downloads目录
      // final manageStorageStatus = await Permission.manageExternalStorage.status;
      // if (!manageStorageStatus.isGranted) {
      //   permissionsToRequest.add(Permission.manageExternalStorage);
      // }
    }

    // 如果应用需要音频文件访问权限，可以添加：
    // final audioStatus = await Permission.audio.status;
    // if (!audioStatus.isGranted) {
    //   permissionsToRequest.add(Permission.audio);
    // }

    if (permissionsToRequest.isNotEmpty) {
      Map<Permission, PermissionStatus> statuses = await permissionsToRequest.request();

      // 分析权限请求结果
      for (var entry in statuses.entries) {
        if (entry.value.isDenied) {
          deniedPermissions.add(entry.key);
        } else if (entry.value.isPermanentlyDenied) {
          permanentlyDeniedPermissions.add(entry.key);
        }
      }

      final allGranted = statuses.values.every((status) => status.isGranted);
      return PermissionResult(
        isGranted: allGranted,
        deniedPermissions: deniedPermissions,
        permanentlyDeniedPermissions: permanentlyDeniedPermissions,
      );
    }

    return const PermissionResult(
      isGranted: true,
      deniedPermissions: [],
      permanentlyDeniedPermissions: [],
    );
  }

  /// Android 11-12 权限检查
  static Future<PermissionResult> _checkAndroid11Permissions(bool includeApkInstall) async {
    List<Permission> permissionsToRequest = [];
    List<Permission> deniedPermissions = [];
    List<Permission> permanentlyDeniedPermissions = [];

    // 检查存储权限
    final storageStatus = await Permission.storage.status;
    if (!storageStatus.isGranted) {
      permissionsToRequest.add(Permission.storage);
    }

    // 检查媒体权限（如果可用）
    final photoStatus = await Permission.photos.status;
    if (!photoStatus.isGranted) {
      permissionsToRequest.add(Permission.photos);
    }

    // APK安装权限（仅在需要时请求）
    if (includeApkInstall) {
      final installPackagesStatus = await Permission.requestInstallPackages.status;
      if (!installPackagesStatus.isGranted) {
        permissionsToRequest.add(Permission.requestInstallPackages);
      }
    }

    if (permissionsToRequest.isNotEmpty) {
      Map<Permission, PermissionStatus> statuses = await permissionsToRequest.request();

      // 分析权限请求结果
      for (var entry in statuses.entries) {
        if (entry.value.isDenied) {
          deniedPermissions.add(entry.key);
        } else if (entry.value.isPermanentlyDenied) {
          permanentlyDeniedPermissions.add(entry.key);
        }
      }

      final allGranted = statuses.values.every((status) => status.isGranted);
      return PermissionResult(
        isGranted: allGranted,
        deniedPermissions: deniedPermissions,
        permanentlyDeniedPermissions: permanentlyDeniedPermissions,
      );
    }

    return const PermissionResult(
      isGranted: true,
      deniedPermissions: [],
      permanentlyDeniedPermissions: [],
    );
  }

  /// Android 10及以下权限检查
  static Future<PermissionResult> _checkLegacyStoragePermissions(bool includeApkInstall) async {
    List<Permission> permissionsToRequest = [];
    List<Permission> deniedPermissions = [];
    List<Permission> permanentlyDeniedPermissions = [];

    // 检查存储权限
    final storageStatus = await Permission.storage.status;
    if (!storageStatus.isGranted) {
      permissionsToRequest.add(Permission.storage);
    }

    // APK安装权限（仅在需要时请求）
    if (includeApkInstall) {
      final installPackagesStatus = await Permission.requestInstallPackages.status;
      if (!installPackagesStatus.isGranted) {
        permissionsToRequest.add(Permission.requestInstallPackages);
      }
    }

    if (permissionsToRequest.isNotEmpty) {
      Map<Permission, PermissionStatus> statuses = await permissionsToRequest.request();

      // 分析权限请求结果
      for (var entry in statuses.entries) {
        if (entry.value.isDenied) {
          deniedPermissions.add(entry.key);
        } else if (entry.value.isPermanentlyDenied) {
          permanentlyDeniedPermissions.add(entry.key);
        }
      }

      final allGranted = statuses.values.every((status) => status.isGranted);
      return PermissionResult(
        isGranted: allGranted,
        deniedPermissions: deniedPermissions,
        permanentlyDeniedPermissions: permanentlyDeniedPermissions,
      );
    }

    return const PermissionResult(
      isGranted: true,
      deniedPermissions: [],
      permanentlyDeniedPermissions: [],
    );
  }

  /// iOS权限检查
  static Future<PermissionResult> _checkIOSPermissions() async {
    final photoStatus = await Permission.photos.status;

    if (!photoStatus.isGranted) {
      final result = await Permission.photos.request();

      if (result.isGranted) {
        return const PermissionResult(
          isGranted: true,
          deniedPermissions: [],
          permanentlyDeniedPermissions: [],
        );
      } else if (result.isPermanentlyDenied) {
        return PermissionResult(
          isGranted: false,
          deniedPermissions: const [],
          permanentlyDeniedPermissions: [Permission.photos],
        );
      } else {
        return PermissionResult(
          isGranted: false,
          deniedPermissions: [Permission.photos],
          permanentlyDeniedPermissions: const [],
        );
      }
    }

    return const PermissionResult(
      isGranted: true,
      deniedPermissions: [],
      permanentlyDeniedPermissions: [],
    );
  }

  /// 显示权限解释对话框
  static void showPermissionExplanationDialog(
    BuildContext context, 
    Permission permission,
    VoidCallback onRetry
  ) {
    String permissionName = _getPermissionName(permission);
    String explanation = _getPermissionExplanation(permission);
    
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('需要$permissionName权限'),
          content: Text(explanation),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                onRetry();
              },
              child: const Text('重新授权'),
            ),
          ],
        );
      },
    );
  }

  /// 显示设置页面引导对话框
  static void showPermissionSettingsDialog(
    BuildContext context, 
    Permission permission
  ) {
    String permissionName = _getPermissionName(permission);
    
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('权限被永久拒绝'),
          content: Text('$permissionName权限已被永久拒绝，请在设置中手动开启。'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                openAppSettings();
              },
              child: const Text('去设置'),
            ),
          ],
        );
      },
    );
  }

  /// 获取权限名称
  static String _getPermissionName(Permission permission) {
    if (permission == Permission.storage) {
      return '存储';
    } else if (permission == Permission.photos) {
      return '照片';
    } else if (permission == Permission.videos) {
      return '视频';
    } else if (permission == Permission.audio) {
      return '音频';
    } else if (permission == Permission.manageExternalStorage) {
      return '文件管理';
    } else if (permission == Permission.requestInstallPackages) {
      return '应用安装';
    } else {
      return '未知';
    }
  }

  /// 获取权限解释
  static String _getPermissionExplanation(Permission permission) {
    if (permission == Permission.storage) {
      return '应用需要存储权限来保存和读取文件。';
    } else if (permission == Permission.photos) {
      return '应用需要照片权限来访问和上传图片。';
    } else if (permission == Permission.videos) {
      return '应用需要视频权限来访问和上传视频文件。';
    } else if (permission == Permission.audio) {
      return '应用需要音频权限来访问和播放音频文件。';
    } else if (permission == Permission.manageExternalStorage) {
      return '应用需要文件管理权限来访问设备上的所有文件。';
    } else if (permission == Permission.requestInstallPackages) {
      return '应用需要安装权限来安装应用更新包。';
    } else {
      return '应用需要此权限来正常运行。';
    }
  }

  /// 专门检查APK安装权限
  /// 这是一个轻量级的方法，只请求APK安装必需的权限
  static Future<PermissionResult> checkAndRequestApkInstallPermissions() async {
    try {
      if (Platform.isAndroid) {
        final installPackagesStatus = await Permission.requestInstallPackages.status;

        if (!installPackagesStatus.isGranted) {
          final result = await Permission.requestInstallPackages.request();

          if (result.isGranted) {
            return const PermissionResult(
              isGranted: true,
              deniedPermissions: [],
              permanentlyDeniedPermissions: [],
            );
          } else if (result.isPermanentlyDenied) {
            return const PermissionResult(
              isGranted: false,
              deniedPermissions: [],
              permanentlyDeniedPermissions: [Permission.requestInstallPackages],
            );
          } else {
            return const PermissionResult(
              isGranted: false,
              deniedPermissions: [Permission.requestInstallPackages],
              permanentlyDeniedPermissions: [],
            );
          }
        }

        return const PermissionResult(
          isGranted: true,
          deniedPermissions: [],
          permanentlyDeniedPermissions: [],
        );
      }
    } catch (e) {
      return PermissionResult(
        isGranted: false,
        deniedPermissions: const [],
        permanentlyDeniedPermissions: const [],
        error: 'Failed to check APK install permissions: $e',
      );
    }

    return const PermissionResult(
      isGranted: false,
      deniedPermissions: [],
      permanentlyDeniedPermissions: [],
      error: 'APK installation not supported on this platform',
    );
  }

  /// 检查特定权限状态
  static Future<PermissionStatus> checkPermissionStatus(Permission permission) async {
    return await permission.status;
  }

  /// 检查是否应该显示权限说明
  static Future<bool> shouldShowRequestRationale(Permission permission) async {
    if (Platform.isAndroid) {
      return await permission.shouldShowRequestRationale;
    }
    return false;
  }
}

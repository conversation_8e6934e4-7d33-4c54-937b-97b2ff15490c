import 'dart:convert';

import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart';
import 'package:pointycastle/export.dart';
import 'package:pointycastle/api.dart' as pc;
import 'dart:typed_data';
class MyCrypt {
  static String md5Encrypt(String oldStr) {
    var bytes = utf8.encode(oldStr);
    var md5Val = md5.convert(bytes);
    // print("origan is $oldStr,  crypt res is ${md5Val.toString()}");
    return md5Val.toString();
  }

  static String aesEncrypt(String oldStr) {
    const String _aesKey = "46391F1DFSB793A7B5FFF9E311FC63AC";
    final IV _aesIv = IV.fromUtf8("769D2894ABC23AFS");
    final key = Key.fromUtf8(_aesKey);
    var encrypter = Encrypter(AES(key, mode: AESMode.cbc));
    final encrypted = encrypter.encrypt(oldStr, iv: _aesIv);

    // print("origan is $oldStr,  crypt res is ${encrypted.base64}");

    return encrypted.base64;
  }

  static String pwdEncrypt(String origanPwd) {
    var md5Pwd = md5Encrypt(origanPwd);
    return aesEncrypt(md5Pwd);
  }

  static String encryptData(String plainText) {

   final key = Uint8List.fromList(utf8.encode('VNDTJQEw9T8ET66Azw9ho5ZnpghzvE6j')); // 32 bytes for AES-256
   final iv = Uint8List.fromList(utf8.encode('28066B5BDACA'));

   //final plainText = oldStr;
   final plainTextBytes = Uint8List.fromList(utf8.encode(plainText));

   final cipher = GCMBlockCipher(AESEngine())
     ..init(true, AEADParameters(KeyParameter(key), 128, iv, Uint8List(0))); // 128-bit tag length

   final encryptedBytes = cipher.process(plainTextBytes);

   final tag = encryptedBytes.sublist(encryptedBytes.length - 16); // Last 16 bytes are the tag

   // 计算总长度
   int totalLength = iv.length + encryptedBytes.length;

   // 创建一个新的 Uint8List 来存储拼接后的数据
   Uint8List result = Uint8List(totalLength);

   // 将第一个 Uint8List 的数据复制到结果中
   result.setRange(0, iv.length, iv);

   // 将第二个 Uint8List 的数据复制到结果中
   result.setRange(iv.length, totalLength, encryptedBytes);

   return base64.encode(result);

  }

}

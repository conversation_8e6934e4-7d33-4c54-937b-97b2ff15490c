import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:open_file/open_file.dart';
import 'package:dio/dio.dart';
import 'permission_helper.dart';

/// APK安装工具类
/// 
/// 这个类提供了一个轻量级的APK安装解决方案，避免使用敏感的MANAGE_EXTERNAL_STORAGE权限
/// 
/// 特点：
/// - 只请求必需的REQUEST_INSTALL_PACKAGES权限
/// - 使用应用私有目录存储APK文件，无需额外存储权限
/// - 提供下载进度回调
/// - 自动处理权限请求和错误情况
class ApkInstaller {
  
  /// 下载并安装APK
  /// 
  /// [url] APK下载链接
  /// [context] 用于显示对话框的上下文
  /// [onProgress] 下载进度回调 (已下载字节数, 总字节数)
  /// [fileName] 可选的文件名，默认从URL提取
  /// 
  /// 返回安装结果：true表示成功启动安装，false表示失败
  static Future<bool> downloadAndInstallApk({
    required String url,
    required BuildContext context,
    Function(int received, int total)? onProgress,
    String? fileName,
  }) async {
    try {
      // 1. 检查APK安装权限
      final permissionResult = await PermissionHelper.checkAndRequestApkInstallPermissions();
      
      if (!permissionResult.isGranted) {
        _showPermissionErrorDialog(context, permissionResult);
        return false;
      }

      // 2. 获取应用私有目录（无需额外权限）
      final directory = await getApplicationDocumentsDirectory();
      final apkFileName = fileName ?? _extractFileNameFromUrl(url);
      final filePath = '${directory.path}/$apkFileName';

      // 3. 下载APK文件
      final success = await _downloadFile(
        url: url,
        savePath: filePath,
        onProgress: onProgress,
      );

      if (!success) {
        _showErrorDialog(context, '下载失败', '无法下载APK文件，请检查网络连接。');
        return false;
      }

      // 4. 安装APK
      return await _installApk(context, filePath);

    } catch (e) {
      _showErrorDialog(context, '安装失败', '安装过程中发生错误：$e');
      return false;
    }
  }

  /// 直接安装本地APK文件
  /// 
  /// [filePath] APK文件路径
  /// [context] 用于显示对话框的上下文
  /// 
  /// 返回安装结果：true表示成功启动安装，false表示失败
  static Future<bool> installLocalApk({
    required String filePath,
    required BuildContext context,
  }) async {
    try {
      // 检查APK安装权限
      final permissionResult = await PermissionHelper.checkAndRequestApkInstallPermissions();
      
      if (!permissionResult.isGranted) {
        _showPermissionErrorDialog(context, permissionResult);
        return false;
      }

      // 检查文件是否存在
      final file = File(filePath);
      if (!await file.exists()) {
        _showErrorDialog(context, '文件不存在', '找不到APK文件：$filePath');
        return false;
      }

      return await _installApk(context, filePath);

    } catch (e) {
      _showErrorDialog(context, '安装失败', '安装过程中发生错误：$e');
      return false;
    }
  }

  /// 下载文件
  static Future<bool> _downloadFile({
    required String url,
    required String savePath,
    Function(int received, int total)? onProgress,
  }) async {
    try {
      final dio = Dio();
      
      await dio.download(
        url,
        savePath,
        onReceiveProgress: onProgress,
      );
      
      return true;
    } catch (e) {
      debugPrint('下载文件失败: $e');
      return false;
    }
  }

  /// 安装APK文件
  static Future<bool> _installApk(BuildContext context, String filePath) async {
    try {
      final result = await OpenFile.open(filePath);
      
      if (result.type == ResultType.done) {
        return true;
      } else {
        _showErrorDialog(context, '安装失败', '无法打开APK文件：${result.message}');
        return false;
      }
    } catch (e) {
      _showErrorDialog(context, '安装失败', '打开APK文件时发生错误：$e');
      return false;
    }
  }

  /// 从URL提取文件名
  static String _extractFileNameFromUrl(String url) {
    final uri = Uri.parse(url);
    final segments = uri.pathSegments;
    
    if (segments.isNotEmpty) {
      final fileName = segments.last;
      if (fileName.endsWith('.apk')) {
        return fileName;
      }
    }
    
    // 如果无法从URL提取文件名，使用默认名称
    return 'app_update_${DateTime.now().millisecondsSinceEpoch}.apk';
  }

  /// 显示权限错误对话框
  static void _showPermissionErrorDialog(BuildContext context, PermissionResult result) {
    String message;
    VoidCallback? action;

    if (result.hasPermanentlyDeniedPermissions) {
      message = '应用安装权限已被永久拒绝，请在设置中手动开启。';
      action = () {
        Navigator.of(context).pop();
        openAppSettings();
      };
    } else {
      message = '需要应用安装权限才能安装APK文件。';
      action = () {
        Navigator.of(context).pop();
        // 可以在这里重新尝试权限请求
      };
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('权限需求'),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            if (action != null)
              TextButton(
                onPressed: action,
                child: Text(result.hasPermanentlyDeniedPermissions ? '去设置' : '重试'),
              ),
          ],
        );
      },
    );
  }

  /// 显示通用错误对话框
  static void _showErrorDialog(BuildContext context, String title, String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }

  /// 清理下载的APK文件
  /// 
  /// 可以在安装完成后调用此方法清理临时文件
  static Future<void> cleanupDownloadedApks() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final dir = Directory(directory.path);
      
      await for (final entity in dir.list()) {
        if (entity is File && entity.path.endsWith('.apk')) {
          await entity.delete();
          debugPrint('已删除APK文件: ${entity.path}');
        }
      }
    } catch (e) {
      debugPrint('清理APK文件失败: $e');
    }
  }
}

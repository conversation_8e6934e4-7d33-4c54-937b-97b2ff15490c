//轮播图
import 'dart:async';

import 'package:flutter/material.dart';


class SlideBanner extends StatefulWidget {
   SlideBanner({Key? key ,required this.list,this.rollingDirection=true,this.manualControl=false}) : super(key: key);
   ///图片数据列表
  List<String> list;
  ///轮播图方向
   bool rollingDirection;
   ///启用手动控制
   bool manualControl;
  @override
  _SlideBannerState createState() => _SlideBannerState();
}

class _SlideBannerState extends State<SlideBanner> {
  List<String> list =[];
  double dxy = 0;//距离
  int curr = 0;//要移出的下标
  int next = 0;//要移入的下标
  bool toTop = true;//自动播放的方向
  Timer? timer;
  Timer? timer2;
  /** 轮播图滑动相关 **/
  dragStart(Offset offset) {
    dxy = 0;
  }

  //累计位移距离
  dragUpdate(Offset offset) {
    double x;
    x= widget.rollingDirection?offset.dy:offset.dx;
    dxy += x;
  }

  //达到一定距离后则触发轮播图滑动
  dragEnd(Velocity v) {
    print("-===============$dxy");
    if (dxy.abs() < 20) return;

    timer?.cancel();
    if (dxy < 0) {
      //逆
      if (!toTop) {
        setState(() {
          toTop = true;
          curr = next - 1 < 0 ? list.length - 1 : next - 1;
        });
      }
      setState(() {
        curr = next;
        next = (++next) % list.length;
      });
    } else {
      //顺
      if (toTop) {
        setState(() {
          toTop = false;
          curr = (next + 1) % list.length;
        });
      }
      setState(() {
        curr = next;
        next = --next < 0 ? list.length - 1 : next;
      });
    }
    //setTimeout
    // timer = Timer(Duration(seconds: 4), autoPlay);
  }

  autoPlays() {
    var second = const Duration(seconds: 0);
    timer2 = Timer.periodic(second, (t) {
      setState(() {
        toTop = true;
        curr = next;
        next = (++next) % list.length;
      });
      t.cancel();
    });

  }
  autoPlay() {
    var second = const Duration(seconds: 4);
    timer = Timer.periodic(second, (t) {
      setState(() {
        toTop = true;
        curr = next;
        next = (++next) % list.length;
      });
    });

  }

  @override
  void initState() {
    super.initState();
    list =widget.list;
    if(!widget.manualControl) {
      autoPlays();
      autoPlay();
    }

    // timer = Timer(Duration(seconds: 4), autoPlay);
  }

  @override
  void dispose() {
    if (timer != null) timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final width = size.width;
    final height = size.height;
    // print("-=-=--==-=-=-=-=-$");
    return Scaffold(
        backgroundColor: Colors.transparent,
        body: GestureDetector(
            onVerticalDragStart:widget.manualControl?(details) => dragStart(details.globalPosition):null,
            onVerticalDragUpdate:widget.manualControl? (details) => dragUpdate(details.delta):null,
            onVerticalDragEnd:widget.manualControl? (details) => dragEnd(details.velocity):null,
            onHorizontalDragStart:widget.manualControl? (details) => dragStart(details.globalPosition):null,
            onHorizontalDragUpdate:widget.manualControl? (details) => dragUpdate(details.delta):null,
            onHorizontalDragEnd: widget.manualControl?(details) => dragEnd(details.velocity):null,
            child: Stack(
              alignment: Alignment.center,
                children: [
                  for(int i=0;i<list.length;i++)
                  AnimatedContainer(
                      duration: Duration(milliseconds: (i == next || i == curr) ? 4000 : 0),
                      // curve: Curves.easeIn,
                      transform:widget.rollingDirection? Matrix4.translationValues(
                          0,i == next ? 0 : i == curr ? (toTop ? -height : height) : (toTop ? height : -height),  0):
                      Matrix4.translationValues(
                          i == next ? 0 : i == curr ? (toTop ? -width : width) : (toTop ? width : -width),0,  0),
                      width: width,
                      height: height,
                      child: Image.asset(list[i],fit: BoxFit.cover)),
                  // Positioned(
                  //     child:Row(
                  //       mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  //       children: [
                  //         IconButton(
                  //           onPressed: () {
                  //             if (!toTop) {
                  //               setState(() {
                  //                 toTop = true;
                  //                 curr = next - 1 < 0 ? list.length - 1 : next - 1;
                  //               });
                  //             }
                  //             setState(() {
                  //               curr = next;
                  //               next = (++next) % list.length;
                  //             });
                  //             },
                  //           icon: const Icon(
                  //             Icons.arrow_back_ios,
                  //             color: Colors.black,
                  //             size: 22,
                  //           ),
                  //         ),
                  //         IconButton(
                  //           onPressed: (){
                  //             if (toTop) {
                  //               setState(() {
                  //                 toTop = false;
                  //                 curr = (next + 1) % list.length;
                  //               });
                  //             }
                  //             setState(() {
                  //               curr = next;
                  //               next = --next < 0 ? list.length - 1 : next;
                  //             });
                  //           },
                  //           icon: const Icon(
                  //             Icons.arrow_forward_ios,
                  //             color: Colors.black,
                  //             size: 22,
                  //           ),
                  //         ),
                  //       ],
                  //     )
                  // ),
                ]

                    //  list
                    // .asMap()
                    // .keys
                    // .map((i) => AnimatedContainer(
                    // duration: Duration(milliseconds: (i == next || i == curr) ? 4000 : 0),
                    // // curve: Curves.easeIn,
                    // transform:widget.rollingDirection? Matrix4.translationValues(
                    //     0,i == next ? 0 : i == curr ? (toTop ? -height : height) : (toTop ? height : -height),  0):
                    // Matrix4.translationValues(
                    //     i == next ? 0 : i == curr ? (toTop ? -width : width) : (toTop ? width : -width),0,  0),
                    // width: width,
                    // height: height,
                    // child: Image.asset(list[i], width: width, height:height ,fit: BoxFit.cover)))
                    // .toList()
            )

        ));
  }
}





import 'package:flutter/material.dart';

/// 全局键管理类
/// 用于管理应用中的全局导航键和上下文
class GlobalKeys {
  /// 全局导航键，用于在没有BuildContext的地方进行导航
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  /// 当前上下文，通过navigatorKey获取
  static BuildContext? get currentContext => navigatorKey.currentContext;

  /// 已弃用：直接使用currentContext
  @Deprecated('Use currentContext instead')
  static BuildContext? context;
}



import 'dart:io';

import 'package:dio/dio.dart';

class Global {

  List<Map<String,dynamic>> homeData1= [
    {"类型":"单选","选项": ["是", "否",  ],"题目":"1.被推荐人是否在上海（被推荐人不在上海先不要填写推荐表，等人来上海了再扫描推荐）"},
    {"类型":"单选","选项": ["重点区域：从抵沪日期起隔离14天，期间2次核酸检测（必须在上海检测，且两次检测中间间隔24小时），结果均为阴性", "受限制区域：一次核酸检测（必须在上海检测），结果为阴性","不受限制区域：无特殊管控要求"  ],"题目":"2.根据安靠公司的疫情防控管理规定，国家列为高、中风险区域的所在城市以及境外（包括中国香港、中国台湾在内）区域均在安靠被视为重点区域，包括黑龙江、吉林、辽宁、新疆、内蒙古、西藏、甘肃、云南、广西、广东在内的省份列为受限制区域，其他区域列为不受限制区域。安靠公司对不同区域设置不同的管理规定，请确认您在过去14天是否途经："},
    {"类型":"单选","选项": ["是,愿意排队接受等待", "不需要",  ],"题目":"3.被推荐人是否需要安排住宿（宿舍安排需要按照入职当天的床位情况，可能需要排队等待）"},
    {"类型":"单选","选项": ["是,有可能等待时间较长", "否",  ],"题目":"4.是否必须应聘到指定部门"},
    {"类型":"单选","选项": ["初中", "高中","专科","本科","硕士及以上"  ],"题目":"5.被推荐人最高学历（举例：高中）"},
    {"类型":"单选","选项": ["亲戚", "朋友",  ],"题目":"6.与被推荐人关系"},
    {"类型":"单选","选项": ["是，并要求还是回去原部门","是，可以接受随机安排部门", "否",  ],"题目":"7.被推荐人是否曾在安靠工作过"},
    {"类型":"单选","选项": ["我已知悉，并告知被推荐人",  ],"题目":"8.被推荐人的简历通过审核后，我们将发送正式面试的短信至他（她）手机，请提醒被推荐人按照短信通知时间前来面试。未在规定时间内来面试者，视为无效。"},
    {"类型":"单选","选项": ["我已知悉此项规定并将遵守",  ],"题目":"9.我保证以上所填写信息准确无误。而且候选人只被推荐过一次，如有重复，视为无效"},
  ];
}


//全局变量
List<dynamic>  homeData=[
  // ["宿舍码","ss_code.png","/showDormitoryCode","true","HR"],
  // ["位置码","work_wzcode.png","/showPositionCode","true","HR"],
  ["内部推荐","nbtj.png","报表||","true","HR"],
  // ["满意度调查","msdj.png","报表||","true","HR"],
  ["班车考勤","bckq.png","报表||","true","HR"],
  // ["员工体检","ygtj.png","报表||","true","HR"],
  ["公司菜单","gscd.png","/cookbook","true","HR"],
  ["运动生活","ydsh.png","报表||","true","HR"],
  ["问卷调查","wjdc.png","/qa","true","HR"],
  ["班车线路","bcxl.png","/ShuttleBusPage","true","HR"],
  ["专业术语","edictionary.png","报表||","true","HR"],
  ["DL绩效考核","DLPA.png","报表||","true","HR"],
  //["培训记录","TrainingRecord.png","/TrainingRecord","true","HR"],
  ["","add_pic1.png","/addFunction","true","HR"],
];
List<dynamic>  homeData1=[
  // ["进厂码","work_qrcode1.png","/showInCode","true","COVID-19"],
  // ["核酸上报","work_hs.png","/uploadCovid","true","COVID-19"],
  // ["抗原上报","work_kylan.png","/uploadAntigen","true","COVID-19"],
  // ["离厂码","work_qrcode2.png","/showOutCode","true","COVID-19"],
  // ["","add_pic1.png",/addFunction,"true","COVID-19"],
];
List<dynamic> homeData2=[
  // ["员工内推表","ygntb.png","/pushInPage","true","审批类"],
  // ["岗位申请表","gwsqb.png","/KongPage","true","审批类"],
  // ["入职信息表","rzxxb.png","/entryFormPage","true","审批类"],
  // ["员工登记","ygdj.png","/KongPage","true","审批类"],
  // ["公积金补缴","gjjbj.png","/accumulationFundPage","true","审批类"],
  // ["","add_pic1.png","/addFunction","true","审批类"],
];
List<dynamic> homeData3=[
  // ["劳勤","lq.png","URL||alipays://platformapi/startapp?appId=2021001132656455","true","第三方应用"],
  ["Teams","Teams.png","URL||msteams://teams.microsoft.com/||com.microsoft.teams","true","第三方应用"],
  ["Outlook","Outlook.png","URL||ms-outlook://||com.microsoft.office.outlook","true","第三方应用"],
  ["E-mobile7","E-mobile.png","URL||emobile7://||com.weaver.emobile7","true","第三方应用"],
  ["","add_pic1.png","/addFunction","true","第三方应用"],
];

List<dynamic> homeDataRe=[
  // ["常见问题","cjwt.png","/FQAPage","true","HR"],
  // ["防疫政策","fyzc.png","/riskAreaPage","true","HR"],
  // ["资产盘点","gdzcpd.png","/KongPage","true","HR"],
  // ["宿舍登记","ssdj.png","/KongPage","true","HR"],
  // ["Facility巡检","fxc.png","/KongPage","true","HR"],
  // ["5S检查","5sjc.png","/KongPage","true","HR"],
  // ["爱心伞","axs.png","/umbrellaPage","true","HR"],
  // ["员工绩效","ygjx.png","/KongPage","true","HR"],
  // ["员工培训","ygjx.png","/KongPage","true","HR"],
];
List<dynamic> homeDataOther=[
  ["","add_pic1.png","/addFunction","true","其他类"],
];


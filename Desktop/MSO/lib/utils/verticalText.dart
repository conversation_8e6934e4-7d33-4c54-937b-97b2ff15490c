import 'package:flutter/material.dart';

class VerticalText extends StatefulWidget {
  const VerticalText({Key? key,required this.text,required this.wrapLength,this.color=Colors.black,this.height=1.2,this.fontSize=14}) : super(key: key);
  final String text;
  final int wrapLength;
  final Color color;
  final double fontSize;
  final double height;

  @override
  _VerticalTextState createState() => _VerticalTextState();
}

class _VerticalTextState extends State<VerticalText> {
    String? text;
    int? index;
    int? wrapLength;
    int? remain;
    int? textLength;


   Future<void> initData()async {
      text=widget.text;
      textLength=text!.length;
      wrapLength=widget.wrapLength;
      remain=textLength!%wrapLength!;
      index=textLength!~/wrapLength!;
      print("0-=-=-=-=-=-=-=-=-=$index");
    }
   @override
   void initState() {
    // TODO: implement initState
     initData();
     print("0-=-=-=-=-=-=-=-=-=$index");
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        for(int i=0;i<index!;i++)
          SizedBox(
            width: widget.fontSize,
            child: Text(
              text!.substring(wrapLength!*i,wrapLength!+wrapLength!*i),
              style: TextStyle(color: widget.color,fontSize: widget.fontSize,height:widget.height,),
              textDirection: TextDirection.ltr,
              textAlign: TextAlign.center,
            ),
          ),
        if(remain!=0)
          SizedBox(
            width: 14,
            child: Text(
              text!.substring(textLength!-remain!),
              style: TextStyle(color: widget.color,fontSize: widget.fontSize,height:widget.height,),
              textDirection: TextDirection.ltr,
              textAlign: TextAlign.center,
            ),
          ),
      ],
    );
  }

}



import 'package:flutter/material.dart';

class TreeView extends StatefulWidget {
  final List<TreeNode> nodes;

  TreeView({required this.nodes});

  @override
  _TreeViewState createState() => _TreeViewState();
}

class _TreeViewState extends State<TreeView> {
  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: widget.nodes.length,
      itemBuilder: (BuildContext context, int index) {
        return _buildTile(widget.nodes[index], );
      },
    );
  }

  Widget _buildTile(TreeNode node) {
    if (node.children.isEmpty) {
      return ListTile(
        title: Text(node.title),
      );
    }
    return ExpansionTile(
      title: Text(node.title),
      childrenPadding:const EdgeInsets.only(left: 20.0 ) ,
      initiallyExpanded:false,
      leading:const Icon(Icons.add),
      // tilePadding:EdgeInsetsDirectional.all(0),
      trailing:const SizedBox(),
      children: [
        ListView.builder(
          shrinkWrap: true,
          physics: const ClampingScrollPhysics(),
          itemCount: node.children.length,
          itemBuilder: (BuildContext context, int index) {
            return _buildTile(node.children[index],);
          },
        )
      ],
    );
  }
}

class TreeNode {
  final String title;
  final List<TreeNode> children;

  TreeNode({required this.title, required this.children});
}


class LoadingWidget extends StatefulWidget {
  const LoadingWidget({Key? key}) : super(key: key);
  @override
  _loadingWidgetState createState() => _loadingWidgetState();
}

class _loadingWidgetState extends State<LoadingWidget> with SingleTickerProviderStateMixin {

  late AnimationController controller;      // 动画控制器
  late Animation<double> animation;      // 动画

  // 补间(动画）
  static final scaleTween =  Tween<double>(begin: 14.0, end: 6.0);
  static final scaleTween2 =  Tween<double>(begin: 12.0, end: 8.0);
  static final scaleTween3 =  Tween<double>(begin: 8.0, end: 12.0);
  static final scaleTween4 =  Tween<double>(begin: 6.0, end: 14.0);
  static final scaleTween5 =  Tween<double>(begin: 20.0, end: 6.0);


  @override
  void initState() {
    super.initState();

    controller = AnimationController(
        duration: const Duration(milliseconds: 500), vsync: this);
    animation =  CurvedAnimation(parent: controller, curve: Curves.easeInOut);
    animation.addListener((){
      setState(() {

      });
    });
    animation.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        // 动画执行结束时反向执行动画
        controller.reverse();
      } else if (status == AnimationStatus.dismissed) {
        // 动画恢复到初始状态时执行动画（正向）
        controller.forward();
      }

    });
    controller.forward();

  }


  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: MediaQuery.of(context).size.width,
      child: Container(
        padding:const EdgeInsetsDirectional.only(start: 88,end: 88),
        width: 30,
        height: scaleTween5.evaluate(animation),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            Container(
              decoration: const BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(50)),
                  color: Color(0Xff258DFF)
              ),
              width: scaleTween.evaluate(animation),
              height: scaleTween.evaluate(animation),
            ),
            Container(
              decoration: const BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(50)),
                  color: Color(0Xff258DFF)
              ),
              width: scaleTween2.evaluate(animation),
              height: scaleTween2.evaluate(animation),
            ),
            Container(
              decoration: const BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(50)),
                  color: Color(0Xff258DFF)
              ),
              width: scaleTween3.evaluate(animation),
              height: scaleTween3.evaluate(animation),
            ),
            Container(
              decoration: const BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(50)),
                  color: Color(0Xff258DFF)
              ),
              width: scaleTween4.evaluate(animation),
              height: scaleTween4.evaluate(animation),
            ),
          ],
        ),

      ),
    );
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }
}
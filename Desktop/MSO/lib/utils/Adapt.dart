import 'dart:io';
import 'dart:ui';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// 屏幕适配工具类
/// 用于处理不同屏幕尺寸的适配问题
class ScreenAdapter {
  static double _screenWidth = 0;
  static double _screenHeight = 0;
  static double _physicalWidth = 0;
  static double _physicalHeight = 0;
  static double _devicePixelRatio = 0;
  static double _widthRatio = 1.0;
  static double _statusBarHeight = 0;
  static double _bottomSafeAreaHeight = 0;
  static double _heightRatio = 0;

  /// 屏幕高宽比
  static double aspectRatio = 0;

  static void initialize(BuildContext context,
      {double designWidth = 375, double designHeight = 667}) {
    // 1.媒体查询信息
    final mediaQueryData = MediaQuery.of(context);

    // 2.获取宽度和高度
    _screenWidth = mediaQueryData.size.width;
    _screenHeight = mediaQueryData.size.height;
    _physicalWidth = window.physicalSize.width;
    _physicalHeight = window.physicalSize.height;
    //像素比
    _devicePixelRatio = window.devicePixelRatio;

    // 3.状态栏的高度
    // 顶部有刘海:47pt 没有刘海的屏幕为20pt
    _statusBarHeight = mediaQueryData.padding.top;
    // 底部有刘海:34pt 没有刘海的屏幕0pt
    _bottomSafeAreaHeight = mediaQueryData.padding.bottom;
    //宽度比例
    _widthRatio = _screenWidth / designWidth;
    //高度比例
    _heightRatio = _screenHeight / designHeight;
    debugPrint("Screen ratio - width: $_widthRatio, height: $_heightRatio");
    aspectRatio = _screenHeight / _screenWidth;
  }

  /// 根据设计稿宽度适配
  static double width(double size) {
    /// Web PC端 macOS端
    if(kIsWeb && _isDesktopPlatform()) {
      return size * _widthRatio / 4;
    }
    /// 移动端
    return size * _widthRatio;
  }

  /// 根据设计稿高度适配
  static double height(double size) {
    /// Web PC端 macOS端
    if(kIsWeb && _isDesktopPlatform()) {
      return size * _heightRatio / 4;
    }
    /// 移动端
    return size * _heightRatio;
  }

  /// 判断是否为桌面平台
  static bool _isDesktopPlatform() {
    return defaultTargetPlatform == TargetPlatform.macOS ||
           defaultTargetPlatform == TargetPlatform.windows;
  }

  /// 设置Android系统UI样式
  static void setAndroidSystemUI({bool isDark = true}) {
    if (Platform.isAndroid) {
      SystemUiOverlayStyle systemUiOverlayStyle = SystemUiOverlayStyle(
        statusBarColor: Colors.transparent, //设置为透明
        systemNavigationBarColor: Colors.transparent,
        statusBarIconBrightness: isDark ? Brightness.dark : Brightness.light,
      );
      SystemChrome.setSystemUIOverlayStyle(systemUiOverlayStyle);
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    }
  }
}

/// 向后兼容的别名类
/// @deprecated 请使用 ScreenAdapter 替代
@Deprecated('Use ScreenAdapter instead')
class Adapt {
  static void initialize(BuildContext context, {double UIWidth = 375, double UIHeight = 667}) {
    ScreenAdapter.initialize(context, designWidth: UIWidth, designHeight: UIHeight);
  }

  static double widthPt(double size) => ScreenAdapter.width(size);
  static double heightPt(double size) => ScreenAdapter.height(size);
  static void setAndroid({bool isDark = true}) => ScreenAdapter.setAndroidSystemUI(isDark: isDark);

  static double get ratio => ScreenAdapter.aspectRatio;
}

import 'dart:async';
import 'package:amkor/login.dart';
import 'package:amkor/services/employeeService.dart';
import 'package:amkor/utils/adapt.dart';
import 'package:amkor/utils/crypt.dart';
import 'package:amkor/utils/debounceAndThrottling.dart';
import 'package:amkor/utils/reg.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:pinput/pinput.dart';

import '../components/tabbar_v2.dart';
import '../utils/colors.dart';

class UpdatePwdPage extends StatefulWidget {
  UpdatePwdPage({Key? key, this.Telephonenumber}) : super(key: key);
  String? Telephonenumber;
  @override
  _UpdatePwdPageState createState() => _UpdatePwdPageState();
}

///记录发送短信的时间
var d1;

class _UpdatePwdPageState extends State<UpdatePwdPage>
    with WidgetsBindingObserver {
  TextEditingController _oldPwdController = TextEditingController();
  TextEditingController _newPwdController = TextEditingController();
  bool _isLoading = false;
  String _oldPwd = "";
  String _newPwd = "";
  bool _showOldPwd = false;
  bool _showNewPwd = false;
  var validationCode;

  // final TextEditingController _fieldOne = TextEditingController();
  // final TextEditingController _fieldTwo = TextEditingController();
  // final TextEditingController _fieldThree = TextEditingController();
  // final TextEditingController _fieldFour = TextEditingController();
  // final TextEditingController _fieldFive = TextEditingController();
  // final TextEditingController _fieldSix = TextEditingController();

  ///
  //FocusNode
  FocusNode jkNode = new FocusNode();

  GlobalKey _formKey = GlobalKey<FormState>();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: TabberV2(
        title: "title6".tr,
        showBack: true,
      ),
      resizeToAvoidBottomInset: false,
      body: _buildBody(),
    );
  }

  @override
  void reassemble() {
    // TODO: implement reassemble
    super.reassemble();
  }

  final d = DateTime.now();
  bool int1 = true;
  Timer? timer_;
  int int_ = 0;
  @override
  void initState() {
    super.initState();
    // WidgetsBinding.instance.addObserver(this);
    //判断是否是安卓10以上设备
    // if(!kIsWeb){
    // if(Platform.isAndroid)
    // _judgeTen();
    // }
  }

  ///

  // @override
  // void didChangeAppLifecycleState(AppLifecycleState state) {
  //   switch (state) {
  //   // 处于这种状态的应用程序应该假设它们可能在任何时候暂停。
  //     case AppLifecycleState.inactive:
  //       // _judgePlatformInfo();
  //       break;
  //   // 应用程序可见，前台
  //     case AppLifecycleState.resumed:
  //     //判断平台信息
  //     print('object1111111111111');
  //       _judgePlatformInfo();
  //       break;
  //   // 应用程序不可见，后台
  //     case AppLifecycleState.paused:
  //
  //       break;
  //   // 申请将暂时暂停
  //     case AppLifecycleState.detached:
  //       break;
  //   }
  // }

  //判断是否是安卓10以上设备
  // _judgeTen () async {
  //   //获取设备信息
  //   DeviceInfoPlugin dip = new DeviceInfoPlugin();
  //   AndroidDeviceInfo adi = await dip.androidInfo;
  //   //经亲测发现安卓10以下取到的版本号是xxx.0，安卓10以上直接取到10或者11
  //   String version = "";
  //   if (adi.version.release!.contains(".")) {
  //     //判断版本号中是否有.，有的话用.分割一下取得那个整数（返回的是字符串类型）
  //     var haha = adi.version.release!.split(".");
  //     version = haha[0];
  //   } else {
  //     version = adi.version.release!;
  //   }
  //   //如果版本号大于等于10，则监听FocusNode
  //   if ( int.parse(version) >= 10 ) {
  //     _androidAboveTengetClipboardContent();
  //   }
  // }

  //判断平台信息
  // _judgePlatformInfo () async {
  //   if (Platform.isAndroid) {
  //     DeviceInfoPlugin dip = new DeviceInfoPlugin();
  //     AndroidDeviceInfo adi = await dip.androidInfo;
  //     String version = "";
  //     if (adi.version.release!.contains(".")) {
  //       var haha = adi.version.release!.split(".");
  //       version = haha[0];
  //     } else {
  //       version = adi.version.release!;
  //     }
  //     if ( int.parse(version) < 10 ) {
  //       _androidBelowTengetClipboardContent();
  //     } else {
  //       FocusScope.of(context).requestFocus(jkNode);
  //     }
  //   }
  // }

  //获取剪贴板内容：安卓10以下和以上两个方法
  // _androidBelowTengetClipboardContent () async {
  //   ClipboardData? data = await Clipboard.getData(Clipboard.kTextPlain);
  //   String? fz;
  //   if(fz!=data?.text)
  //   {
  //     fz = data?.text;
  //     if (data != null && int.tryParse(data.text!) != null &&
  //         data.text?.length == 6) {
  //       _fieldOne.text = data.text!.substring(0, 1);
  //       _fieldTwo.text = data.text!.substring(1, 2);
  //       _fieldThree.text = data.text!.substring(2, 3);
  //       _fieldFour.text = data.text!.substring(3, 4);
  //       _fieldFive.text = data.text!.substring(4, 5);
  //       _fieldSix.text = data.text!.substring(5, 6);
  //     }
  //   }
  // }
  // _androidAboveTengetClipboardContent () async {
  //   String? fz;
  //   jkNode.addListener(() async {
  //     if (jkNode.hasFocus) {
  //       FocusScope.of(context).unfocus();
  //
  //       ClipboardData? data = await Clipboard.getData(Clipboard.kTextPlain);
  //       print('${data?.text}================${fz}');
  //       if(fz!=data?.text)
  //       {
  //       fz = data?.text;
  //
  //       if (data != null && int.tryParse(data.text!) != null&&data.text?.length==6 ) {
  //         _fieldOne.text = data.text!.substring(0, 1);
  //         _fieldTwo.text = data.text!.substring(1, 2);
  //         _fieldThree.text = data.text!.substring(2, 3);
  //         _fieldFour.text = data.text!.substring(3, 4);
  //         _fieldFive.text = data.text!.substring(4, 5);
  //         _fieldSix.text = data.text!.substring(5, 6);
  //
  //       }
  //     }
  //       ///清空剪切版
  //
  //     }
  //   });
  //
  // }
  //

  Widget _buildBody() {
    ///可能出bug
    FocusNode focusNode = FocusNode();
    return Container(
      padding: const EdgeInsets.fromLTRB(25, 10, 25, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ///旧密码，新密码修改,验证码
          _buildForm(),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              widget.Telephonenumber != ""
                  ? Text(
                      "text6_13".tr +
                          '${widget.Telephonenumber?.substring(0, 3)}****${widget.Telephonenumber?.substring(7)}',
                      style: const TextStyle(
                          color: Color.fromRGBO(151, 151, 151, 1),
                          fontSize: 12),
                    )
                  : Text(
                      'text6_13'.tr + '****',
                      style: TextStyle(
                          color: Color.fromRGBO(151, 151, 151, 1),
                          fontSize: 12),
                    ),
              const Padding(padding: EdgeInsets.only(left: 20)),
              TextButton(
                onPressed: int1 &&
                        _oldPwdController.text != '' &&
                        _newPwdController.text != '' &&
                        RegExp(r'^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])[a-zA-Z0-9]{6,16}$')
                                .firstMatch(_newPwdController.text) !=
                            null
                    ? throttle(() async {
                        FocusScope.of(context).requestFocus(focusNode);
                        await Future.delayed(
                            const Duration(milliseconds: 1000));
                        if (!mounted) {
                          return;
                        }

                        ///请求短信验证
                        var enp = MyCrypt.pwdEncrypt(_oldPwdController.text);
                        var aaa = await EmployeeService().GetSmsCode(enp);

                        ///限制新密码
                        Fluttertoast.showToast(msg: "${aaa?.message}");
                        if (aaa?.code != 500) {
                          int1 = false;
                          int_ = 60;
                          timer_ = Timer.periodic(
                              const Duration(
                                milliseconds: 1000,
                              ), (timer) {
                            int_--;
                            if (int_ == 0) {
                              int1 = true;
                              timer_?.cancel();
                            }
                            setState(() {});
                          });
                        }
                      })
                    : () {},
                autofocus: false,
                style: ButtonStyle(
                  textStyle: MaterialStateProperty.all(const TextStyle(
                    fontSize: 16,
                  )),

                  //更优美的方式来设置
                  foregroundColor: MaterialStateProperty.resolveWith(
                    (states) {
                      if (states.contains(MaterialState.focused) &&
                          !states.contains(MaterialState.pressed)) {
                        //获取焦点时的颜色
                        return Colors.blue;
                      } else if (states.contains(MaterialState.pressed)) {
                        //按下时的颜色
                        return Colors.white;
                      }
                      //默认状态使用灰色
                      return Colors.blue;
                    },
                  ),
                  //背景颜色
                  backgroundColor: MaterialStateProperty.resolveWith((states) {
                    //设置按下时的背景颜色
                    if (states.contains(MaterialState.pressed)) {
                      return Colors.black;
                    }
                    //默认不使用背景颜色
                    return null;
                  }),
                  //设置水波纹颜色
                  overlayColor: MaterialStateProperty.all(Colors.black),
                  //设置阴影  不适用于这里的TextButton
                  elevation: MaterialStateProperty.all(0),
                  //设置按钮内边距
                  padding: MaterialStateProperty.all(const EdgeInsets.all(10)),
                  //设置按钮的大小
                  minimumSize: MaterialStateProperty.all(const Size(60, 30)),

                  //设置边框
                  side: MaterialStateProperty.all(
                      const BorderSide(color: Colors.grey, width: 1)),
                  //外边框装饰 会覆盖 side 配置的样式
                  shape: MaterialStateProperty.all(const StadiumBorder()),
                ),

                child: int_ == 0
                    ? (Text(
                        "SendSMS".tr,
                        style: _oldPwdController.text == '' ||
                                _newPwdController.text == ''
                            ?
                            // child: int_==0? (Text("发送验证码",style:_oldPwdController.text==''||_newPwdController.text==''?
                            const TextStyle(
                                color: Color.fromRGBO(151, 151, 151, 1))
                            : null,
                      ))
                    : Text(int_.toString()),
                // const TextStyle(color:  Color.fromRGBO(151, 151, 151, 1)):null,)):Text(int_.toString()),
              )
            ],
          ),
          const Padding(padding: EdgeInsets.only(top: 30)),
          Text(
            "text6_6".tr,
            style: const TextStyle(
                color: Color.fromRGBO(151, 151, 151, 1), fontSize: 12),
          ),
          const Padding(padding: EdgeInsets.only(top: 10)),
          Container(
            width: MediaQuery.of(context).size.width,
            padding: EdgeInsets.only(top: Adapt.heightPt(30)),
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: CustomColors.ThemeColor,
                minimumSize: Size(MediaQuery.of(context).size.width, 48),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
              onPressed: _isLoading
                  ? null
                  : () {
                      setState(() {
                        _isLoading = true;
                      });
                      _updatePwdClick();
                    },
              child: _isLoading
                  ? SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text("text6_5".tr, style: TextStyle(color: Colors.white)),
            ),
          )
        ],
      ),
    );
  }

  Widget _buildForm() {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          const Padding(padding: EdgeInsets.only(top: 20)),
          _buildOldPwd(),
          const Padding(padding: EdgeInsets.only(top: 20)),
          _buildNewPwd(),
          const Padding(padding: EdgeInsets.only(top: 20)),

          ///验证码
          RawKeyboardListener(
            focusNode: FocusNode(),
            autofocus: false,
            onKey: (event) {
              if (event.runtimeType == RawKeyDownEvent) {
                switch (event.logicalKey.keyLabel) {
                  case "Backspace":
                    // FocusScope.of(context).previousFocus();
                    break;
                }
              }
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [buildPinPut()],
            ),
          ),
          const Padding(padding: EdgeInsets.only(top: 20)),
        ],
      ),
    );
  }

  Widget _buildOldPwd() {
    return TextFormField(
      autofocus: false,
      controller: _oldPwdController,
      onChanged: (e) {
        setState(() {
          _oldPwd = e;
        });
      },
      obscureText: !_showOldPwd,
      decoration: InputDecoration(
        hintText: "text6_2".tr,
        floatingLabelBehavior: FloatingLabelBehavior.auto,
        label: Text(
          "text6_1".tr,
          style: TextStyle(color: CustomColors.ThemeColor),
        ),
        focusColor: const Color.fromRGBO(29, 64, 133, 1),
        focusedBorder: const UnderlineInputBorder(),
        suffixIcon: Offstage(
          offstage: _oldPwd == "",
          child: IconButton(
            icon: Icon(
              _showOldPwd ? Icons.visibility : Icons.visibility_off,
              size: 20,
              color: Colors.grey[500],
            ),
            onPressed: () {
              setState(() {
                _showOldPwd = !_showOldPwd;
              });
            },
          ),
        ),
      ),
      autovalidateMode: AutovalidateMode.onUserInteraction,
      validator: (v) {
        if (!v!.trim().isNotEmpty) {
          return "text6_8".tr;
        }
        return null;
      },
    );
  }

  Widget _buildNewPwd() {
    return TextFormField(
      autofocus: false,
      controller: _newPwdController,
      onChanged: (e) {
        setState(() {
          _newPwd = e;
        });
      },
      obscureText: !_showNewPwd,
      decoration: InputDecoration(
        hintText: "text6_4".tr,
        floatingLabelBehavior: FloatingLabelBehavior.auto,
        label: Text(
          "text6_3".tr,
          style: TextStyle(color: CustomColors.ThemeColor),
        ),
        focusColor: const Color.fromRGBO(29, 64, 133, 1),
        focusedBorder: const UnderlineInputBorder(),
        suffixIcon: Offstage(
          offstage: _newPwd == "",
          child: IconButton(
            icon: Icon(
              _showNewPwd ? Icons.visibility : Icons.visibility_off,
              size: 20,
              color: Colors.grey[500],
            ),
            onPressed: () {
              setState(() {
                _showNewPwd = !_showNewPwd;
              });
            },
          ),
        ),
      ),
      autovalidateMode: AutovalidateMode.onUserInteraction,
      validator: (v) {
        if (!v!.trim().isNotEmpty) {
          return "text6_9".tr;
        }
        if (v.trim().length < 6) {
          return "text6_10".tr;
        }
        if (!MyReg.matchPwd(v.trim())) return "text6_11".tr;
        return null;
      },
    );
  }

  void _updatePwdClick() async {
    if ((_formKey.currentState as FormState).validate() == false) {
      setState(() {
        _isLoading = false;
      });
      return;
    }
    var oldPwd = _oldPwdController.text;
    var newPwd = _newPwdController.text;

    var eop = MyCrypt.pwdEncrypt(oldPwd);
    var enp = MyCrypt.pwdEncrypt(newPwd);
    //验证码加密上传
    // var yzm = MyCrypt.pwdEncrypt(validationCode);
    if (validationCode == null) {
      Fluttertoast.showToast(msg: "验证码不能为空".tr);
      setState(() {
        _isLoading = false;
      });
      return;
    }
    var resp = await EmployeeService().UpdatePwd(eop, enp, validationCode);

    setState(() {
      _isLoading = false;
    });

    if (resp == null) {
      Fluttertoast.showToast(msg: "Tips6".tr);
      return;
    }
    if (resp.message == "") {
      Fluttertoast.showToast(msg: "text6_12".tr);
    } else {
      Fluttertoast.showToast(msg: resp.message);
    }
    if (resp.success) _back();
  }

  void _back() {
    ///修改密码成功后回到登陆界面重新登陆
    Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const LoginPage()),
        (route) => false);
  }

  @override
  void dispose() {
    _newPwdController.dispose();
    _oldPwdController.dispose();
    timer_?.cancel();
    // WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  Widget buildPinPut() {
    final defaultPinTheme = PinTheme(
      width: 36,
      height: 36,
      textStyle: const TextStyle(
          fontSize: 20,
          color: Color.fromRGBO(30, 60, 87, 1),
          fontWeight: FontWeight.w600),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.black),
        // border: Border.all(color: const Color.fromRGBO(234, 239, 243, 1)),
        borderRadius: BorderRadius.circular(20),
      ),
    );

    final focusedPinTheme = defaultPinTheme.copyDecorationWith(
      border: Border.all(color: const Color.fromRGBO(114, 178, 238, 1)),
      borderRadius: BorderRadius.circular(8),
    );

    final submittedPinTheme = defaultPinTheme.copyWith(
      decoration: defaultPinTheme.decoration?.copyWith(
        color: const Color.fromRGBO(234, 239, 243, 1),
      ),
    );

    return Pinput(
        length: 6,
        defaultPinTheme: defaultPinTheme,
        focusedPinTheme: focusedPinTheme,
        submittedPinTheme: submittedPinTheme,
        inputFormatters: [
          FilteringTextInputFormatter.allow(RegExp("[0-9.]")),
        ],
        // validator: (s) {
        //   return s == '2222' ? null : 'Pin is incorrect';
        // },
        pinputAutovalidateMode: PinputAutovalidateMode.onSubmit,
        showCursor: true,
        onChanged: (value) {
          validationCode = value;
          print('-------------$validationCode');
        },
        onCompleted: (pin) => () {
              validationCode = pin;
              print('-------------$pin');
            });
  }
}

class OtpInput extends StatelessWidget {
  final TextEditingController controller;
  final bool autoFocus;
  final int name1;

  const OtpInput(this.controller, this.autoFocus, this.name1, {Key? key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 46,
      width: 40,
      child:
          // TextField
          TextFormField(
        //只允许输入小数
        inputFormatters: [
          FilteringTextInputFormatter.allow(RegExp("[0-9.]")),
        ],
        //键盘类型
        keyboardType: const TextInputType.numberWithOptions(decimal: true),
        autofocus: autoFocus,
        textAlign: TextAlign.center,
        controller: controller,
        // maxLength: 1,
        cursorColor: Theme.of(context).primaryColor,
        decoration: const InputDecoration(
            border: OutlineInputBorder(),
            counterText: '',
            // errorStyle: TextStyle(color: Colors.black, fontSize:1),
            hintStyle: TextStyle(color: Colors.black, fontSize: 10.0)),
        autovalidateMode: AutovalidateMode.onUserInteraction,
        onTap: () {
          FocusScope.of(context).requestFocus();
          print('========================点击事件目前空');
        },
        onChanged: (value) {
          ///需要修改
          print('========================${controller.text}');

          ///加一个判断 最后一个输入框是否跳到下一个焦点

          if (value.length >= 1) {
            controller.text = '1';
            FocusScope.of(context).nextFocus();
          }

          // ///加一个判断 第一个输入框是否跳到上一个焦点
          // if (value.length == 0&&name1!=1) {
          //   FocusScope.of(context).previousFocus();
          // }
        },
      ),
    );
  }
}

import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:amkor/indexPages/index/index.dart';
import 'package:amkor/relogin.dart';
import 'package:amkor/services/authService.dart';
import 'package:amkor/services/baseService.dart';
import 'package:amkor/services/dto/loginDto.dart';
import 'package:amkor/services/employeeService.dart';
import 'package:amkor/utils/TreeView.dart';
import 'package:amkor/utils/adapt.dart';
import 'package:amkor/utils/addj.dart';
import 'package:amkor/utils/colors.dart';
import 'package:amkor/utils/crypt.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get_storage/get_storage.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:amkor/utils/permission_helper.dart';
import 'package:url_launcher/url_launcher.dart';

import 'package:get/get.dart';
import 'components/routes.dart';
import 'deviceLogin.dart';
import 'main.dart';
import 'services/permission/main_page.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({Key? key}) : super(key: key);

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  String username = "";
  String password = "";
  bool showPassord = false;
  TextEditingController _usernameCtrl = TextEditingController();
  TextEditingController _passwordCtrl = TextEditingController();
  bool _isLoading = false;
  String _version = "";
  String lang = "";
  GlobalKey _formKey = GlobalKey<FormState>();
  bool _freeLogin = false;

  bool showUserClear = false;
  String platform = "";
  String device_str = "";

  @override
  void initState() {
    Get.locale = Locale('zh', 'CN');

    ///先请求一下相机权限，iOS用户体验
    if (!kIsWeb) {
      if (Platform.isAndroid) {
        Adapt.setAndroid();
        checkPermission();
      }
    }
    WidgetsFlutterBinding.ensureInitialized();
    var f = GetStorage().read("loginInfo");
    if (f != null) {
      if (f is LoginInputDto) {
        _usernameCtrl.text = f.employeeId;
        _passwordCtrl.text = f.password;
        setState(() {
          username = f.employeeId;
          password = f.password;
        });
      } else {
        var li = LoginInputDto.fromJson(f);
        _usernameCtrl.text = li.employeeId;
        _passwordCtrl.text = li.password;
        setState(() {
          username = li.employeeId;
          password = li.password;
        });
      }

      _freeLogin = true;
    }
    if (kIsWeb) {
      setState(() {
        _version = "v1.0.6";
      });
    } else {
      PackageInfo.fromPlatform().then((info) async {
        if (Platform.isAndroid) {
          platform = "Android";

          final deviceInfo = await DeviceInfoPlugin().androidInfo;
          print('---------->');
          // print(deviceInfo.version.baseOS);  // The base OS build the product is based on.
          // print(deviceInfo.version.codename);  // The current development codename, or the string "REL" if this is a release build.
          // print(deviceInfo.version.incremental);  // The internal value used by the underlying source control to represent this build.
          // print(deviceInfo.version.release);  // The user-visible version string.
          // print(deviceInfo.version.securityPatch); //
          // print(deviceInfo.model);
          // print('${deviceInfo.version.sdkInt}');
          // print(deviceInfo.board);
          // print(deviceInfo.bootloader);
          // print(deviceInfo.brand);
          // print(deviceInfo.device);
          // print(deviceInfo.display);
          // print(deviceInfo.fingerprint);
          // print(deviceInfo.hardware);
          // print(deviceInfo.host);
          // print(deviceInfo.id);
          // print(deviceInfo.manufacturer);
          // print(deviceInfo.model);
          // print(deviceInfo.product);
          // print(deviceInfo.supported32BitAbis);
          // print(deviceInfo.supported64BitAbis);
          // print(deviceInfo.supportedAbis);
          // print(deviceInfo.tags);
          // print(deviceInfo.type);
          // print(deviceInfo.isPhysicalDevice);
          // print(deviceInfo.androidId);

          // print(deviceInfo.fingerprint);
          // print(deviceInfo.model);
          // print(deviceInfo.androidId);
          // account with device (v2.5.0)
          device_str =
              '${deviceInfo.fingerprint};${deviceInfo.model};${deviceInfo.id}'; // 设备和账号绑定 Leo
          print(device_str);
        } else if (Platform.isIOS) {
          platform = "IOS";

          final deviceInfo = await DeviceInfoPlugin().iosInfo;

          // print(deviceInfo.name);  // Device name.
          // print(deviceInfo.systemName);  // The name of the current operating system.
          // print(deviceInfo.systemVersion);  // The current operating system version.
          // print(deviceInfo.model);  // Device model.
          // print(deviceInfo.localizedModel);  // Localized name of the device model.
          // print(deviceInfo.identifierForVendor);  // Unique UUID value identifying the current device.
          // account with device (v2.5.0)
          device_str =
              '${deviceInfo.name};${deviceInfo.systemName};${deviceInfo.systemVersion};${deviceInfo.model};${deviceInfo.localizedModel};${deviceInfo.identifierForVendor}'; // 设备和账号绑定 Leo
          print(device_str);
        }
        setState(() {
          _version = "v${info.version}";
        });
      });
    }
    super.initState();
  }

  //判断是否有储存权限 - 更新为Android 13兼容版本
  void checkPermission() async {
    final result = await PermissionHelper.checkAndRequestStoragePermissions();

    if (result.hasAllPermissions) {
      // 所有权限都已授予
      debugPrint('所有权限已授予');
    } else if (result.hasPermanentlyDeniedPermissions) {
      // 有权限被永久拒绝，引导用户到设置
      for (var permission in result.permanentlyDeniedPermissions) {
        _showPermissionSettingsDialog(permission);
      }
    } else if (result.hasDeniedPermissions) {
      // 有权限被拒绝，显示解释对话框
      for (var permission in result.deniedPermissions) {
        _showPermissionExplanationDialog(permission);
      }
    }

    if (result.error != null) {
      debugPrint('权限检查错误: ${result.error}');
    }
  }

  // 显示权限解释对话框 - 使用PermissionHelper统一处理
  void _showPermissionExplanationDialog(Permission permission) {
    PermissionHelper.showPermissionExplanationDialog(
        context, permission, checkPermission);
  }

  // 显示设置页面引导对话框 - 使用PermissionHelper统一处理
  void _showPermissionSettingsDialog(Permission permission) {
    PermissionHelper.showPermissionSettingsDialog(context, permission);
  }

  @override
  void reassemble() {
    if (kIsWeb) {
      ///web版本号
      setState(() {
        _version = "v1.0.7";
      });
    } else {
      PackageInfo.fromPlatform().then((info) {
        if (Platform.isAndroid) {
          platform = "Android";
        } else if (Platform.isIOS) {
          platform = "IOS";
        }
        setState(() {
          _version = "v${info.version}";
        });
      });
    }
    super.reassemble();
  }

  @override
  Widget build(BuildContext context) {
    Adapt.initialize(context);
    lang = Localizations.localeOf(context).toString().replaceAll('zh_', '');
    return Scaffold(
      body: SingleChildScrollView(
        child: SizedBox(
          ///高度适配web
          width: kIsWeb &&
                  (defaultTargetPlatform == TargetPlatform.macOS ||
                      defaultTargetPlatform == TargetPlatform.windows)
              ? MediaQuery.of(context).size.width * 0.24
              : null,
          // height:  MediaQuery.of(context).size.height*0.4,
          child: Stack(
            children: [
              Image(
                image: const AssetImage('assets/images/main_bg.png'),
                fit: BoxFit.fitWidth,
                width: MediaQuery.of(context).size.width,
              ),
              Padding(
                padding: EdgeInsets.only(top: Adapt.widthPt(120)),
                child: _buildCard(),
              ),
            ],
          ),
        ),
      ),

      // ),
    );
  }

  /// 页面中间的内容
  Widget _buildCard() {
    return SizedBox(
      child: Card(
        elevation: 0,
        margin: EdgeInsets.fromLTRB(
            Adapt.widthPt(20), 0, Adapt.widthPt(20), Adapt.widthPt(10)),
        child: Column(
          mainAxisSize: MainAxisSize.max,
          children: [
            const Padding(padding: EdgeInsets.only(top: 20)),
            Image(
              image: const AssetImage("assets/images/logo.png"),
              width: Adapt.widthPt(198),
            ),
            _buildForm(),
          ],
        ),
      ),
    );
  }

  Widget _buildForm() {
    return Form(
      key: _formKey,
      child: Padding(
        padding: EdgeInsets.fromLTRB(Adapt.widthPt(15), Adapt.widthPt(37),
            Adapt.widthPt(15), Adapt.widthPt(15)),
        child: Column(
          // mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              // autofocus: true,
              controller: _usernameCtrl,
              onChanged: (e) {
                setState(() {
                  username = e;
                });
              },

              decoration: InputDecoration(
                  hintText: 'Job number Tips'.tr,
                  labelText: 'Job number'.tr,
                  labelStyle: TextStyle(color: CustomColors.ThemeColor),
                  prefixIcon: Icon(
                    Icons.account_circle,
                    color: CustomColors.ThemeColor,
                    size: 25,
                  ),
                  focusColor: CustomColors.ThemeColor,
                  focusedBorder: const UnderlineInputBorder(),
                  suffixIcon: Offstage(
                    offstage: username == "",
                    child: IconButton(
                      icon: Icon(
                        Icons.cancel,
                        size: 20,
                        color: Colors.grey[500],
                      ),
                      onPressed: () {
                        setState(() {
                          username = "";
                          _usernameCtrl.text = "";
                        });
                      },
                    ),
                  )),
              autovalidateMode: AutovalidateMode.onUserInteraction,
              // 校验用户名
              validator: (v) {
                return v!.trim().isNotEmpty ? null : "Job number Tips1".tr;
              },
            ),
            Padding(
              padding: EdgeInsets.only(top: Adapt.widthPt(20)),
              child: TextFormField(
                onChanged: (s) {
                  setState(() {
                    password = s;
                  });
                },
                controller: _passwordCtrl,
                decoration: InputDecoration(
                  hintText: "password Tips".tr,
                  labelText: "password".tr,
                  labelStyle: TextStyle(color: CustomColors.ThemeColor),
                  prefixIcon: Icon(
                    Icons.lock,
                    color: CustomColors.ThemeColor,
                    size: 25,
                  ),
                  suffixIcon: Offstage(
                    offstage: password == "",
                    child: IconButton(
                      icon: Icon(
                        showPassord ? Icons.visibility_off : Icons.visibility,
                        size: 20,
                        color: Colors.grey[500],
                      ),
                      onPressed: () {
                        setState(() {
                          showPassord = !showPassord;
                        });
                      },
                    ),
                  ),
                  focusedBorder: UnderlineInputBorder(),
                ),
                obscureText: !showPassord,
                autovalidateMode: AutovalidateMode.onUserInteraction,
                //校验密码
                validator: (v) {
                  return v!.trim().length > 5 ? null : "password Tips1".tr;
                },
              ),
            ),
            Padding(padding: EdgeInsets.only(top: Adapt.heightPt(10))),
            _buildFreelogin(),
            Padding(padding: EdgeInsets.only(top: Adapt.heightPt(30))),
            _buildLoginBtn(),
            _buildVersion(),
            //语言切换
            Container(
              height: 120,
              alignment: Alignment.bottomRight,
              child: LanguageSwitching(),
            ),
            _buildFilling(),
          ],
        ),
      ),
    );
  }

  ///7天免登录
  Widget _buildFreelogin() {
    return Padding(
      padding: EdgeInsets.only(top: Adapt.widthPt(20)),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Checkbox(
                value: _freeLogin,
                activeColor: CustomColors.ThemeColor,
                onChanged: (value) {
                  setState(() {
                    _freeLogin = value!;
                  });
                },
              ),
              Text(
                'remember password'.tr,
                style: const TextStyle(
                    fontSize: 14, color: Color.fromRGBO(151, 151, 151, 1)),
              ),
            ],
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pushNamed(RouteNames.forgetPassword);
            },
            child: Text(
              // "忘记密码",
              "forget password".tr,
              style: TextStyle(
                fontSize: 12,
                color: CustomColors.ThemeColor,
              ),
            ),
          ),
          // TextButton(
          //   onPressed: () {},
          //   child: Text(
          //     "《用户服务协议及隐私政策》",
          //     style: TextStyle(
          //       fontSize: 12,
          //       color: CustomColors.ThemeColor,
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }

  ///登录按钮
  Widget _buildLoginBtn() {
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        backgroundColor: CustomColors.ThemeColor,
        minimumSize: Size(double.infinity, 48),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(6),
        ),
      ),
      onPressed: _isLoading
          ? null
          : () {
              setState(() {
                _isLoading = true;
              });
              _loginClick();
            },
      child: _isLoading
          ? SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          : Padding(
              padding: const EdgeInsets.only(right: 80, left: 80),
              child: Text(
                'Sign in'.tr,
                style:
                    TextStyle(fontSize: Adapt.widthPt(16), color: Colors.white),
              ),
            ),
    );
  }

  ///版本号
  Widget _buildVersion() {
    var controller = '';
    return Container(
      padding: EdgeInsets.only(top: 20),
      alignment: Alignment.center,
      child: Text(
        "$_version${BaseService(controller).judge}",
        style: const TextStyle(
          color: Color(0xFF888888),
        ),
      ),
    );
  }

  ///版本号
  Widget _buildFilling() {
    return GestureDetector(
      onTap: () async {
        // 定义你想要打开的URL
        final uri = Uri.parse('https://beian.miit.gov.cn');
        // 尝试打开URL
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri);
        } else {
          throw 'Could not launch ${uri.toString()}';
        }
      },
      child: Text(
        "沪ICP备18045150号-2A",
        style: const TextStyle(
          color: Color(0xFF888888),
        ),
      ),
    );
  }

  ///语言切换Language switching
  Widget LanguageSwitching() {
    MessagesController messagesController = Get.put(MessagesController());
    return ElevatedButton(
        style: ButtonStyle(
            backgroundColor:
                MaterialStateProperty.all(CustomColors.ThemeColor)),
        onPressed: () {
          'Language'.tr == 'English' ? lang = 'CN' : lang = 'EN';
          'Language'.tr == 'English'
              ? messagesController.changeLanguage('zh', "CN")
              : messagesController.changeLanguage('en', "EN");
        },
        child: Padding(
          padding: const EdgeInsets.only(right: 0, left: 0),
          child: Text(
            'Language1'.tr,
            style: TextStyle(fontSize: Adapt.widthPt(16)),
          ),
        ));
  }

  copyData(var data) {
    List<dynamic> cpData = [];
    for (var element in data) {
      cpData.add(element);
    }
    return cpData;
  }

  void _loginClick() async {
    var s = GetStorage();
    if ((_formKey.currentState as FormState).validate() == false) {
      setState(() {
        _isLoading = false;
      });
      return;
    }
    var u = _usernameCtrl.text;
    var p = _passwordCtrl.text;
    String encryptPwd = MyCrypt.pwdEncrypt(p);
    if (u == 'internal_account') {
      device_str = '';
    }
    var input = LoginInputDto(u, encryptPwd, platform == '' ? 'wap' : platform,
        _version, lang, device_str);
    var r = !kIsWeb
        ? await AuthService().login(input)
        : await AuthService().Weblogin(input);

    setState(() {
      _isLoading = false;
    });

    if (r == null) {
      Fluttertoast.showToast(msg: "网络异常，请稍后再试");
      return;
    }

    if (!r.success) {
      Fluttertoast.showToast(msg: r.message);
      return;
    }

    // 权限打开方法 By Leo 2023-12-25
    String supplier_str;
    roles = r.data["roles"] ?? "0"; //0:供应商, 1:ATC员工
    supplier_str = r.data["menus"] ??
        "内部推荐,班车考勤,运动生活,班车线路,公告,Teams,Outlook,E-mobile7,问卷调查,DL绩效考核"; //不需要显示的功能
    supplier = supplier_str.split(",");

    var loginInfo = LoginOutputDto.fromJson(r.data!);
    s.write("token", loginInfo.token);
    loginInfo.employee!.employeeId = u;

    ///传值
    ///添加了一个供app store审核的账号该账号电话号等参数为空所以需要加一个判定
    if (r.data['employee']['mobile'] != null)
      Routes(r.data['employee']['mobile'], r.data['employee']['maxPicSize']);
    s.write("employee", loginInfo.employee!.toJson());
    s.write("userinfo", loginInfo.employee);

    ///判断是否有新的应用来替换缓存
    bool renew = true;

    s.remove('cyl');
    print('print homedata');
    print(homeData);
    var homeDatas = copyData(homeData);
    var homeData1s = copyData(homeData1);
    var homeData2s = copyData(homeData2);
    var homeData3s = copyData(homeData3);
    var homeDataRes = copyData(homeDataRe);
    var homeDataOthers = copyData(homeDataOther);
    var homePages = copyData(homePage);

    ///暂时缓存本地的数据
    // bool flag_a=false; // 登陆权限控制 by Leo
    if (renew) {
      s.write('cyl', homeDatas);
      s.write('spl', homeData1s);
      s.write('COVID_19', homeData2s);
      s.write('dsf', homeData3s);
      s.write('class_0', homeDataRes);
      s.write('class_Other', homeDataOthers);
      s.write('top1', homePages);
    }

    supplier.map((e) {
      complexDataRemoveAt(s.read('cyl'), e);
      complexDataRemoveAt(s.read('spl'), e);
      complexDataRemoveAt(s.read('COVID_19'), e);
      complexDataRemoveAt(s.read('dsf'), e);
      complexDataRemoveAt(s.read('class_0'), e);
      complexDataRemoveAt(s.read('class_Other'), e);
      complexDataRemoveAt(s.read("top1"), e);
    }).toList();

    if (roles == "0") {
      var e = s.read("top1");
      for (int i = 0; i < e.length; i++) {
        if (e[i][0] == '员工码') {
          e[i] = ["通行码", "gp", "/showPassportCode"];
          print('->-->${e}');
        }
      }
    }
    // Leo 20240415 未加短信登陆
    // if (!mounted) return;
    // Navigator.of(context).pushReplacementNamed(RouteNames.indexPage);

    // Leo 20240415 已加短信登陆
    String login_range = r.message;
    // String login_range = "";

    if (login_range == "SMS") {
      // || login_range == "DEVICE"
      // 短信验证页面
      // Navigator.push(context, MaterialPageRoute(builder:(_) => Relogin(emp_id: r.data['employee']['employeeId'], emp_phonenumber: r.data['employee']['mobile'],platform:platform,version:_version,lang:lang, uuid:device_str)));
      Navigator.push(
          context,
          MaterialPageRoute(
              builder: (_) => Relogin(
                  emp_id: r.data['employee']['employeeId'],
                  emp_phonenumber: r.data['employee']['mobile'],
                  platform: platform,
                  version: _version,
                  lang: lang)));
    } else if (login_range == "DEVICE") {
      // account with device (v2.5.0)
      Navigator.push(
          context,
          MaterialPageRoute(
              builder: (_) => DeviceRelogin(
                  emp_id: r.data['employee']['employeeId'],
                  emp_phonenumber: r.data['employee']['mobile'],
                  platform: platform,
                  version: _version,
                  lang: lang,
                  uuid: device_str,
                  freeLogin: _freeLogin,
                  encryptPwd: p)));
    } else {
      var responses = await EmployeeService().Getquestionnaire(2);
      responses!.data.map((e) {
        s.read('cyl').map((s) {
          if (s[0] == e["title"]) {
            s[2] = "报表||${e["url"]}";
          }
        }).toList();
        s.read('spl').map((s) {
          if (s[0] == e["title"]) {
            s[2] = "报表||${e["url"]}";
          }
        }).toList();
        s.read('top1').map((s) {
          if (s[0] == e["title"]) {
            s[2] = "报表||${e["url"]}";
          }
        }).toList();

        s.read('COVID_19').map((s) {
          if (s[0] == e["title"]) {
            s[2] = "报表||${e["url"]}";
          }
        }).toList();
        s.read('dsf').map((s) {
          if (s[0] == e["title"]) {
            s[2] = "报表||${e["url"]}";
          }
        }).toList();
        s.read('class_0').map((s) {
          if (s[0] == e["title"]) {
            s[2] = "报表||${e["url"]}";
          }
        }).toList();
        s.read('class_Other').map((s) {
          if (s[0] == e["title"]) {
            s[2] = "报表||${e["url"]}";
          }
        }).toList();
      }).toList();

      if (_freeLogin) {
        s.write("loginInfo",
            LoginInputDto(u, p, platform, _version, lang, device_str));
      } else {
        s.remove("loginInfo");
      }

      // 登陆成功之后的页面
      Navigator.of(context).pushReplacementNamed(RouteNames.indexPage);
    }
  }

  @override
  void dispose() {
    _usernameCtrl.dispose();
    _passwordCtrl.dispose();
    super.dispose();
  }
}

import 'package:amkor/components/tabbar_v2.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';
import '../components/routes.dart';
import '../components/tabbar.dart';

class AboutPage extends StatefulWidget {
  const AboutPage({Key? key}) : super(key: key);

  @override
  _AboutPageState createState() => _AboutPageState();
}

class _AboutPageState extends State<AboutPage> {

  @override
  void initState() {
    super.initState();

  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.dark);
    return Scaffold(
      appBar: TabberV2(
        title: "关于MSO",
        titleSize: 14,
        showBack: true,
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            children: [
              const Padding(padding:EdgeInsetsDirectional.all(6)),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: const [
                  Image(
                    image: AssetImage("assets/icons/qr_embedded.png"),
                    height: 38,
                    width: 38,
                  ),
                  Text("  MSO",style: TextStyle(fontWeight: FontWeight.w600,fontSize: 18),)
                ],
              ),
              const Padding(padding:EdgeInsetsDirectional.all(10),
                child: Text("MSO Version 1.1.3",style: TextStyle(fontWeight: FontWeight.w600,fontSize: 12,color: Colors.grey),),
              ),
              _buildMenus(),
            ],
          ),
          Column(
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children:  [
                  InkWell(
                      onTap: () {},
                      child:const Text("《用户协议》",style: TextStyle(fontSize: 12,color: Colors.blue),)
                  ),
                  InkWell(
                      onTap: () {},
                      child:const Text("《隐私政策》",style: TextStyle(fontSize: 12,color: Colors.blue),),
                  ),

                ],
              ),
              const Padding(
                  padding: EdgeInsetsDirectional.all(4),
                  child:Text("2022-2025",style: TextStyle(fontSize: 12,color: Colors.grey),),)

            ],
          )


        ],
      ),
    );
  }



  ///菜单列表
  Widget _buildMenus() {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.fromLTRB(12, 0, 12, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildMenuItem(MyMenuKeys.bbgx, "版本更新"),
          const Divider(
            height: 1,
          ),
          _buildMenuItem(MyMenuKeys.gnjs, "功能介绍"),
        ],
      ),
    );
  }

  ///退出登陆弹窗
  Future<void> _LogOutAppDialog(MyMenuKeys key)  {

    return showDialog<void>(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return CupertinoAlertDialog(
            title:  Row(
              children:const <Widget>[
                Image(
                  width: 34,
                  height: 34,
                  image: AssetImage("assets/icons/qr_embedded.png"),
                ),
                Padding(
                    padding:  EdgeInsets.fromLTRB(10.0, 0.0, 10.0, 0.0),
                    child:  Text("amkor"))
              ],
            ),
            content:const Text(
              "您正在退出app请确认。 ",style: TextStyle(fontSize: 14),),
            actions: <Widget>[
              CupertinoDialogAction(
                child:const Text("取消"),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
              CupertinoDialogAction(
                child:const Text("确定"),
                onPressed: () {
                  _navTo(key);
                  Fluttertoast.showToast(msg: "退出登录成功");


                },
              )
            ],
          );
        });

  }


  Widget _buildMenuItem(MyMenuKeys key, String desc) {
    return InkWell(
        onTap: () {

        },
        child:Container(
          key: UniqueKey(),
          padding: const EdgeInsets.only(top: 8, bottom: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                desc,
                style: const TextStyle(
                  fontSize: 14,
                ),
              ),
              const Icon(
                Icons.chevron_right,
                color: Color.fromRGBO(187, 187, 187, 1),
                size: 25,
              )
            ],
          ),
        )
    );
  }

  void _navTo(MyMenuKeys key) {
    switch (key) {
      case MyMenuKeys.gnjs:
        Navigator.of(context).pushNamed(RouteNames.personAddress);
        break;
      case MyMenuKeys.bbgx:
        Navigator.of(context).pushNamed(RouteNames.updatePwd);
        return;
    }
  }
}

enum MyMenuKeys {bbgx, gnjs,}

import 'package:flutter/material.dart';
import '../components/security_manager.dart';

/// 安全管理器使用示例
/// 展示如何在不同场景下使用 SecurityManager 替代 screenshot_callback
class SecurityUsageExample extends StatefulWidget {
  const SecurityUsageExample({Key? key}) : super(key: key);

  @override
  State<SecurityUsageExample> createState() => _SecurityUsageExampleState();
}

class _SecurityUsageExampleState extends State<SecurityUsageExample> {
  final SecurityManager _securityManager = SecurityManager.instance;

  @override
  void initState() {
    super.initState();
    _initializeSecurity();
  }

  Future<void> _initializeSecurity() async {
    // 初始化安全管理器
    await _securityManager.initialize();

    // 添加截屏监听器
    _securityManager.addScreenshotListener(() {
      _showScreenshotDetectedDialog();
    });
  }

  void _showScreenshotDetectedDialog() {
    if (mounted) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('截屏检测'),
          content: const Text('检测到截屏操作！'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('确定'),
            ),
          ],
        ),
      );
    }
  }

  @override
  void dispose() {
    // 清理监听器
    _securityManager.clearScreenshotListeners();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('安全管理器示例'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '安全设置',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            const Text(
              '注意：防截屏和后台隐藏功能需要通过 SecureApplication 组件在应用级别配置。\n'
              '请参考文档了解如何在 MaterialApp 中集成 SecureApplication。',
              style: TextStyle(fontSize: 14, color: Colors.orange),
            ),

            const SizedBox(height: 32),

            const Text(
              '测试功能',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // 手动触发截屏回调按钮
            ElevatedButton(
              onPressed: () {
                _securityManager.triggerScreenshotCallbacks();
              },
              child: const Text('模拟截屏事件'),
            ),

            const SizedBox(height: 16),

            // 使用 SecureWrapper 包装的敏感内容
            const Text(
              '敏感内容区域（使用 SecureWrapper 包装）:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),

            SecureWrapper(
              preventScreenshot: true,
              hideInBackground: true,
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red.shade100,
                  border: Border.all(color: Colors.red),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Column(
                  children: [
                    Text(
                      '🔒 敏感信息',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.red,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      '这个区域包含敏感信息，\n已启用防截屏和后台隐藏保护。',
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 32),

            const Text(
              '使用说明',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),

            const Text(
              '1. 防止截屏：启用后用户无法对应用进行截屏\n'
              '2. 后台隐藏：应用切换到后台时会隐藏内容\n'
              '3. 截屏检测：当检测到截屏时会触发回调\n'
              '4. SecureWrapper：可以包装特定组件进行保护',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }
}

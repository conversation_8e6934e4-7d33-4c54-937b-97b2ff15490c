import 'package:flutter/material.dart';

/// 单选按钮变色示例
/// 展示不同的红色变色方案
class RadioColorExample extends StatefulWidget {
  const RadioColorExample({Key? key}) : super(key: key);

  @override
  State<RadioColorExample> createState() => _RadioColorExampleState();
}

class _RadioColorExampleState extends State<RadioColorExample> {
  String selectedOption1 = 'Option 1';
  String selectedOption2 = 'Option A';
  String selectedOption3 = 'Choice 1';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('单选按钮变色示例'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 方法1：使用 activeColor 属性
            _buildSection(
              title: '方法1：使用 activeColor（推荐）',
              child: Column(
                children: [
                  RadioListTile<String>(
                    title: const Text('选项 1'),
                    value: 'Option 1',
                    groupValue: selectedOption1,
                    activeColor: Colors.red, // 选中时显示红色
                    onChanged: (value) {
                      setState(() {
                        selectedOption1 = value!;
                      });
                    },
                  ),
                  RadioListTile<String>(
                    title: const Text('选项 2'),
                    value: 'Option 2',
                    groupValue: selectedOption1,
                    activeColor: Colors.red, // 选中时显示红色
                    onChanged: (value) {
                      setState(() {
                        selectedOption1 = value!;
                      });
                    },
                  ),
                  RadioListTile<String>(
                    title: const Text('选项 3'),
                    value: 'Option 3',
                    groupValue: selectedOption1,
                    activeColor: Colors.red, // 选中时显示红色
                    onChanged: (value) {
                      setState(() {
                        selectedOption1 = value!;
                      });
                    },
                  ),
                ],
              ),
            ),

            const SizedBox(height: 20),

            // 方法2：使用 Theme 包装
            _buildSection(
              title: '方法2：使用 Theme 包装',
              child: Theme(
                data: Theme.of(context).copyWith(
                  unselectedWidgetColor: Colors.grey, // 未选中时的颜色
                ),
                child: Column(
                  children: [
                    RadioListTile<String>(
                      title: const Text('选择 A'),
                      value: 'Option A',
                      groupValue: selectedOption2,
                      activeColor: Colors.red, // 选中时显示红色
                      onChanged: (value) {
                        setState(() {
                          selectedOption2 = value!;
                        });
                      },
                    ),
                    RadioListTile<String>(
                      title: const Text('选择 B'),
                      value: 'Option B',
                      groupValue: selectedOption2,
                      activeColor: Colors.red, // 选中时显示红色
                      onChanged: (value) {
                        setState(() {
                          selectedOption2 = value!;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),

            // 方法3：自定义样式的单选按钮
            _buildSection(
              title: '方法3：自定义样式',
              child: Column(
                children: [
                  _buildCustomRadio('Choice 1', selectedOption3),
                  _buildCustomRadio('Choice 2', selectedOption3),
                  _buildCustomRadio('Choice 3', selectedOption3),
                ],
              ),
            ),

            const SizedBox(height: 20),

            // 方法4：不同的红色色调
            _buildSection(
              title: '方法4：不同红色色调',
              child: Column(
                children: [
                  Row(
                    children: [
                      Radio<String>(
                        value: 'Red1',
                        groupValue: 'Red1',
                        activeColor: Colors.red, // 标准红色
                        onChanged: (value) {},
                      ),
                      const Text('标准红色'),
                    ],
                  ),
                  Row(
                    children: [
                      Radio<String>(
                        value: 'Red2',
                        groupValue: 'Red2',
                        activeColor: Colors.red.shade700, // 深红色
                        onChanged: (value) {},
                      ),
                      const Text('深红色'),
                    ],
                  ),
                  Row(
                    children: [
                      Radio<String>(
                        value: 'Red3',
                        groupValue: 'Red3',
                        activeColor: Colors.redAccent, // 红色强调色
                        onChanged: (value) {},
                      ),
                      const Text('红色强调色'),
                    ],
                  ),
                  Row(
                    children: [
                      Radio<String>(
                        value: 'Red4',
                        groupValue: 'Red4',
                        activeColor: const Color(0xFFE53E3E), // 自定义红色
                        onChanged: (value) {},
                      ),
                      const Text('自定义红色'),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection({required String title, required Widget child}) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            const SizedBox(height: 12),
            child,
          ],
        ),
      ),
    );
  }

  Widget _buildCustomRadio(String value, String groupValue) {
    bool isSelected = value == groupValue;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          selectedOption3 = value;
        });
      },
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 4),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected ? Colors.red.shade50 : Colors.transparent,
          border: Border.all(
            color: isSelected ? Colors.red : Colors.grey.shade300,
            width: 2,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: isSelected ? Colors.red : Colors.grey,
                  width: 2,
                ),
                color: isSelected ? Colors.red : Colors.transparent,
              ),
              child: isSelected
                  ? const Icon(
                      Icons.circle,
                      size: 12,
                      color: Colors.white,
                    )
                  : null,
            ),
            const SizedBox(width: 12),
            Text(
              value,
              style: TextStyle(
                color: isSelected ? Colors.red : Colors.black,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

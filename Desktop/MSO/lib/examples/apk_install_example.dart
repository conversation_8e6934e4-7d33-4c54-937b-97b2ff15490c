import 'package:flutter/material.dart';
import '../utils/apk_installer.dart';

/// APK安装功能使用示例
/// 
/// 这个示例展示了如何使用优化后的APK安装工具
/// 避免使用敏感的MANAGE_EXTERNAL_STORAGE权限
class ApkInstallExample extends StatefulWidget {
  const ApkInstallExample({Key? key}) : super(key: key);

  @override
  State<ApkInstallExample> createState() => _ApkInstallExampleState();
}

class _ApkInstallExampleState extends State<ApkInstallExample> {
  double _downloadProgress = 0.0;
  bool _isDownloading = false;
  String _statusMessage = '准备下载...';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('APK安装示例'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 权限说明卡片
            _buildPermissionInfoCard(),
            
            const SizedBox(height: 20),
            
            // 下载进度显示
            _buildProgressSection(),
            
            const SizedBox(height: 20),
            
            // 操作按钮
            _buildActionButtons(),
            
            const SizedBox(height: 20),
            
            // 使用说明
            _buildUsageInstructions(),
          ],
        ),
      ),
    );
  }

  /// 权限说明卡片
  Widget _buildPermissionInfoCard() {
    return Card(
      color: Colors.blue.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: Colors.blue.shade700),
                const SizedBox(width: 8),
                Text(
                  '权限说明',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            const Text(
              '• 只需要"应用安装"权限 (REQUEST_INSTALL_PACKAGES)\n'
              '• 不需要敏感的"管理外部存储"权限\n'
              '• APK文件存储在应用私有目录中\n'
              '• 符合Google Play政策要求',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  /// 下载进度显示区域
  Widget _buildProgressSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '下载状态',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade700,
              ),
            ),
            const SizedBox(height: 12),
            
            // 进度条
            LinearProgressIndicator(
              value: _downloadProgress,
              backgroundColor: Colors.grey.shade300,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
            ),
            
            const SizedBox(height: 8),
            
            // 状态文本
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(_statusMessage),
                Text('${(_downloadProgress * 100).toStringAsFixed(1)}%'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 操作按钮区域
  Widget _buildActionButtons() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // 下载并安装按钮
        ElevatedButton.icon(
          onPressed: _isDownloading ? null : _downloadAndInstallApk,
          icon: _isDownloading 
            ? const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            : const Icon(Icons.download),
          label: Text(_isDownloading ? '下载中...' : '下载并安装APK'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 12),
          ),
        ),
        
        const SizedBox(height: 12),
        
        // 安装本地文件按钮
        OutlinedButton.icon(
          onPressed: _isDownloading ? null : _installLocalApk,
          icon: const Icon(Icons.install_mobile),
          label: const Text('安装本地APK文件'),
          style: OutlinedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 12),
          ),
        ),
        
        const SizedBox(height: 12),
        
        // 清理文件按钮
        TextButton.icon(
          onPressed: _cleanupFiles,
          icon: const Icon(Icons.cleaning_services),
          label: const Text('清理下载的APK文件'),
        ),
      ],
    );
  }

  /// 使用说明
  Widget _buildUsageInstructions() {
    return Card(
      color: Colors.green.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.lightbulb_outline, color: Colors.green.shade700),
                const SizedBox(width: 8),
                Text(
                  '使用建议',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.green.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            const Text(
              '1. 首次使用时会自动请求应用安装权限\n'
              '2. 如果权限被拒绝，可以引导用户到设置中开启\n'
              '3. 下载完成后会自动打开系统安装界面\n'
              '4. 建议定期清理下载的APK文件以节省空间',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  /// 下载并安装APK
  Future<void> _downloadAndInstallApk() async {
    setState(() {
      _isDownloading = true;
      _downloadProgress = 0.0;
      _statusMessage = '开始下载...';
    });

    // 示例APK下载链接（请替换为实际的APK下载链接）
    const apkUrl = 'https://example.com/app-release.apk';

    final success = await ApkInstaller.downloadAndInstallApk(
      url: apkUrl,
      context: context,
      onProgress: (received, total) {
        setState(() {
          _downloadProgress = received / total;
          _statusMessage = '下载中... ${(received / 1024 / 1024).toStringAsFixed(1)}MB / ${(total / 1024 / 1024).toStringAsFixed(1)}MB';
        });
      },
      fileName: 'my_app_update.apk',
    );

    setState(() {
      _isDownloading = false;
      if (success) {
        _statusMessage = '下载完成，正在安装...';
        _downloadProgress = 1.0;
      } else {
        _statusMessage = '下载失败';
        _downloadProgress = 0.0;
      }
    });

    if (success) {
      _showSuccessMessage('APK下载完成，请按照系统提示完成安装。');
    }
  }

  /// 安装本地APK文件
  Future<void> _installLocalApk() async {
    // 这里应该实现文件选择逻辑
    // 为了示例，我们假设有一个固定的文件路径
    const localApkPath = '/path/to/local/app.apk';

    final success = await ApkInstaller.installLocalApk(
      filePath: localApkPath,
      context: context,
    );

    if (success) {
      _showSuccessMessage('正在安装APK文件，请按照系统提示完成安装。');
    }
  }

  /// 清理下载的文件
  Future<void> _cleanupFiles() async {
    await ApkInstaller.cleanupDownloadedApks();
    _showSuccessMessage('已清理下载的APK文件。');
    
    setState(() {
      _downloadProgress = 0.0;
      _statusMessage = '准备下载...';
    });
  }

  /// 显示成功消息
  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }
}

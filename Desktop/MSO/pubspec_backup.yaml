name: amkor
description: MSO

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 2.5.0+1

environment:
  sdk: ">=3.2.0 <4.0.0"
  flutter: ">=3.24.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  # IOS 设备ID
  flutter_keychain: ^2.5.0
  #  屏幕自动调亮
  screen_brightness: ^1.0.1
  cupertino_icons: ^1.0.8
  outline_search_bar: ^2.3.0
  qr_flutter: ^4.1.0
  qr_code_scanner: ^1.0.1
  flutter_launcher_icons: ^0.13.1
  dio: ^4.0.6
  get_storage: ^2.0.3
  fluttertoast: ^8.0.9
  flutter_animated_dialog: ^2.0.1
  crypto: ^3.0.2
  encrypt: ^5.0.1
  rounded_loading_button: ^2.1.0
  flutter_easyloading: ^3.0.5
#  插件已更新到package_info_plus
  package_info_plus: ^8.0.0
  flutter_picker: ^2.0.3
  # date_time_picker有intl版本冲突，暂时移除或使用替代方案
  # date_time_picker: ^2.1.0
  # 使用Flutter内置的日期选择器或其他替代方案
  image_picker: ^0.8.5+3
  http: ^0.13.4
  url_launcher: ^6.1.4
  path_provider: ^2.0.11
  get: ^4.3.8
  open_file: ^3.2.1

  webview_flutter: ^3.0.4
  flutter_native_image: ^0.0.6+1
#  图片压缩
  flutter_image_compress: ^1.1.3
  device_info_plus: ^4.2.1
  mobile_scanner: ^2.0.0


  permission_handler: ^10.2.0

  jpush_flutter: ^2.3.4
  flutter_udid: ^2.0.0
#  pinput: 2.2.18
  pinput: ^2.2.18
  universal_html: ^2.0.8
#  生物识别
#  local_auth: ^2.1.2
#  包名跳
  external_app_launcher: ^3.1.0
#  shared_preferences: ^2.0.15
  flutter_html: ^2.2.1
  pull_to_refresh: ^2.0.0
#  图片预览
  photo_view: ^0.14.0
#  本地通知
  flutter_local_notifications: ^12.0.2
  flutter_staggered_animations: ^1.1.1
  device_information: ^0.0.4
#  禁止截屏
  secure_application: ^3.8.0
#  高德地图
#  amap_flutter_map: ^3.0.0
#  amap_flutter_location: ^3.0.0
#  amap_search_fluttify: ^0.18.0
#  location: ^4.4.0
  sqflite: ^2.0.3+1
#  network_info_plus: 2.0.2
  badges: ^3.1.1
#  flutter_app_badger: ^1.5.0

  web_socket_channel: ^2.4.0

  mqtt_client: ^9.6.1


#  screenshot_callback: ^2.0.0
#  screenshot: ^1.3.0
dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  assets:
    - assets/images/
    - assets/icons/
    - assets/icons/nav/
    - assets/icons/news/
  fonts:
    - family: MyIcons
      fonts:
        - asset: assets/fonts/icomoon.ttf
          weight: 600
  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

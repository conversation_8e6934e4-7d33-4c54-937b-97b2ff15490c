name: mso
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 2.5.0+1

environment:
  sdk: ">=3.0.0 <4.0.0"
  flutter: ">=3.24.0"

dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6

  # 核心网络和存储
  dio: ^4.0.6
  get_storage: ^2.0.3
  http: ^0.13.5

  # UI组件
  fluttertoast: ^8.0.9
  flutter_animated_dialog: ^2.0.1
  rounded_loading_button: ^2.1.0
  flutter_easyloading: ^3.0.5
  outline_search_bar: ^2.3.0
  pull_to_refresh: ^2.0.0
  flutter_staggered_animations: ^1.1.1
  photo_view: ^0.14.0
  badges: ^3.1.1

  # 二维码相关
  qr_flutter: ^4.0.0
  qr_code_scanner: ^1.0.0
  mobile_scanner: ^2.0.0

  # 文件和媒体
  image_picker: ^0.8.6
  flutter_native_image: ^0.0.6+1
  flutter_image_compress: ^1.1.3
  open_file: ^3.2.1
  path_provider: ^2.0.11

  # WebView
  webview_flutter: ^3.0.4

  # 设备信息和权限
  device_info_plus: ^4.2.1
  permission_handler: ^10.2.0
  flutter_keychain: ^2.5.0
  screen_brightness: ^0.2.2+1

  # 加密和安全
  crypto: ^3.0.2
  encrypt: ^5.0.1
  secure_application: ^3.8.0

  # 导航和URL
  url_launcher: ^6.1.5
  get: ^4.6.5
  external_app_launcher: ^3.1.0

  # 输入组件
  pinput: ^2.2.18
  flutter_picker: ^2.0.3

  # 推送和通知
  jpush_flutter: ^2.3.4
  flutter_local_notifications: ^12.0.2

  # 设备标识
  flutter_udid: ^2.0.0
  device_information: ^0.0.4

  # HTML渲染
  flutter_html: ^2.2.1

  # 网络通信
  web_socket_channel: ^2.4.0
  mqtt_client: ^9.6.1

  # 数据库
  sqflite: ^2.0.3+1

  # 其他工具
  universal_html: ^2.0.8
  package_info_plus: ^4.2.0
  flutter_launcher_icons: ^0.10.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/
    - assets/
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-variant files by
  # listing the main asset with all of its variants:
  # assets:
  #   - images/a_dot_burrito.jpeg
  #   - images/<EMAIL>
  #   - images/<EMAIL>

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this format:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

flutter_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/logo.png"
  min_sdk_android: 21 # android min sdk min:16, default 21
  web:
    generate: true
    image_path: "assets/images/logo.png"
    background_color: "#hexcode"
    theme_color: "#hexcode"
  windows:
    generate: true
    image_path: "assets/images/logo.png"
    icon_size: 48 # min:48, max:256, default: 48

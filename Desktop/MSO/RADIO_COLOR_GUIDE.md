# 单选按钮变色指南

## 快速解决方案

你的代码已经修复！现在单选按钮选中时会显示红色：

```dart
Radio<String>(
  value: 'Option 1',
  groupValue: 'Option 1',
  activeColor: Colors.red, // 选中时显示红色
  onChanged: (value) {
    // 这里可以添加状态更新逻辑
  },
)
```

## 不同的红色变色方案

### 1. 基础方案：使用 activeColor（推荐）

```dart
Radio<String>(
  value: 'Option 1',
  groupValue: selectedValue,
  activeColor: Colors.red, // 选中时显示红色
  onChanged: (value) {
    setState(() {
      selectedValue = value!;
    });
  },
)
```

### 2. 使用 RadioListTile（更完整的UI）

```dart
RadioListTile<String>(
  title: Text('选项 1'),
  value: 'Option 1',
  groupValue: selectedValue,
  activeColor: Colors.red, // 选中时显示红色
  onChanged: (value) {
    setState(() {
      selectedValue = value!;
    });
  },
)
```

### 3. 不同的红色色调

```dart
// 标准红色
activeColor: Colors.red

// 深红色
activeColor: Colors.red.shade700

// 红色强调色
activeColor: Colors.redAccent

// 自定义红色
activeColor: Color(0xFFE53E3E)
```

### 4. 使用 Theme 包装（全局设置）

```dart
Theme(
  data: Theme.of(context).copyWith(
    unselectedWidgetColor: Colors.grey, // 未选中时的颜色
  ),
  child: Radio<String>(
    value: 'Option 1',
    groupValue: selectedValue,
    activeColor: Colors.red, // 选中时显示红色
    onChanged: (value) {
      setState(() {
        selectedValue = value!;
      });
    },
  ),
)
```

## 完整的状态管理示例

```dart
class MyRadioWidget extends StatefulWidget {
  @override
  _MyRadioWidgetState createState() => _MyRadioWidgetState();
}

class _MyRadioWidgetState extends State<MyRadioWidget> {
  String selectedOption = 'Option 1'; // 当前选中的值

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        RadioListTile<String>(
          title: Text('选项 1'),
          value: 'Option 1',
          groupValue: selectedOption,
          activeColor: Colors.red, // 选中时显示红色
          onChanged: (value) {
            setState(() {
              selectedOption = value!;
            });
          },
        ),
        RadioListTile<String>(
          title: Text('选项 2'),
          value: 'Option 2',
          groupValue: selectedOption,
          activeColor: Colors.red, // 选中时显示红色
          onChanged: (value) {
            setState(() {
              selectedOption = value!;
            });
          },
        ),
        RadioListTile<String>(
          title: Text('选项 3'),
          value: 'Option 3',
          groupValue: selectedOption,
          activeColor: Colors.red, // 选中时显示红色
          onChanged: (value) {
            setState(() {
              selectedOption = value!;
            });
          },
        ),
      ],
    );
  }
}
```

## 自定义样式的单选按钮

如果你想要更多的自定义控制：

```dart
Widget buildCustomRadio(String value, String groupValue) {
  bool isSelected = value == groupValue;
  
  return GestureDetector(
    onTap: () {
      setState(() {
        selectedValue = value;
      });
    },
    child: Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isSelected ? Colors.red.shade50 : Colors.transparent,
        border: Border.all(
          color: isSelected ? Colors.red : Colors.grey.shade300,
          width: 2,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: isSelected ? Colors.red : Colors.grey,
                width: 2,
              ),
              color: isSelected ? Colors.red : Colors.transparent,
            ),
            child: isSelected
                ? Icon(Icons.circle, size: 12, color: Colors.white)
                : null,
          ),
          SizedBox(width: 12),
          Text(
            value,
            style: TextStyle(
              color: isSelected ? Colors.red : Colors.black,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ],
      ),
    ),
  );
}
```

## 常用的红色值

```dart
// Flutter 内置红色
Colors.red                    // 标准红色
Colors.red.shade300          // 浅红色
Colors.red.shade700          // 深红色
Colors.redAccent             // 红色强调色

// 自定义红色
Color(0xFFE53E3E)            // 自定义红色
Color.fromRGBO(229, 62, 62, 1.0)  // RGB红色
```

## 注意事项

1. **状态管理**：确保使用 `setState()` 来更新选中状态
2. **groupValue**：所有相关的 Radio 组件应该使用相同的 `groupValue`
3. **value**：每个 Radio 组件的 `value` 应该是唯一的
4. **onChanged**：必须实现 `onChanged` 回调来处理状态变化

## 测试你的实现

运行应用后，你应该看到：
- 选中的单选按钮显示红色
- 未选中的单选按钮显示默认颜色（通常是灰色）
- 点击不同选项时，红色会跟随选中状态移动

你的代码现在已经正确设置了 `activeColor: Colors.red`，所以选中时会显示红色！

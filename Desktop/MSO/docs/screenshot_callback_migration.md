# screenshot_callback 插件迁移指南

## 问题背景

`screenshot_callback` 插件在 Flutter 3.24.6 版本中存在兼容性问题，无法正常编译和使用。为了解决这个问题，我们采用了 `secure_application` 插件作为替代方案。

## 替代方案对比

### 原方案：screenshot_callback
```yaml
dependencies:
  screenshot_callback: ^1.0.0  # 已禁用
```

### 新方案：secure_application + SecurityManager
```yaml
dependencies:
  secure_application: ^3.8.0  # 新增
```

## 功能对比

| 功能 | screenshot_callback | secure_application + SecurityManager |
|------|-------------------|-----------------------------------|
| 截屏检测 | ✅ | ✅ (通过自定义实现) |
| 防止截屏 | ❌ | ✅ |
| 后台隐藏 | ❌ | ✅ |
| iOS 支持 | ✅ | ✅ |
| Android 支持 | ✅ | ✅ |
| Flutter 3.24+ 兼容 | ❌ | ✅ |

## 迁移步骤

### 1. 更新依赖

在 `pubspec.yaml` 中：
```yaml
dependencies:
  # screenshot_callback: ^1.0.0  # 移除或注释
  secure_application: ^3.8.0     # 新增
```

### 2. 导入新的安全管理器

```dart
// 旧的导入
// import 'package:screenshot_callback/screenshot_callback.dart';

// 新的导入
import '../components/security_manager.dart';
```

### 3. 替换初始化代码

#### 旧代码：
```dart
ScreenshotCallback screenshotCallback = ScreenshotCallback();

Future<void> initScreenshotCallback() async {
  screenshotCallback.addListener(() async {
    // 截屏处理逻辑
  });
}
```

#### 新代码：
```dart
final SecurityManager _securityManager = SecurityManager.instance;

Future<void> initScreenshotCallback() async {
  // 初始化安全管理器
  await _securityManager.initialize(
    preventScreenshot: false, // 是否防止截屏
    hideInBackground: true,   // 是否后台隐藏
  );
  
  // 添加截屏监听器
  _securityManager.addScreenshotListener(() async {
    // 截屏处理逻辑
  });
}
```

### 4. 更新 dispose 方法

```dart
@override
void dispose() {
  // 清理安全管理器的监听器
  _securityManager.clearScreenshotListeners();
  super.dispose();
}
```

## 新增功能

### 1. 防止截屏
```dart
// 启用防截屏
await _securityManager.setPreventScreenshot(true);

// 禁用防截屏
await _securityManager.setPreventScreenshot(false);
```

### 2. 后台隐藏
```dart
// 启用后台隐藏
await _securityManager.setHideInBackground(true);

// 禁用后台隐藏
await _securityManager.setHideInBackground(false);
```

### 3. 安全包装器
对于需要特殊保护的页面或组件，可以使用 `SecureWrapper`：

```dart
SecureWrapper(
  preventScreenshot: true,
  hideInBackground: true,
  child: YourSensitiveWidget(),
)
```

## 使用示例

### 基本用法
```dart
class MySecurePage extends StatefulWidget {
  @override
  State<MySecurePage> createState() => _MySecurePageState();
}

class _MySecurePageState extends State<MySecurePage> {
  final SecurityManager _securityManager = SecurityManager.instance;

  @override
  void initState() {
    super.initState();
    _initializeSecurity();
  }

  Future<void> _initializeSecurity() async {
    await _securityManager.initialize(
      preventScreenshot: true,  // 防止截屏
      hideInBackground: true,   // 后台隐藏
    );

    _securityManager.addScreenshotListener(() {
      // 处理截屏事件
      print('检测到截屏！');
    });
  }

  @override
  void dispose() {
    _securityManager.clearScreenshotListeners();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('安全页面')),
      body: Center(
        child: Text('这是一个受保护的页面'),
      ),
    );
  }
}
```

## 注意事项

1. **权限要求**：`secure_application` 可能需要额外的权限配置
2. **平台差异**：不同平台的安全功能实现可能有差异
3. **性能影响**：启用安全功能可能对性能有轻微影响
4. **测试**：建议在真机上测试安全功能的效果

## 故障排除

### 1. 编译错误
如果遇到编译错误，请确保：
- Flutter 版本为 3.24.6 或更高
- 已正确添加 `secure_application` 依赖
- 已移除或注释 `screenshot_callback` 相关代码

### 2. 功能不生效
如果安全功能不生效：
- 检查是否正确调用了 `initialize()` 方法
- 确认在真机上测试（模拟器可能不支持某些安全功能）
- 检查平台特定的权限配置

### 3. 性能问题
如果遇到性能问题：
- 考虑只在需要的页面启用安全功能
- 使用 `SecureWrapper` 包装特定组件而不是整个应用

## 总结

通过使用 `secure_application` 插件和自定义的 `SecurityManager`，我们不仅解决了 `screenshot_callback` 的兼容性问题，还获得了更强大的安全功能。新方案提供了更好的 Flutter 3.24+ 兼容性和更丰富的安全选项。

PODS:
  - device_info_plus_macos (0.0.1):
    - FlutterMacOS
  - file_selector_macos (0.0.1):
    - FlutterMacOS
  - flutter_local_notifications (0.0.1):
    - FlutterMacOS
  - flutter_udid (0.0.1):
    - FlutterMacOS
    - SAMKeychain
  - FlutterMacOS (1.0.0)
  - FMDB (2.7.10):
    - FMDB/standard (= 2.7.10)
  - FMDB/standard (2.7.10)
  - mobile_scanner (0.0.1):
    - FlutterMacOS
  - package_info (0.0.1):
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - SAMKeychain (1.5.3)
  - screen_brightness_macos (0.1.0):
    - FlutterMacOS
  - sqflite (0.0.2):
    - FlutterMacOS
    - FMDB (>= 2.7.5)
  - url_launcher_macos (0.0.1):
    - FlutterMacOS
  - wakelock_macos (0.0.1):
    - FlutterMacOS

DEPENDENCIES:
  - device_info_plus_macos (from `Flutter/ephemeral/.symlinks/plugins/device_info_plus_macos/macos`)
  - file_selector_macos (from `Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos`)
  - flutter_local_notifications (from `Flutter/ephemeral/.symlinks/plugins/flutter_local_notifications/macos`)
  - flutter_udid (from `Flutter/ephemeral/.symlinks/plugins/flutter_udid/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - mobile_scanner (from `Flutter/ephemeral/.symlinks/plugins/mobile_scanner/macos`)
  - package_info (from `Flutter/ephemeral/.symlinks/plugins/package_info/macos`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/macos`)
  - screen_brightness_macos (from `Flutter/ephemeral/.symlinks/plugins/screen_brightness_macos/macos`)
  - sqflite (from `Flutter/ephemeral/.symlinks/plugins/sqflite/macos`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)
  - wakelock_macos (from `Flutter/ephemeral/.symlinks/plugins/wakelock_macos/macos`)

SPEC REPOS:
  trunk:
    - FMDB
    - SAMKeychain

EXTERNAL SOURCES:
  device_info_plus_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/device_info_plus_macos/macos
  file_selector_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos
  flutter_local_notifications:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_local_notifications/macos
  flutter_udid:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_udid/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  mobile_scanner:
    :path: Flutter/ephemeral/.symlinks/plugins/mobile_scanner/macos
  package_info:
    :path: Flutter/ephemeral/.symlinks/plugins/package_info/macos
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/macos
  screen_brightness_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/screen_brightness_macos/macos
  sqflite:
    :path: Flutter/ephemeral/.symlinks/plugins/sqflite/macos
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos
  wakelock_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/wakelock_macos/macos

SPEC CHECKSUMS:
  device_info_plus_macos: 1ad388a1ef433505c4038e7dd9605aadd1e2e9c7
  file_selector_macos: 468fb6b81fac7c0e88d71317f3eec34c3b008ff9
  flutter_local_notifications: 3805ca215b2fb7f397d78b66db91f6a747af52e4
  flutter_udid: 6b2b89780c3dfeecf0047bdf93f622d6416b1c07
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  FMDB: eae540775bf7d0c87a5af926ae37af69effe5a19
  mobile_scanner: 35dc92ffdbd7934b0dbc411b1c731bc2ef23c2dc
  package_info: 6eba2fd8d3371dda2d85c8db6fe97488f24b74b2
  path_provider_foundation: 29f094ae23ebbca9d3d0cec13889cd9060c0e943
  SAMKeychain: 483e1c9f32984d50ca961e26818a534283b4cd5c
  screen_brightness_macos: 2d6d3af2165592d9a55ffcd95b7550970e41ebda
  sqflite: a5789cceda41d54d23f31d6de539d65bb14100ea
  url_launcher_macos: d2691c7dd33ed713bf3544850a623080ec693d95
  wakelock_macos: bc3f2a9bd8d2e6c89fee1e1822e7ddac3bd004a9

PODFILE CHECKSUM: 353c8bcc5d5b0994e508d035b5431cfe18c1dea7

COCOAPODS: 1.15.2

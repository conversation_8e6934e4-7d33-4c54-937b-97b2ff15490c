<!--<manifest xmlns:android="http://schemas.android.com/apk/res/android" package="com.amkor.amkor">-->
<manifest xmlns:android="http://schemas.android.com/apk/res/android" package="com.amkor.test">
<!--  极光推送需要传入字符串  ${applicationName}替换为了 android.app.Application-->
    <application android:label="MSO Test" android:name="android.app.Application" android:usesCleartextTraffic="true" android:icon="@mipmap/ic_launcher"  android:resizeableActivity="false" android:requestLegacyExternalStorage="true">
<!--    <application android:label="MSO Test" android:name="android.app.Application" android:usesCleartextTraffic="true" android:icon="@mipmap/ic_launcher"  android:resizeableActivity="false">-->
        <activity android:name=".MainActivity" android:exported="true" android:launchMode="singleTop" android:theme="@style/LaunchTheme" android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode" android:hardwareAccelerated="true" android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data android:name="io.flutter.embedding.android.NormalTheme" android:resource="@style/NormalTheme" />
            <meta-data android:name="android.notch_support" android:value="true" />
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

        </activity>
        <receiver android:name=".MyBroadcastReceiver"  android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED"/>
                <action android:name="android.intent.action.INPUT_METHOD_CHANGED" />
                <action android:name="com.example.broadcasttest.MY_BROADCAST"/>
            </intent-filter>
        </receiver>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data android:name="flutterEmbedding" android:value="2" />
        <provider
         android:name="androidx.core.content.FileProvider"
         android:authorities="${applicationId}.fileprovider"
        android:exported="false"
        android:grantUriPermissions="true">
        <meta-data
         android:name="android.support.FILE_PROVIDER_PATHS"
         android:resource="@xml/file_paths" />
        </provider>



    </application>
    //android9.0以上保活需要该权限
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

    <!-- 传统存储权限 (Android 10及以下) -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="29" />

    <!-- Android 13+ 媒体权限 -->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />

    <!-- 通知权限 -->
    <uses-permission android:name="android.permission.RECEIVE_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <!-- APK安装相关权限 -->
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />

    <!-- 注意：MANAGE_EXTERNAL_STORAGE是敏感权限，Google Play审核严格 -->
    <!-- 对于APK安装，推荐使用应用私有目录，无需此权限 -->
    <!-- <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" /> -->

    <!-- 其他权限 -->
<!--    <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS" />-->
<!--    <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS" />-->
<!--    <uses-permission android:name="android.permission.ACCESS_MEDIA_LOCATION" />-->
</manifest>
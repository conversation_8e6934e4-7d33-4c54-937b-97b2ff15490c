// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:device_info_plus/device_info_plus.dart';

import 'package:amkor/main.dart';

void main() {
  runApp(MyApp());

  // testWidgets('Counter increments smoke test', (WidgetTester tester) async {
  //   // Build our app and trigger a frame.
  //   await tester.pumpWidget( MyApp());
  //
  //   // Verify that our counter starts at 0.
  //   expect(find.text('0'), findsOneWidget);
  //   expect(find.text('1'), findsNothing);
  //
  //   // Tap the '+' icon and trigger a frame.
  //   await tester.tap(find.byIcon(Icons.add));
  //   await tester.pump();
  //
  //   // Verify that our counter has incremented.
  //   expect(find.text('0'), findsNothing);
  //   expect(find.text('1'), findsOneWidget);
  // });
}


class MyApp extends StatefulWidget {
  @override
  _MyAppState createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  String _deviceInfo = 'Loading...';

  Future<void> _getDeviceInfo() async {
    // final info = await DeviceInfoPlugin().androidInfo; // 获取Android信息
    // final iosInfo = await DeviceInfoPlugin().iosInfo; // 获取iOS信息
    final info = Platform.isAndroid ? await DeviceInfoPlugin().androidInfo: await DeviceInfoPlugin().iosInfo;
    print('Device info:\n${info.toString()}');
    setState((){
      _deviceInfo = 'Device info:\n${info.toString()}';
    });

    // // 根据平台选择合适的信息
    // if (Platform.isAndroid) {
    //   setState(() {
    //     _deviceInfo = 'Android Info:\n${info.toString()}';
    //   });
    // } else if (Platform.isIOS) {
    //   setState(() {
    //     _deviceInfo = 'iOS Info:\n${iosInfo.toString()}';
    //   });
    // }
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        appBar: AppBar(title: const Text('Device Info')),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Text(_deviceInfo),
              ElevatedButton(
                onPressed: _getDeviceInfo,
                child: const Text('Get Device Info'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

